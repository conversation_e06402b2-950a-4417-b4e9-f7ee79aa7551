# 1.1 Introduction and Objectives

## 1.1 Introduction and objectives

The European Union is at the forefront of the global effort to combat climate change, with the ambitious goal of achieving climate neutrality by 2050 as outlined in the European Green Deal. Carbon Capture, Utilisation, and Storage (CUS) is recognized by the Intergovernmental Panel on Climate Change (IPCC) and the European Commission as a critical component in the portfolio of mitigation actions to provide a strategic bridge away from fossil fuels and decarbonize hard-to-abate industries. To achieve the necessary scale, Europe must develop a robust portfolio of geological storage sites. For the EU, developing a robust CUS infrastructure, particularly for onshore geological storage, is not just an environmental necessity but a strategic one. It offers increased flexibility, reduced costs, and the ability for Member States to manage their decarbonisation strategies at a territorial level, thereby enhancing energy security and fostering local economic activity.

However, the success of onshore CUS hinges on one overriding factor: public acceptance, which is contingent on demonstrating uncompromising long-term safety and storage permanence. When CO2 is injected deep underground into saline aquifers or depleted hydrocarbon reservoirs, it exists in a supercritical state. Over time, this supercritical CO2 interacts with the surrounding rock and brine, triggering a cascade of coupled Hydro-Chemo-Mechanical (HCM) processes. These interactions, involving mineral dissolution and precipitation, can alter the fundamental properties of the reservoir rock including its porosity, permeability, and mechanical strength. These alterations, in turn, can affect the integrity of the caprock seal, and potentially create new pathways for CO2 to leak back into the atmosphere. Furthermore, the dual-purpose application of CO2 injection, for both storage and as in Enhanced Geothermal Systems (EGS), introduces additional complexities. While CO2 offers thermodynamic advantages over water in EGS, the long-term chemo-mechanical consequences of cycling CO2 in these systems are largely unknown.

Current computational models often struggle to capture the full complexity of these coupled multi-physics phenomena over the vast timescales (e.g. across decadal, centennial, and millennial timescales) relevant for geological storage. They either oversimplify the chemical-mechanical couplings or are too computationally expensive to be used for the large number of simulations required for robust uncertainty quantification and risk assessment. This represents a critical knowledge gap that hinders our ability to confidently select, operate, and monitor CUS and EGS sites. This project is directly motivated by the need to fill this gap by developing a next-generation computational framework capable of accurately and efficiently predicting the long-term fate of stored CO2, thereby paving the way for the safe and widespread deployment of CUS technologies across Europe.

**Overall Objective:**
The overall objective of this project is to develop a novel, robust, and computationally efficient modeling framework to predict the long-term multi-physical evolution of geological reservoirs subjected to CO2 injection for both storage and Enhanced Geothermal Systems (EGS) applications. This ambitious goal will be achieved through the creation of a modular, staggered simulation scheme that integrates high-fidelity models for the key physical processes governing reservoir behaviour. The framework will be built upon a flexible, open-source platform, allowing for the seamless coupling of different physical modules to address the specific challenges of CUS and EGS.

A cornerstone of this project will be the use of a novel staggered scheme to model the complex chemo-hydro-mechanical interactions and fracture propagation within the reservoir. This cutting-edge technique is particularly well-suited for capturing the intricate interplay between chemical reactions (dissolution and precipitation) and mechanical deformation, which is crucial for assessing the long-term integrity of the caprock and the risk of induced fracturing.

Furthermore, the project recognizes that high-fidelity simulations are often too slow for the extensive parametric studies needed for risk assessment and optimization. To overcome this limitation, this project will develop fast and accurate surrogate models from the simulation data. These surrogate models will capture the essential dynamics of the complex HCM system at a fraction of the computational cost, enabling comprehensive uncertainty quantification and robust risk analysis. This project will deliver a transformative tool for the design, operation, and monitoring of CUS and EGS projects, ultimately contributing to their safe and efficient deployment as key technologies in the fight against climate change.

**Specific Objectives**
To achieve the overall objective, this project is structured around three specific, interconnected objectives (SOs):

**SO1:** To develop a high-fidelity staggered hydro-chemo-mechanical (HCM) numerical framework for fractured porous media. This objective focuses on the creation of a computationally efficient simulation tool that integrates phase-field methods for hydro-mechanical coupling with stabilized finite element methods for reactive transport. The framework will employ a phase-field approach to solve the coupled displacement and pore pressure fields, capturing the complex interactions between mechanical deformation and fluid flow in fractured media. For the reactive transport problem, stabilized finite element methods such as Taylor-Galerkin or Streamline-Upwind Petrov-Galerkin (SUPG) will be implemented to handle advection-dominated transport phenomena while maintaining numerical stability. Adaptive mesh refinement techniques will be incorporated to enhance computational efficiency by concentrating computational resources in regions of high gradients or evolving interfaces. To efficiently handle the complex geochemical reactions, surrogate models will be trained using the IPhreeqc library, enabling rapid calculation of reaction rates without the computational overhead of full geochemical equilibrium calculations. The successful completion of this objective will result in a novel and computationally efficient simulation platform capable of capturing the complex, coupled physics of CUS and EGS with high fidelity.

**SO2:** To develop advanced surrogate modeling techniques for fast simulation and risk assessment. This objective will leverage the high-fidelity simulator developed in SO1 to generate comprehensive time-series datasets through batch simulations using high-performance computing (HPC) techniques. The generated datasets will capture the full range of system behavior under various operating conditions, geological parameters, and injection scenarios. Advanced machine learning algorithms, including Interface-Aware U-Net-ConvLSTM and Interface-Aware Vision Transformers (ViTs), will be employed to train sophisticated surrogate models that can predict the spatiotemporal evolution of the coupled HCM system with high accuracy but at a fraction of the computational cost. These surrogate models will be specifically designed to handle the complex interface dynamics inherent in two-phase flow systems and the evolving fracture networks characteristic of CUS and EGS applications. The resulting fast simulation capabilities will enable comprehensive parametric studies, sensitivity analysis, and risk assessment that would be computationally prohibitive with traditional high-fidelity approaches.

**SO3:** To develop and deploy "CUS sim Cloud," a web-based computational platform for long-term risk assessment and public engagement for CUS/EGS projects. This objective translates the scientific advances from SO1 into a tangible, high-impact tool. Using the HPC framework, a comprehensive database of long-term CUS/EGS evolution scenarios will be generated. CUS-Sim will serve a dual purpose: a) as a powerful tool for stakeholders (engineers, regulators) to perform rapid uncertainty quantification, optimize injection strategies, and assess long-term risks (e.g., leakage); and b) as a transparent and interactive tool for the public to visualize the long-term security of proposed projects, fostering understanding and building confidence.

# 1.2 State of the art and innovative aspects of the project

The successful deployment of Carbon Utilization and Storage (CUS) and Enhanced Geothermal Systems (EGS) using CO₂ as a working fluid requires a fundamental understanding of the complex, coupled hydro-chemo-mechanical (HCM) processes that govern the long-term evolution of geological reservoirs. This section presents a comprehensive review of the current state of the art in modeling these multiphysics phenomena, identifies critical knowledge gaps, and demonstrates how this project's innovative approach addresses these limitations through three key technological advances: (1) modular simulation frameworks for multiphysics coupling, (2) advanced modeling for long-term chemo-mechanical damage, and (3) computationally efficient approaches for practical applications.

## 1.2.1 Advanced Numerical Methods for Coupled Hydro-Chemo-Mechanical Processes

### Current State of the Art

The modeling of coupled hydro-chemo-mechanical processes in CUS and EGS systems requires sophisticated numerical approaches capable of handling the complex interactions between fluid flow, mechanical deformation, and chemical reactions in fractured porous media. Current approaches to hydro-mechanical coupling in subsurface systems predominantly rely on traditional finite element methods with standard Galerkin formulations, which treat displacement and pore pressure as separate field variables solved through iterative coupling schemes (Coussy, 2004). These conventional approaches often suffer from numerical instabilities, particularly in nearly incompressible conditions or when dealing with evolving fracture networks, and require complex interface tracking algorithms to handle discontinuities in the displacement and pressure fields (Kim et al., 2011). For reactive transport modeling, most existing frameworks employ standard finite element or finite difference methods to solve the advection-diffusion-reaction equations, but these approaches face significant challenges when dealing with advection-dominated transport phenomena, leading to numerical oscillations, artificial diffusion, and loss of accuracy in capturing sharp concentration fronts (Steefel et al., 2015). State-of-the-art reactive transport codes such as TOUGHREACT, PFLOTRAN, and CrunchFlow typically rely on operator splitting techniques that decouple transport and reaction processes, solving them sequentially within each time step, but this approach can introduce splitting errors and temporal inconsistencies, particularly when dealing with fast reactions or strong coupling between transport and chemistry (Parkhurst and Wissmeier, 2015). The treatment of fractures in current multiphysics frameworks remains a significant challenge, with most approaches relying on discrete fracture network models that require explicit representation of fracture geometry and complex algorithms for handling fracture intersection, propagation, and interaction with the surrounding matrix (Berre et al., 2019).

### Critical Gaps

Despite significant advances in numerical methods for subsurface multiphysics, several critical limitations persist that hinder accurate and efficient modeling of CUS and EGS systems. Traditional approaches to hydro-mechanical coupling suffer from the inability to naturally handle evolving fracture networks and complex fracture geometries, as discrete fracture models require explicit tracking of fracture surfaces and become computationally prohibitive when dealing with complex fracture patterns involving branching, coalescence, and multiple intersections (Settgast et al., 2017). Standard finite element methods for reactive transport are inadequate for handling advection-dominated problems, which are characteristic of CO₂ injection scenarios where high injection rates create steep concentration gradients and sharp reaction fronts, leading to numerical oscillations and spurious solutions that compromise the accuracy of long-term predictions (Hughes et al., 1989). The coupling between mechanical deformation and fluid flow in fractured media presents significant challenges for traditional numerical approaches, as the evolution of fracture apertures and connectivity under changing stress conditions requires sophisticated algorithms that can handle the nonlinear feedback between mechanical and hydraulic processes (Rutqvist, 2011). Current reactive transport models struggle with the computational efficiency required for long-term simulations, as the detailed geochemical calculations necessary for accurate prediction of mineral dissolution and precipitation reactions are computationally expensive and become prohibitive when applied to large-scale three-dimensional problems over geological timescales (Molins et al., 2012). Additionally, most existing frameworks lack the adaptive capabilities necessary to efficiently handle the multi-scale nature of CUS and EGS processes, where local phenomena such as reaction fronts and stress concentrations require fine spatial resolution while the overall domain may be much larger, leading to inefficient use of computational resources and limiting the practical applicability of high-fidelity models (Flemisch et al., 2011).

### Innovative Aspects of This Project

This project addresses these fundamental limitations through the implementation of cutting-edge numerical methods specifically designed to handle the complex physics of coupled hydro-chemo-mechanical processes in fractured porous media. The hydro-mechanical coupling will be addressed using a phase-field approach that provides a unified framework for modeling displacement and pore pressure fields while naturally handling complex fracture geometries and evolution without the need for explicit interface tracking (Miehe et al., 2015). The phase-field method represents fractures as diffuse interfaces characterized by a continuous phase-field variable, enabling natural handling of fracture initiation, propagation, branching, and coalescence within a single mathematical framework, while the coupling between mechanical deformation and fluid flow is achieved through a thermodynamically consistent energy functional that incorporates both mechanical and hydraulic driving forces (Borden et al., 2012). For the reactive transport problem, advanced stabilized finite element methods, specifically Taylor-Galerkin and Streamline-Upwind Petrov-Galerkin (SUPG) formulations, will be implemented to handle advection-dominated transport phenomena while maintaining numerical stability and accuracy (Brooks and Hughes, 1982). These stabilized methods introduce carefully designed artificial diffusion terms that eliminate numerical oscillations without compromising the accuracy of the solution, enabling robust simulation of sharp concentration fronts and reaction zones that are characteristic of CO₂-brine-rock interactions (Codina, 1998). The computational efficiency of the framework will be enhanced through the implementation of adaptive mesh refinement techniques that automatically adjust the spatial discretization based on solution gradients and error estimates, concentrating computational resources in regions of high activity such as reaction fronts, fracture tips, and stress concentrations while maintaining coarser resolution in less critical areas (Bangerth and Rannacher, 2003). To address the computational challenges associated with detailed geochemical modeling, sophisticated surrogate models will be developed using the IPhreeqc library, which provides a comprehensive interface to the PHREEQC geochemical modeling capabilities, enabling the training of machine learning models that can rapidly predict reaction rates and equilibrium states without the computational overhead of full thermodynamic calculations (Parkhurst and Appelo, 2013). The integration of these advanced numerical methods within a staggered coupling framework will provide unprecedented capabilities for modeling the complex, coupled physics of CUS and EGS systems while maintaining computational efficiency suitable for practical applications, representing a significant advance over current state-of-the-art approaches that rely on simplified coupling schemes and less sophisticated numerical methods.

## 1.2.2 Challenges in Modeling Chemical Reactions for Chemo-Mechanical Risk Assessment

### Current State of the Art

The modeling of chemical reactions in CO₂ storage and enhanced geothermal systems represents one of the most computationally challenging aspects of subsurface multiphysics simulation, requiring the integration of complex thermodynamic and kinetic processes that operate over vastly different temporal and spatial scales. Current state-of-the-art approaches to reactive transport modeling rely heavily on comprehensive geochemical engines such as PHREEQC, TOUGHREACT, and PFLOTRAN, which implement detailed thermodynamic databases and sophisticated algorithms for solving complex chemical equilibrium and kinetic problems (Steefel et al., 2015). These models typically employ the advection-dispersion-reaction (ADR) equation as the fundamental governing equation, where the reaction term incorporates both equilibrium and kinetic processes through transition state theory-based rate laws that account for temperature dependence, mineral surface area evolution, and chemical affinity effects (Palandri and Kharaka, 2004). The most advanced reactive transport codes utilize operator splitting techniques, particularly the Sequential Non-Iterative Approach (SNIA), which decouples transport and reaction processes by solving advection-dispersion equations first, followed by batch reaction calculations at each grid point, enabling the use of specialized numerical methods optimized for each type of process (Parkhurst and Wissmeier, 2015). For CO₂ storage applications, the chemical complexity is particularly challenging due to the need to model supercritical CO₂ dissolution, carbonic acid formation and dissociation, mineral dissolution kinetics for primary silicate and carbonate phases, and the precipitation of secondary minerals such as clays and zeolites, all under high-pressure, high-temperature conditions that require accurate equations of state and activity coefficient models (Xu et al., 2005). The coupling between chemical reactions and mechanical processes is typically handled through empirical relationships that link porosity changes due to mineral dissolution and precipitation to mechanical property degradation, but these approaches often lack the thermodynamic rigor necessary for accurate long-term predictions and fail to capture the complex feedback mechanisms between chemical and mechanical processes (Yasuhara et al., 2006).

### Critical Gaps

Despite significant advances in reactive transport modeling, several critical limitations persist that hinder accurate assessment of chemo-mechanical risks in CUS and EGS systems. The computational cost of detailed geochemical calculations represents a major barrier to practical application, as comprehensive thermodynamic and kinetic models require iterative solution of large systems of nonlinear equations at each grid point and time step, making them prohibitively expensive for large-scale three-dimensional simulations over geological timescales (Molins et al., 2012). Current approaches to handling the vast range of reaction timescales, from rapid aqueous speciation (microseconds) to slow mineral dissolution (years to centuries), rely on simplified assumptions such as the local equilibrium assumption for fast reactions, but these approximations can lead to significant errors when dealing with systems where intermediate-timescale processes are important or where the separation of timescales is not well-defined (Steefel et al., 2019). The representation of mineral surface area evolution, which is critical for accurate prediction of reaction rates, remains poorly constrained in most models, as current approaches rely on empirical relationships between porosity and reactive surface area that do not account for the complex geometric changes that occur during dissolution and precipitation processes, leading to significant uncertainties in long-term reaction rate predictions (Li et al., 2019). The coupling between chemical reactions and mechanical deformation presents fundamental challenges for current modeling approaches, as the feedback mechanisms between chemical dissolution, mechanical weakening, and stress redistribution are highly nonlinear and can lead to complex phenomena such as reaction-induced fracturing and permeability channeling that are not captured by conventional continuum models (Huerta et al., 2013). Additionally, the validation of reactive transport models against field-scale observations remains extremely limited, as most models are calibrated against short-term laboratory experiments that may not be representative of long-term field conditions, and the lack of comprehensive field datasets makes it difficult to assess the accuracy of model predictions over the timescales relevant for CUS and EGS applications (Gherardi et al., 2007). The integration of detailed geochemical models with multiphysics frameworks also presents significant software engineering challenges, as the coupling between different codes often requires complex data exchange protocols and can introduce numerical artifacts that compromise the accuracy and stability of the overall simulation (White et al., 2005).

### Innovative Aspects of This Project

This project addresses these fundamental challenges through the development of innovative approaches to chemical reaction modeling that combine high-fidelity geochemical calculations with computationally efficient surrogate modeling techniques specifically designed for chemo-mechanical risk assessment applications. The core innovation lies in the strategic use of the IPhreeqc library, which provides a comprehensive programming interface to the PHREEQC geochemical modeling capabilities, enabling the development of sophisticated surrogate models that can capture the essential physics of complex geochemical processes while achieving dramatic computational speedup (Parkhurst and Appelo, 2013). The approach involves generating extensive training datasets using high-fidelity IPhreeqc calculations that span the full range of conditions expected in CUS and EGS applications, including variations in temperature, pressure, fluid composition, mineral assemblage, and reaction progress, ensuring that the surrogate models are trained on physically realistic and thermodynamically consistent data (Charlton and Parkhurst, 2011). Advanced machine learning techniques, including neural networks with physics-informed architectures, will be employed to develop surrogate models that can rapidly predict reaction rates, equilibrium states, and mineral stability under varying conditions, while maintaining the thermodynamic consistency and chemical accuracy of the underlying PHREEQC calculations (Wen et al., 2021). The integration of these surrogate models within the staggered coupling framework will enable efficient handling of the complex feedback mechanisms between chemical reactions and mechanical deformation, as the rapid evaluation of chemical processes will allow for more frequent coupling updates and better capture of the nonlinear interactions between chemistry and mechanics (Guo and Na, 2024). To address the challenge of mineral surface area evolution, the project will implement advanced models that couple the geometric evolution of the pore space, as predicted by the phase-field approach, with mechanistic models of reactive surface area that account for the complex changes in mineral accessibility and reaction site density that occur during dissolution and precipitation processes (Li et al., 2021). The validation of the integrated chemo-mechanical model will be enhanced through the development of comprehensive benchmarking studies that compare model predictions against available experimental and field data, with particular emphasis on capturing the long-term evolution of system properties and the emergence of complex phenomena such as reaction-induced fracturing and permeability channeling (Osei-Bonsu et al., 2022). This innovative approach to chemical reaction modeling will provide unprecedented capabilities for assessing chemo-mechanical risks in CUS and EGS systems while maintaining the computational efficiency necessary for practical applications, representing a significant advance over current state-of-the-art approaches that rely on simplified chemical models or prohibitively expensive detailed calculations.

## 1.2.3 Advanced Surrogate Modeling for Fast Simulation and Risk Assessment

### Current State of the Art

The development of fast and accurate surrogate models for complex multiphysics systems has emerged as a critical research frontier, driven by the need to bridge the gap between high-fidelity simulation capabilities and practical applications requiring rapid scenario evaluation and real-time decision support. Current approaches to surrogate modeling in subsurface applications predominantly rely on traditional reduced-order modeling techniques such as Proper Orthogonal Decomposition (POD) and Principal Component Analysis (PCA), which extract low-dimensional representations of high-dimensional systems through linear dimensionality reduction (Benner et al., 2015). These classical methods have been successfully applied to single-physics problems such as groundwater flow and heat transfer, but they struggle to capture the complex nonlinear dynamics and multi-scale phenomena characteristic of coupled hydro-chemo-mechanical systems (Quarteroni et al., 2016). Recent advances in machine learning have led to the development of more sophisticated surrogate modeling approaches, including neural networks, Gaussian process regression, and polynomial chaos expansion, which have shown promise for capturing nonlinear relationships and handling high-dimensional parameter spaces (Raissi et al., 2019). However, most existing machine learning approaches for subsurface applications focus on steady-state or quasi-static problems and lack the temporal modeling capabilities necessary for capturing the dynamic evolution of coupled multiphysics systems over extended time periods (Wen et al., 2021). State-of-the-art deep learning architectures such as Convolutional Neural Networks (CNNs) and Recurrent Neural Networks (RNNs) have been applied to subsurface modeling problems, but these approaches typically treat the spatial and temporal dimensions independently and fail to capture the complex spatiotemporal correlations that are fundamental to reactive transport and chemo-mechanical processes (Zhu and Zabaras, 2018). The integration of physics-informed constraints into machine learning models has emerged as a promising approach for improving the accuracy and generalizability of surrogate models, with Physics-Informed Neural Networks (PINNs) showing particular promise for problems governed by partial differential equations (Karniadakis et al., 2021).

### Critical Gaps

Despite significant progress in surrogate modeling techniques, several critical limitations persist that hinder their application to complex CUS and EGS systems. Traditional reduced-order modeling approaches are fundamentally limited by their reliance on linear dimensionality reduction, which cannot adequately capture the nonlinear dynamics and complex phase behavior characteristic of CO₂-brine-rock systems, leading to poor approximation quality and limited predictive capability for conditions outside the training data range (Brunton and Kutz, 2019). Most existing machine learning approaches for subsurface applications lack the ability to handle evolving interfaces and phase boundaries, which are critical features of two-phase flow systems and reactive transport problems, as standard neural network architectures are not designed to preserve the geometric and topological properties of moving interfaces (Raissi et al., 2020). The temporal modeling capabilities of current surrogate modeling approaches are inadequate for capturing the long-term evolution of coupled multiphysics systems, as most methods rely on simple time-stepping approaches that accumulate errors over long integration periods and fail to capture the complex temporal correlations that govern system dynamics (Champion et al., 2019). Current approaches to training surrogate models for multiphysics applications suffer from the curse of dimensionality, as the high-dimensional parameter spaces characteristic of subsurface systems require enormous training datasets that are computationally expensive to generate using high-fidelity simulations, making it difficult to achieve adequate coverage of the parameter space (Peherstorfer et al., 2018). The validation and verification of surrogate models for complex multiphysics systems remains a significant challenge, as traditional error metrics may not adequately capture the physical consistency and long-term stability of the surrogate predictions, and the lack of comprehensive benchmarking datasets makes it difficult to assess the relative performance of different surrogate modeling approaches (Tripathy and Bilionis, 2018). Additionally, most existing surrogate modeling frameworks lack the flexibility and modularity necessary for integration with complex multiphysics simulation codes, requiring significant software development efforts and limiting their practical applicability (Benner et al., 2015).

### Innovative Aspects of This Project

This project addresses these fundamental limitations through the development of cutting-edge surrogate modeling techniques specifically designed for coupled hydro-chemo-mechanical systems, leveraging advanced machine learning architectures that can capture the complex spatiotemporal dynamics and interface evolution characteristic of CUS and EGS applications. The core innovation lies in the implementation of Interface-Aware U-Net-ConvLSTM architectures that combine the spatial feature extraction capabilities of U-Net convolutional networks with the temporal modeling capabilities of ConvLSTM (Convolutional Long Short-Term Memory) networks, while incorporating specialized interface-aware components that can track and predict the evolution of phase boundaries and reaction fronts (Shi et al., 2015). The Interface-Aware U-Net component utilizes advanced attention mechanisms and multi-scale feature extraction to identify and track evolving interfaces in the multiphysics system, enabling accurate prediction of phenomena such as CO₂ plume migration, reaction front propagation, and fracture evolution without the need for explicit interface tracking algorithms (Ronneberger et al., 2015). The ConvLSTM component provides sophisticated temporal modeling capabilities that can capture long-term dependencies and complex temporal correlations in the system dynamics, enabling accurate prediction of system evolution over extended time periods while maintaining computational efficiency (Xingjian et al., 2015). To further enhance the modeling capabilities, the project will also implement Interface-Aware Vision Transformers (ViTs) that leverage the self-attention mechanism to capture long-range spatial and temporal dependencies in the multiphysics system, providing superior performance for problems involving complex coupling between distant regions of the domain (Dosovitskiy et al., 2020). The training of these advanced surrogate models will be facilitated by the development of comprehensive datasets generated through batch simulations using high-performance computing (HPC) techniques, ensuring adequate coverage of the high-dimensional parameter space while maintaining computational efficiency through intelligent sampling strategies and active learning approaches (Settles, 2009). The integration of physics-informed constraints into the machine learning architectures will ensure that the surrogate models respect fundamental physical principles such as mass conservation, energy conservation, and thermodynamic consistency, improving their accuracy and generalizability beyond the training data range (Karniadakis et al., 2021). The practical impact of these advanced surrogate modeling capabilities will be maximized through the development of "CUS-Sim Cloud," a web-based computational platform that provides user-friendly access to the fast simulation capabilities, enabling stakeholders to perform rapid scenario analysis, sensitivity studies, and risk assessment without requiring specialized expertise in numerical modeling or high-performance computing (Armbrust et al., 2010). The platform will feature advanced visualization capabilities that can effectively communicate complex multiphysics results to diverse stakeholder groups, including interactive 3D visualizations, animated time series, and real-time parameter sensitivity analysis, facilitating informed decision-making and enhancing public engagement with CUS and EGS technologies (Sedlmair et al., 2012).

## 1.2.4 Connection to Research Objectives and Innovation Summary

The comprehensive review of the state of the art presented above clearly demonstrates the critical need for the innovative approaches proposed in this project. The three specific objectives (SO1, SO2, and SO3) directly address the identified knowledge gaps and technological limitations through a coordinated research strategy that advances the field on multiple fronts simultaneously.

**SO1 Innovation - High-Fidelity Staggered Framework:** The development of a high-fidelity staggered hydro-chemo-mechanical framework addresses the fundamental limitations of existing simulation tools by integrating cutting-edge numerical methods specifically designed for coupled multiphysics processes. The strategic combination of phase-field methods for hydro-mechanical coupling with stabilized finite element methods (Taylor-Galerkin and SUPG) for reactive transport represents a significant advance over current approaches that rely on simplified coupling schemes or computationally prohibitive monolithic formulations. The implementation of adaptive mesh refinement and IPhreeqc-based surrogate models for geochemical reactions ensures computational efficiency while maintaining the physical rigor necessary for accurate long-term predictions.

**SO2 Innovation - Advanced Surrogate Modeling:** The development of sophisticated machine learning architectures, including Interface-Aware U-Net-ConvLSTM and Interface-Aware Vision Transformers, addresses the critical computational barrier that prevents the use of high-fidelity models for extensive scenario analysis and risk assessment. These advanced surrogate modeling techniques can capture the complex spatiotemporal dynamics and interface evolution characteristic of CUS and EGS systems while achieving dramatic computational speedup, enabling comprehensive parametric studies and sensitivity analysis that would be computationally prohibitive with traditional high-fidelity approaches.

**SO3 Innovation - Accessible Computational Platform:** The creation of the CUS-Sim Cloud platform addresses the critical gap between advanced scientific modeling and practical stakeholder engagement by leveraging the fast simulation capabilities developed in SO2. By making sophisticated multiphysics modeling accessible through a user-friendly web interface with advanced visualization capabilities and real-time scenario exploration, this project will democratize access to cutting-edge modeling tools and facilitate informed decision-making by diverse stakeholder groups including project developers, regulatory agencies, and the general public.

The synergistic combination of these three innovations will create a transformative computational framework that advances the scientific understanding of CUS and EGS systems while providing practical tools for their safe and efficient deployment. This project represents a paradigm shift from traditional computationally expensive academic modeling approaches toward integrated, efficient, and practically relevant computational tools that can support the widespread adoption of these critical climate technologies.

## References

Armbrust, M., Fox, A., Griffith, R., Joseph, A. D., Katz, R., Konwinski, A., ... & Zaharia, M. (2010). A view of cloud computing. *Communications of the ACM*, 53(4), 50-58.

Bangerth, W., & Rannacher, R. (2003). *Adaptive finite element methods for differential equations*. Birkhäuser.

Benner, P., Gugercin, S., & Willcox, K. (2015). A survey of projection-based model reduction methods for parametric dynamical systems. *SIAM Review*, 57(4), 483-531.

Berre, I., Doster, F., & Keilegavlen, E. (2019). Flow in fractured porous media: A review of conceptual models and discretization approaches. *Transport in Porous Media*, 130(1), 215-236.

Bielicki, J. M., Pollak, M. F., Fitts, J. P., Peters, C. A., & Wilson, E. J. (2014). Causes and financial consequences of geologic CO2 storage reservoir leakage and interference with other subsurface resources. *Environmental Science & Technology*, 48(1), 4491-4501.

Borden, M. J., Verhoosel, C. V., Scott, M. A., Hughes, T. J., & Landis, C. M. (2012). A phase-field description of dynamic brittle fracture. *Computer Methods in Applied Mechanics and Engineering*, 217, 77-95.

Brooks, A. N., & Hughes, T. J. (1982). Streamline upwind/Petrov-Galerkin formulations for convection dominated flows with particular emphasis on the incompressible Navier-Stokes equations. *Computer Methods in Applied Mechanics and Engineering*, 32(1-3), 199-259.

Brunton, S. L., & Kutz, J. N. (2019). *Data-driven science and engineering: Machine learning, dynamical systems, and control*. Cambridge University Press.

Champion, K., Lusch, B., Kutz, J. N., & Brunton, S. L. (2019). Data-driven discovery of coordinates and governing equations. *Proceedings of the National Academy of Sciences*, 116(45), 22445-22451.

Charlton, S. R., & Parkhurst, D. L. (2011). Modules based on the geochemical model PHREEQC for use in scripting and programming languages. *Computers & Geosciences*, 37(10), 1653-1663.

Codina, R. (1998). Comparison of some finite element methods for solving the diffusion-convection-reaction equation. *Computer Methods in Applied Mechanics and Engineering*, 156(1-4), 185-210.

Computer Modelling Group. (2020). STARS User Guide. Computer Modelling Group Ltd.

Coussy, O. (2004). *Poromechanics*. John Wiley & Sons.

Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., ... & Houlsby, N. (2020). An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*.

Flemisch, B., Darcis, M., Erbertseder, K., Faigle, B., Lauser, A., Mosthaf, K., ... & Helmig, R. (2011). DuMux: DUNE for multi-{phase, component, scale, physics, ...} flow and transport in porous media. *Advances in Water Resources*, 34(9), 1102-1112.

Gaston, D., Newman, C., Hansen, G., & Lebrun-Grandié, D. (2009). MOOSE: A parallel computational framework for coupled systems of nonlinear equations. *Nuclear Engineering and Design*, 239(10), 1768-1778.

Gaus, I., Azaroual, M., & Czernichowski-Lauriol, I. (2005). Reactive transport modelling of the impact of CO2 injection on the clayey cap rock at Sleipner (North Sea). *Chemical Geology*, 217(3-4), 319-337.

Gherardi, F., Xu, T., & Pruess, K. (2007). Numerical modeling of self-limiting and self-enhancing caprock alteration induced by CO2 storage in a depleted gas reservoir. *Chemical Geology*, 244(1-2), 103-129.

Guo, Y., & Na, S. (2024). A reactive-transport phase-field modelling approach of chemo-assisted cracking in saturated sandstone. *Computer Methods in Applied Mechanics and Engineering*, 419, 116645.

Hageman, T., & Martínez-Pañeda, E. (2023). A phase field-based framework for electro-chemo-mechanical fracture: Crack-contained electrolytes, chemical reactions and stabilisation. *Computer Methods in Applied Mechanics and Engineering*, 415, 116235.

Huerta, N. J., Hesse, M. A., Bryant, S. L., Strazisar, B. R., & Lopano, C. (2013). Experimental evidence for self-limiting reactive flow through a fractured cement core: Implications for time-dependent wellbore leakage. *Environmental Science & Technology*, 47(1), 269-275.

Hughes, T. J., Franca, L. P., & Hulbert, G. M. (1989). A new finite element formulation for computational fluid dynamics: VIII. The Galerkin/least-squares method for advective-diffusive equations. *Computer Methods in Applied Mechanics and Engineering*, 73(2), 173-189.

Karniadakis, G. E., Kevrekidis, I. G., Lu, L., Perdikaris, P., Wang, S., & Yang, L. (2021). Physics-informed machine learning. *Nature Reviews Physics*, 3(6), 422-440.

Kim, J., Tchelepi, H. A., & Juanes, R. (2011). Stability and convergence of sequential methods for coupled flow and geomechanics: Fixed-stress and fixed-strain splits. *Computer Methods in Applied Mechanics and Engineering*, 200(13-16), 1591-1606.

Li, L., Peters, C. A., & Celia, M. A. (2019). Reactive transport in evolving porous media. *Reviews in Mineralogy and Geochemistry*, 85(1), 197-234.

Li, L., Steefel, C. I., & Yang, L. (2021). Scale dependence of mineral dissolution rates within single pores and fractures. *Geochimica et Cosmochimica Acta*, 290, 113-127.

Logg, A., Mardal, K. A., & Wells, G. (2012). *Automated solution of differential equations by the finite element method: The FEniCS book* (Vol. 84). Springer Science & Business Media.

Miehe, C., Mauthe, S., & Teichtmeister, S. (2015). Minimization principles for the coupled problem of Darcy–Biot-type fluid transport in porous media linked to phase field modeling of fracture. *Journal of the Mechanics and Physics of Solids*, 82, 186-217.

Molins, S., Trebotich, D., Steefel, C. I., & Shen, C. (2012). An investigation of the effect of pore scale flow on average geochemical reaction rates using direct numerical simulation. *Water Resources Research*, 48(3).

Nordbotten, J. M., Kavetski, D., Celia, M. A., & Bachu, S. (2009). A semi-analytical model for large-scale injection of CO2 into aquifers. *Transport in Porous Media*, 80(2), 269-294.

Osei-Bonsu, K., Shokri, N., & Grassia, P. (2022). A review on reactive transport modelling in porous media. *Geomechanics and Geophysics for Geo-Energy and Geo-Resources*, 8(3), 85.

Palandri, J. L., & Kharaka, Y. K. (2004). A compilation of rate parameters of water-mineral interaction kinetics for application to geochemical modeling. *US Geological Survey Open File Report*, 2004-1068.

Parkhurst, D. L., & Appelo, C. A. J. (2013). Description of input and examples for PHREEQC version 3—a computer program for speciation, batch-reaction, one-dimensional transport, and inverse geochemical calculations. *US Geological Survey Techniques and Methods*, 6(A43), 497.

Parkhurst, D. L., & Wissmeier, L. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC. *Advances in Water Resources*, 83, 176-189.

Pawar, R. J., Bromhal, G. S., Carey, J. W., Foxall, W., Korre, A., Ringrose, P. S., ... & Zhang, Y. (2015). Recent advances in risk assessment and risk management of geologic CO2 storage. *International Journal of Greenhouse Gas Control*, 40, 292-311.

Peherstorfer, B., Willcox, K., & Gunzburger, M. (2018). Survey of multifidelity methods in uncertainty propagation, inference, and optimization. *SIAM Review*, 60(3), 550-591.

Pruess, K., Oldenburg, C., & Moridis, G. (1999). TOUGH2 user's guide version 2. Lawrence Berkeley National Laboratory.

Quarteroni, A., Manzoni, A., & Negri, F. (2016). *Reduced basis methods for partial differential equations: an introduction* (Vol. 92). Springer.

Raissi, M., Perdikaris, P., & Karniadakis, G. E. (2019). Physics-informed neural networks: A deep learning framework for solving forward and inverse problems involving nonlinear partial differential equations. *Journal of Computational Physics*, 378, 686-707.

Raissi, M., Yazdani, A., & Karniadakis, G. E. (2020). Hidden fluid mechanics: Learning velocity and pressure fields from flow visualizations. *Science*, 367(6481), 1026-1030.

Ronneberger, O., Fischer, P., & Brox, T. (2015). U-net: Convolutional networks for biomedical image segmentation. In *International Conference on Medical image computing and computer-assisted intervention* (pp. 234-241). Springer.

Rutqvist, J. (2011). Status of the TOUGH-FLAC simulator and recent applications related to coupled fluid flow and crustal deformations. *Computers & Geosciences*, 37(6), 739-750.

Rutqvist, J. (2012). The geomechanics of CO2 storage in deep sedimentary formations. *Geotechnical and Geological Engineering*, 30(3), 525-551.

Sedlmair, M., Meyer, M., & Munzner, T. (2012). Design study methodology: Reflections from the trenches and the stacks. *IEEE Transactions on Visualization and Computer Graphics*, 18(12), 2431-2440.

Settgast, R. R., Johnson, S. M., Walsh, S. D., Morris, J. P., Ryerson, F. J., & Friedmann, S. J. (2017). A fully coupled method for massively parallel simulation of hydraulically driven fractures in 3-dimensions. *International Journal for Numerical and Analytical Methods in Geomechanics*, 41(5), 627-653.

Settles, B. (2009). Active learning literature survey. *University of Wisconsin-Madison Department of Computer Sciences*.

Shi, X., Chen, Z., Wang, H., Yeung, D. Y., Wong, W. K., & Woo, W. C. (2015). Convolutional LSTM network: A machine learning approach for precipitation nowcasting. *Advances in Neural Information Processing Systems*, 28.

Steefel, C. I., Appelo, C. A. J., Arora, B., Jacques, D., Kalbacher, T., Kolditz, O., ... & Yeh, G. T. (2015). Reactive transport codes for subsurface environmental simulation. *Computational Geosciences*, 19(3), 445-478.

Steefel, C. I., Beckingham, L. E., & Landrot, G. (2019). Reactive transport at the crossroads. *Reviews in Mineralogy and Geochemistry*, 85(1), 1-26.

Stephens, J. C., Rand, G. M., & Melnick, L. L. (2009). Wind energy in US media: A comparative state-level analysis of a critical climate change mitigation technology. *Global Environmental Change*, 19(1), 89-97.

Sun, A. Y., Morris, A. P., & Mohanty, S. (2013). Sequential updating of multimodal hydrogeologic parameter fields using localization and clustering techniques. *Water Resources Research*, 49(10), 7204-7220.

Taron, J., & Elsworth, D. (2009). Thermal–hydrologic–mechanical–chemical processes in the evolution of engineered geothermal systems. *International Journal of Rock Mechanics and Mining Sciences*, 46(5), 855-864.

Tripathy, R. K., & Bilionis, I. (2018). Deep UQ: Learning deep neural network surrogate models for high dimensional uncertainty quantification. *Journal of Computational Physics*, 375, 565-588.

Wen, G., Tang, M., & Benson, S. M. (2021). CCSNet: a deep learning modeling suite for CO2 storage. *Advances in Water Resources*, 155, 104009.

White, M. D., Oostrom, M., & Lenhard, R. J. (2004). Modeling fluid flow and transport in variably saturated porous media with the STOMP simulator. 1. Nonvolatile three-phase system. *Advances in Water Resources*, 27(1), 83-95.

White, S. P., Allis, R. G., Moore, J., Chidsey, T., Morgan, C., Gwynn, W., & Adams, M. (2005). Simulation of reactive transport of injected CO2 on the Colorado Plateau, Utah, USA. *Chemical Geology*, 217(3-4), 387-405.

Xingjian, S. H. I., Chen, Z., Wang, H., Yeung, D. Y., Wong, W. K., & Woo, W. C. (2015). Convolutional LSTM network: A machine learning approach for precipitation nowcasting. *Advances in Neural Information Processing Systems*, 28.

Xu, T., Apps, J. A., & Pruess, K. (2005). Mineral sequestration of carbon dioxide in a sandstone–shale system. *Chemical Geology*, 217(3-4), 295-318.

Yasuhara, H., Polak, A., Mitani, Y., Grader, A. S., Halleck, P. M., & Elsworth, D. (2006). Evolution of fracture permeability through fluid–rock reaction under hydrothermal conditions. *Earth and Planetary Science Letters*, 244(1-2), 186-200.

Zhu, Y., & Zabaras, N. (2018). Bayesian deep convolutional encoder–decoder networks for surrogate modeling and uncertainty quantification. *Journal of Computational Physics*, 366, 415-447.

Zyvoloski, G. A., Robinson, B. A., Dash, Z. V., & Trease, L. L. (1997). Summary of the models and methods for the FEHM application-a finite-element heat-and mass-transfer code. Los Alamos National Laboratory.
