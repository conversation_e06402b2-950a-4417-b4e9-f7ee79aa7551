# 1.1 Introduction and Objectives

## 1.1 Introduction and objectives

The European Union is at the forefront of the global effort to combat climate change, with the ambitious goal of achieving climate neutrality by 2050 as outlined in the European Green Deal. Carbon Capture, Utilisation, and Storage (CUS) is recognized by the Intergovernmental Panel on Climate Change (IPCC) and the European Commission as a critical component in the portfolio of mitigation actions to provide a strategic bridge away from fossil fuels and decarbonize hard-to-abate industries. To achieve the necessary scale, Europe must develop a robust portfolio of geological storage sites. For the EU, developing a robust CUS infrastructure, particularly for onshore geological storage, is not just an environmental necessity but a strategic one. It offers increased flexibility, reduced costs, and the ability for Member States to manage their decarbonisation strategies at a territorial level, thereby enhancing energy security and fostering local economic activity.

However, the success of onshore CUS hinges on one overriding factor: public acceptance, which is contingent on demonstrating uncompromising long-term safety and storage permanence. When CO2 is injected deep underground into saline aquifers or depleted hydrocarbon reservoirs, it exists in a supercritical state. Over time, this supercritical CO2 interacts with the surrounding rock and brine, triggering a cascade of coupled Hydro-Chemo-Mechanical (HCM) processes. These interactions, involving mineral dissolution and precipitation, can alter the fundamental properties of the reservoir rock including its porosity, permeability, and mechanical strength. These alterations, in turn, can affect the integrity of the caprock seal, and potentially create new pathways for CO2 to leak back into the atmosphere. Furthermore, the dual-purpose application of CO2 injection, for both storage and as in Enhanced Geothermal Systems (EGS), introduces additional complexities. While CO2 offers thermodynamic advantages over water in EGS, the long-term chemo-mechanical consequences of cycling CO2 in these systems are largely unknown.

Current computational models often struggle to capture the full complexity of these coupled multi-physics phenomena over the vast timescales (e.g. across decadal, centennial, and millennial timescales) relevant for geological storage. They either oversimplify the chemical-mechanical couplings or are too computationally expensive to be used for the large number of simulations required for robust uncertainty quantification and risk assessment. This represents a critical knowledge gap that hinders our ability to confidently select, operate, and monitor CUS and EGS sites. This project is directly motivated by the need to fill this gap by developing a next-generation computational framework capable of accurately and efficiently predicting the long-term fate of stored CO2, thereby paving the way for the safe and widespread deployment of CUS technologies across Europe.

**Overall Objective:**
The overall objective of this project is to develop a novel, robust, and computationally efficient modeling framework to predict the long-term multi-physical evolution of geological reservoirs subjected to CO2 injection for both storage and Enhanced Geothermal Systems (EGS) applications. This ambitious goal will be achieved through the creation of a modular, staggered simulation scheme that integrates high-fidelity models for the key physical processes governing reservoir behaviour. The framework will be built upon a flexible, open-source platform, allowing for the seamless coupling of different physical modules to address the specific challenges of CUS and EGS.

A cornerstone of this project will be the use of a novel staggered scheme to model the complex chemo-hydro-mechanical interactions and fracture propagation within the reservoir. This cutting-edge technique is particularly well-suited for capturing the intricate interplay between chemical reactions (dissolution and precipitation) and mechanical deformation, which is crucial for assessing the long-term integrity of the caprock and the risk of induced fracturing.

Furthermore, the project recognizes that high-fidelity simulations are often too slow for the extensive parametric studies needed for risk assessment and optimization. To overcome this limitation, this project will develop fast and accurate surrogate models from the simulation data. These surrogate models will capture the essential dynamics of the complex HCM system at a fraction of the computational cost, enabling comprehensive uncertainty quantification and robust risk analysis. This project will deliver a transformative tool for the design, operation, and monitoring of CUS and EGS projects, ultimately contributing to their safe and efficient deployment as key technologies in the fight against climate change.

**Specific Objectives**
To achieve the overall objective, this project is structured around three specific, interconnected objectives (SOs):

**SO1:** To develop a modular, fully coupled hydro-chemo-mechanical (HCM) numerical framework for fractured porous media. This objective focuses on the creation of the core simulation tool. It involves the development and integration of five distinct modules within a staggered coupling scheme: (1) a Mixed Finite Element Method (MFEM) module for Darcy's flow and mass conservation; (2) a Finite Element Method (FEM) module for Reynolds' lubrication in fractures; (3) a module for the mechanics of poroelastic media; (4) a Symmetric Galerkin Boundary Element Method (SGBEM) module for fracture mechanics; and (5) a phase-field module for advection-diffusion-reaction processes. The coupling between the fluid and solid mechanics will be achieved through a robust fixed-stress splitting scheme, while the reactive transport equations will be solved efficiently using operator splitting. The successful completion of this objective will result in a novel and powerful open-source simulation platform capable of capturing the complex, coupled physics of CUS and EGS with high fidelity.

**SO2:** To quantify long-term chemo-mechanical risks by developing a surrogate-model-driven assessment framework. This objective will use the high-fidelity model (SO1) to simulate CO2-rock-brine interactions and generate a comprehensive dataset on long-term reservoir evolution. This data will then be used to train a computationally efficient Koopman Autoencoder (KAE) surrogate model. The resulting KAE will predict reservoir behavior with unprecedented speed, enabling comprehensive risk assessment of potential leakage through extensive simulations. This provides a transformative tool for ensuring the long-term safety and public acceptance of CUS and EGS projects.

**SO3:** To develop and deploy "CUS sim Cloud," a web-based computational platform for long-term risk assessment and public engagement for CUS/EGS projects. This objective translates the scientific advances from SO1 into a tangible, high-impact tool. Using the HPC framework, a comprehensive database of long-term CUS/EGS evolution scenarios will be generated. CUS-Sim will serve a dual purpose: a) as a powerful tool for stakeholders (engineers, regulators) to perform rapid uncertainty quantification, optimize injection strategies, and assess long-term risks (e.g., leakage); and b) as a transparent and interactive tool for the public to visualize the long-term security of proposed projects, fostering understanding and building confidence.

# 1.2 State of the art and innovative aspects of the project

The successful deployment of Carbon Utilization and Storage (CUS) and Enhanced Geothermal Systems (EGS) using CO₂ as a working fluid requires a fundamental understanding of the complex, coupled hydro-chemo-mechanical (HCM) processes that govern the long-term evolution of geological reservoirs. This section presents a comprehensive review of the current state of the art in modeling these multiphysics phenomena, identifies critical knowledge gaps, and demonstrates how this project's innovative approach addresses these limitations through three key technological advances: (1) advanced phase-field modeling for chemo-mechanical damage, (2) modular simulation frameworks for multiphysics coupling, and (3) computationally efficient surrogate modeling for practical applications.

## 1.2.1 The Challenge of Modeling Long-Term Chemo-Mechanical Damage

### Current State of the Art

The injection of CO₂ into geological formations triggers a complex cascade of coupled processes that fundamentally alter the physical and chemical properties of reservoir rocks over time. The acidic nature of CO₂-saturated brine (pH typically ranging from 3.5 to 5.5) drives mineral dissolution reactions, particularly affecting carbonate minerals such as calcite (CaCO₃) and dolomite (CaMg(CO₃)₂), while simultaneously promoting the precipitation of secondary minerals including clays and zeolites (Gaus et al., 2008; Xu et al., 2007). These geochemical reactions create a feedback loop with mechanical processes, where chemical weakening of the rock matrix leads to stress redistribution, potential fracture initiation, and altered permeability pathways.

Current modeling approaches for chemo-mechanical coupling in CO₂ storage systems can be broadly categorized into three main paradigms: (1) sequential coupling methods, (2) iterative coupling schemes, and (3) fully coupled approaches. Sequential coupling, exemplified by the work of Rutqvist et al. (2002) and Taron et al. (2009), treats chemical and mechanical processes as separate phenomena with one-way information transfer. While computationally efficient, this approach fails to capture the bidirectional feedback between chemical reactions and mechanical deformation that is critical for long-term reservoir evolution.

Iterative coupling schemes, as implemented in codes such as TOUGH-FLAC (Rutqvist, 2011) and OpenGeoSys (Kolditz et al., 2012), represent a significant advancement by allowing information exchange between chemical and mechanical modules at each time step. However, these approaches often suffer from convergence issues and numerical instabilities, particularly when dealing with strong coupling effects or rapid chemical reactions (White et al., 2016). The fundamental limitation lies in the discrete nature of the coupling, which can lead to artificial oscillations and loss of physical consistency.

Fully coupled approaches, such as those developed by Taron and Elsworth (2009) and Yasuhara et al. (2012), attempt to solve the complete system of equations simultaneously. While theoretically superior, these methods face significant computational challenges due to the vastly different time scales involved in chemical (seconds to hours) and mechanical (years to decades) processes. The resulting stiff systems of equations require specialized numerical techniques and often become prohibitively expensive for practical applications.

### Critical Knowledge Gaps

Despite significant advances in chemo-mechanical modeling, several critical knowledge gaps persist that limit our ability to predict long-term reservoir behavior accurately:

**Fracture Initiation and Propagation:** Current models struggle to predict the initiation and propagation of fractures driven by chemical weakening processes. Traditional approaches rely on empirical failure criteria that do not adequately capture the gradual degradation of material properties due to chemical reactions. The challenge is compounded by the heterogeneous nature of geological formations, where local variations in mineralogy and stress state can lead to preferential dissolution pathways and unexpected fracture patterns (Huerta et al., 2013).

**Multi-scale Coupling:** The coupling between pore-scale chemical reactions and continuum-scale mechanical behavior remains poorly understood. Chemical reactions occur at the mineral-fluid interface (nanometer scale), while their mechanical consequences manifest at the reservoir scale (kilometer scale). Bridging these scales requires sophisticated homogenization techniques that are currently underdeveloped for reactive systems (Molins et al., 2012).

**Long-term Evolution:** Most existing models are validated against short-term laboratory experiments (days to months), but CUS and EGS systems must operate safely for decades to centuries. The extrapolation of short-term behavior to long-term evolution involves significant uncertainties, particularly regarding the evolution of reaction rates and the potential for new reaction pathways to emerge over time (Gherardi et al., 2007).

### Innovative Aspects: Phase-Field Approach for Chemo-Mechanical Coupling

This project addresses these fundamental limitations through the implementation of a cutting-edge phase-field approach for modeling chemo-mechanical damage. Phase-field methods, originally developed for modeling phase transitions in materials science (Cahn and Hilliard, 1958), have recently emerged as a powerful tool for fracture mechanics (Francfort and Marigo, 1998; Bourdin et al., 2000) and have been extended to chemo-mechanical systems by researchers such as Martínez-Pañeda and colleagues (Hageman and Martínez-Pañeda, 2023).

The phase-field approach offers several revolutionary advantages over traditional methods:

**Natural Fracture Representation:** Unlike discrete fracture models that require explicit tracking of crack surfaces, phase-field methods represent fractures as diffuse interfaces characterized by a continuous phase-field variable. This approach naturally handles complex fracture patterns, including branching, coalescence, and interaction with heterogeneities, without the need for remeshing or special interface tracking algorithms (Miehe et al., 2010).

**Seamless Chemo-Mechanical Coupling:** The phase-field framework provides a natural platform for coupling chemical reactions with mechanical damage. Chemical dissolution can be directly linked to the evolution of the phase-field variable, creating a physically consistent representation of chemically-induced weakening. This approach captures the gradual transition from intact rock to fully damaged material, avoiding the artificial discontinuities inherent in traditional failure criteria (Klinsmann et al., 2016).

**Thermodynamic Consistency:** Phase-field models are based on rigorous thermodynamic principles, ensuring energy conservation and providing a solid theoretical foundation for the coupling between different physical processes. This thermodynamic framework naturally incorporates the driving forces for both chemical reactions and mechanical damage, leading to more robust and physically meaningful predictions (Miehe et al., 2015).

The implementation of phase-field methods in this project will be enhanced by the integration with PhreeqcRM, a state-of-the-art geochemical reaction module developed by the United States Geological Survey (Parkhurst and Wissmeier, 2013). PhreeqcRM provides a comprehensive database of thermodynamic and kinetic parameters for mineral-water reactions, enabling accurate prediction of reaction rates and equilibrium states under reservoir conditions. The coupling between phase-field mechanics and PhreeqcRM will be achieved through a novel operator splitting scheme that maintains computational efficiency while preserving the physical coupling between processes.

**Surrogate Modeling for Chemical Reactions:** To address the computational challenges associated with detailed geochemical modeling, this project will develop advanced surrogate models for chemical reactions using machine learning techniques. High-fidelity PhreeqcRM simulations will be used to generate comprehensive datasets of reaction behavior under various conditions (temperature, pressure, fluid composition, mineral assemblage). These datasets will then be used to train neural network-based surrogate models that can predict reaction rates and products with high accuracy but at a fraction of the computational cost. This approach will enable the integration of detailed geochemical modeling into large-scale reservoir simulations without prohibitive computational overhead.

## 1.2.2 The Need for Modular, Efficient Simulation Frameworks

### Current State of the Art in Multiphysics Simulation

The complexity of CUS and EGS systems necessitates the coupling of multiple physical processes, each governed by different mathematical formulations and operating on distinct temporal and spatial scales. Current simulation tools for subsurface multiphysics problems can be broadly classified into three categories: (1) monolithic codes, (2) loosely coupled frameworks, and (3) modular coupling platforms.

**Monolithic Codes:** Traditional monolithic simulators, such as TOUGH2 (Pruess et al., 1999), ECLIPSE (Schlumberger, 2020), and CMG-STARS (Computer Modelling Group, 2020), integrate multiple physical processes within a single computational framework. While these codes offer computational efficiency and numerical stability for well-established coupling schemes, they suffer from significant limitations in terms of flexibility and extensibility. The tight integration of different physics modules makes it extremely difficult to incorporate new physical processes, update numerical methods, or adapt to emerging research needs. Furthermore, monolithic codes often rely on simplified representations of complex processes to maintain computational tractability, limiting their applicability to cutting-edge research problems (Flemisch et al., 2011).

**Loosely Coupled Frameworks:** Loosely coupled approaches, exemplified by codes such as TOUGH-FLAC (Rutqvist, 2011) and FEHM-ABAQUS (Zyvoloski et al., 2008), attempt to leverage the strengths of specialized codes by coupling them through external interfaces. While this approach allows for the use of best-in-class solvers for individual physics, it introduces significant challenges in terms of data transfer, temporal synchronization, and numerical stability. The coupling is typically achieved through file-based communication or simplified interface conditions, which can lead to loss of information and artificial decoupling effects (White et al., 2016).

**Modular Coupling Platforms:** Recent advances in computational science have led to the development of modular coupling platforms such as MOOSE (Gaston et al., 2009), FEniCS (Logg et al., 2012), and deal.II (Bangerth et al., 2007). These frameworks provide sophisticated infrastructure for coupling different physics modules while maintaining flexibility and extensibility. However, most existing platforms focus on traditional finite element methods and lack the specialized capabilities required for advanced subsurface applications, such as boundary element methods for fracture mechanics or phase-field methods for reactive transport.

### Critical Limitations of Existing Frameworks

Despite significant progress in multiphysics simulation technology, several critical limitations persist that hinder the development of comprehensive CUS and EGS models:

**Inadequate Treatment of Fracture Mechanics:** Most existing frameworks rely on simplified representations of fractures, such as discrete fracture networks or embedded discontinuities. These approaches struggle to capture the complex evolution of fracture systems, particularly the initiation of new fractures and the interaction between multiple fracture sets. The lack of robust fracture mechanics capabilities severely limits the ability to predict the long-term integrity of storage formations and the performance of EGS systems (Settgast et al., 2017).

**Limited Support for Advanced Numerical Methods:** Current frameworks are predominantly based on standard finite element or finite difference methods, with limited support for specialized techniques such as mixed finite element methods (MFEM) for flow problems, symmetric Galerkin boundary element methods (SGBEM) for fracture mechanics, or phase-field methods for reactive transport. This limitation constrains the ability to leverage the most appropriate numerical techniques for different physical processes (Berre et al., 2019).

**Insufficient Temporal Coupling Strategies:** The vast range of time scales involved in CUS and EGS systems (from milliseconds for acoustic waves to millennia for long-term chemical evolution) poses significant challenges for temporal coupling. Most existing frameworks rely on simple explicit or implicit coupling schemes that are either unstable for fast processes or inefficient for slow processes. Advanced temporal coupling strategies, such as multirate methods or adaptive time stepping, are rarely implemented in a robust and general manner (Gear and Wells, 1984).

**Scalability and Performance Issues:** The computational demands of high-fidelity multiphysics simulations often exceed the capabilities of existing frameworks, particularly when dealing with large-scale three-dimensional problems or long-term evolution studies. Many frameworks lack the sophisticated parallel computing capabilities and memory management strategies required for efficient execution on modern high-performance computing systems (Balay et al., 2019).

### Innovative Aspects: Modular Staggered Coupling Framework

This project addresses these fundamental limitations through the development of a novel modular staggered coupling framework that integrates five specialized numerical modules within a unified computational platform:

**Mixed Finite Element Method (MFEM) Module for Darcy Flow:** The MFEM module will implement advanced mixed formulations for fluid flow in porous media, providing superior mass conservation properties and accurate velocity fields compared to standard finite element methods. The implementation will be based on Raviart-Thomas elements (Raviart and Thomas, 1977) and will incorporate advanced features such as adaptive mesh refinement, parallel computing capabilities, and robust linear solvers. The module will handle complex fluid properties, including the equation of state for CO₂ under supercritical conditions and the effects of dissolved species on fluid density and viscosity (Wheeler and Yotov, 2006).

**Finite Element Method (FEM) Module for Reynolds Lubrication:** A specialized FEM module will be developed to solve the Reynolds lubrication equation in fractures, accounting for the complex geometry of rough fracture surfaces and the effects of aperture variations on fluid flow. The module will incorporate advanced contact mechanics algorithms to handle fracture closure and opening under varying stress conditions, as well as sophisticated models for fracture permeability evolution due to chemical dissolution and mechanical wear (Zimmerman and Bodvarsson, 1996).

**Poroelasticity Module:** The mechanical behavior of the reservoir will be modeled using advanced poroelasticity formulations that account for the coupling between fluid pressure and mechanical deformation. The module will implement both linear and nonlinear constitutive models, including plasticity and damage mechanics, to capture the complex mechanical response of geological materials under varying stress and chemical conditions. Special attention will be paid to the implementation of robust numerical algorithms for handling nearly incompressible behavior and the coupling with fluid flow (Coussy, 2004).

**Symmetric Galerkin Boundary Element Method (SGBEM) Module:** A cutting-edge SGBEM module will be developed specifically for fracture mechanics applications in CUS and EGS systems. SGBEM offers significant advantages over traditional finite element methods for fracture problems, including the ability to handle infinite domains without artificial boundary conditions and superior accuracy for stress intensity factor calculations. The module will implement advanced features such as adaptive mesh refinement, fast multipole acceleration, and parallel computing capabilities to handle large-scale fracture systems efficiently (Bonnet et al., 1998).

**Phase-Field Module for Reactive Transport:** The phase-field module will implement state-of-the-art formulations for coupled advection-diffusion-reaction processes, with particular emphasis on the coupling between chemical reactions and mechanical damage. The module will incorporate advanced numerical techniques such as operator splitting for handling the different time scales of advection, diffusion, and reaction processes, as well as adaptive time stepping algorithms for maintaining numerical stability and accuracy (Miehe et al., 2015).

**Staggered Coupling Strategy:** The integration of these five modules will be achieved through a sophisticated staggered coupling strategy that leverages the strengths of each numerical method while maintaining overall stability and efficiency. The coupling will be based on a fixed-stress splitting scheme for the fluid-solid interaction, which has been proven to be unconditionally stable and efficient for poroelasticity problems (Kim et al., 2011). For the reactive transport coupling, an operator splitting approach will be employed to handle the different time scales of transport and reaction processes efficiently.

The modular design of the framework will provide unprecedented flexibility for researchers to adapt the code to specific applications, incorporate new physical processes, or experiment with different numerical methods. Each module will be designed as a self-contained unit with well-defined interfaces, enabling easy integration with external codes and facilitating collaborative development efforts.

## 1.2.3 The Computational Barrier to Practical Application and Stakeholder Engagement

### Current State of the Art in High-Fidelity Simulation

While significant advances have been made in developing sophisticated multiphysics models for CUS and EGS systems, a fundamental disconnect exists between the scientific capabilities of these models and their practical application for decision-making and stakeholder engagement. High-fidelity coupled HCM simulations, while scientifically rigorous, typically require substantial computational resources and execution times that range from hours to weeks for a single scenario analysis. This computational burden creates a significant barrier to their use in practical applications where rapid scenario evaluation, uncertainty quantification, and interactive exploration of design parameters are essential.

**Computational Complexity of Coupled HCM Models:** State-of-the-art coupled HCM models involve the simultaneous solution of multiple nonlinear partial differential equations with strong coupling terms and vastly different characteristic time scales. The computational complexity is further exacerbated by the need for fine spatial discretization to capture local phenomena such as reaction fronts and stress concentrations, as well as long temporal integration periods to assess long-term behavior. For example, a typical three-dimensional reservoir-scale simulation with detailed geochemical modeling may require millions of degrees of freedom and thousands of time steps, resulting in computational times measured in days or weeks on high-performance computing systems (Steefel et al., 2015).

**Limited Applicability for Uncertainty Quantification:** The high computational cost of detailed HCM models severely limits their applicability for uncertainty quantification and risk assessment, which require hundreds or thousands of simulation runs to adequately sample the parameter space. Traditional Monte Carlo approaches become prohibitively expensive, forcing researchers to rely on simplified models or reduced parameter sets that may not capture the full complexity of the system. This limitation is particularly problematic for CUS and EGS applications, where geological uncertainty and parameter variability are significant sources of risk (Nordbotten et al., 2012).

**Barriers to Stakeholder Engagement:** The complexity and computational requirements of high-fidelity models create significant barriers to stakeholder engagement and public acceptance of CUS and EGS technologies. Regulatory agencies, project developers, and the general public require tools that can provide rapid, transparent, and understandable assessments of system performance and risk. The current generation of scientific models, while technically sophisticated, are largely inaccessible to non-expert users and cannot provide the real-time feedback necessary for effective decision-making and public engagement (Bielicki et al., 2014).

### Critical Gaps in Practical Application Tools

Several critical gaps exist between current scientific modeling capabilities and the practical needs of CUS and EGS deployment:

**Lack of Real-Time Decision Support Tools:** Current modeling workflows typically involve offline simulation runs followed by post-processing and analysis, making them unsuitable for real-time decision support during project operation. The inability to rapidly evaluate the consequences of operational changes or unexpected events limits the effectiveness of adaptive management strategies and increases operational risks (Pawar et al., 2015).

**Insufficient Integration with Monitoring Data:** Most existing models operate as forward prediction tools with limited capability for integration with real-time monitoring data. The lack of robust data assimilation and model updating capabilities means that models cannot be easily calibrated or validated against field observations, reducing confidence in their predictions and limiting their utility for operational decision-making (Sun et al., 2013).

**Limited Accessibility for Non-Expert Users:** The complexity of current modeling tools requires specialized expertise in numerical methods, high-performance computing, and subsurface physics, making them inaccessible to many stakeholders who need to use model results for decision-making. The lack of user-friendly interfaces and automated workflows creates a significant barrier to the widespread adoption of advanced modeling capabilities (Flemisch et al., 2011).

**Inadequate Visualization and Communication Tools:** Current modeling platforms typically provide limited visualization capabilities and lack the sophisticated tools necessary for effective communication of complex multiphysics results to diverse stakeholder groups. The inability to create compelling, interactive visualizations that clearly communicate model predictions and uncertainties hampers public engagement and regulatory acceptance (Stephens et al., 2014).

### Innovative Aspects: Koopman Autoencoders for Surrogate Modeling

This project addresses these fundamental limitations through the development of advanced surrogate modeling techniques based on Koopman Autoencoders (KAEs), a cutting-edge machine learning approach that combines the theoretical rigor of Koopman operator theory with the practical capabilities of deep neural networks.

**Koopman Operator Theory Foundation:** Koopman operator theory provides a rigorous mathematical framework for analyzing the dynamics of nonlinear systems by embedding them in an infinite-dimensional linear space (Koopman, 1931; Mezić, 2005). This approach is particularly well-suited for multiphysics systems where the underlying dynamics are governed by nonlinear partial differential equations with complex coupling terms. The Koopman operator framework enables the identification of coherent structures and dominant modes in the system dynamics, providing fundamental insights into the long-term evolution of CUS and EGS systems (Brunton et al., 2022).

**Autoencoder Architecture for Dimensionality Reduction:** The integration of Koopman operator theory with autoencoder neural networks creates a powerful tool for learning low-dimensional representations of high-dimensional multiphysics systems. The autoencoder architecture consists of an encoder network that maps high-dimensional simulation data to a low-dimensional latent space, and a decoder network that reconstructs the full system state from the latent representation. The key innovation lies in constraining the latent space dynamics to follow linear evolution equations, enabling accurate long-term prediction with minimal computational cost (Lusch et al., 2018).

**Training Data Generation Strategy:** The development of effective KAE surrogate models requires comprehensive training datasets that capture the full range of system behavior under various operating conditions and parameter values. This project will implement a sophisticated active learning strategy that iteratively identifies regions of parameter space where additional high-fidelity simulations are needed to improve surrogate model accuracy. The training dataset will be generated using the high-fidelity multiphysics framework developed in SO1, ensuring that the surrogate models capture all relevant physical processes and coupling effects (Champion et al., 2019).

**Multi-Fidelity Modeling Approach:** To maximize the efficiency of surrogate model development, this project will implement a multi-fidelity modeling approach that combines high-fidelity simulations with lower-fidelity models to generate comprehensive training datasets. Lower-fidelity models, such as simplified analytical solutions or reduced-order physics models, will be used to explore the parameter space efficiently, while high-fidelity simulations will be strategically placed to capture complex nonlinear behavior and validate surrogate model predictions (Peherstorfer et al., 2018).

**Uncertainty Quantification Integration:** The KAE surrogate models will be designed to provide not only rapid predictions of system behavior but also quantitative estimates of prediction uncertainty. This will be achieved through the implementation of Bayesian neural network techniques that propagate parameter uncertainty through the surrogate model and provide confidence intervals for all predictions. The uncertainty quantification capabilities will be essential for risk assessment and decision-making applications (Gal and Ghahramani, 2016).

### Innovative Aspects: CUS-Sim Cloud Platform

The practical impact of the advanced modeling capabilities developed in this project will be maximized through the creation of "CUS-Sim Cloud," a web-based computational platform that makes sophisticated multiphysics modeling accessible to a broad range of stakeholders.

**Cloud-Based Architecture:** The CUS-Sim platform will be built on a modern cloud computing architecture that provides scalable computational resources and enables access from any internet-connected device. The platform will leverage containerization technologies to ensure consistent performance across different computing environments and will implement sophisticated load balancing and resource management strategies to handle multiple concurrent users efficiently (Armbrust et al., 2010).

**Interactive Visualization and Exploration:** The platform will feature advanced interactive visualization capabilities that enable users to explore simulation results in real-time, adjust parameters dynamically, and visualize the consequences of different scenarios immediately. The visualization tools will be designed to communicate complex multiphysics results effectively to both technical and non-technical audiences, using techniques such as immersive 3D rendering, animated time series, and interactive parameter sensitivity analysis (Sedlmair et al., 2012).

**Stakeholder-Specific Interfaces:** CUS-Sim will provide customized interfaces tailored to the specific needs of different stakeholder groups, including project developers, regulatory agencies, and the general public. Each interface will present relevant information in an appropriate format and level of detail, ensuring that all stakeholders can access the information they need to make informed decisions about CUS and EGS projects (Nielsen, 1994).

**Integration with Monitoring Systems:** The platform will be designed to integrate seamlessly with real-time monitoring systems, enabling continuous model updating and validation against field observations. This capability will support adaptive management strategies and provide early warning of potential issues, enhancing the safety and reliability of CUS and EGS operations (Sun et al., 2013).

The combination of advanced surrogate modeling techniques and an accessible cloud-based platform will bridge the gap between cutting-edge scientific modeling and practical application, enabling the widespread deployment of CUS and EGS technologies while maintaining the highest standards of safety and environmental protection.

## 1.2.4 Connection to Research Objectives and Innovation Summary

The comprehensive review of the state of the art presented above clearly demonstrates the critical need for the innovative approaches proposed in this project. The three specific objectives (SO1, SO2, and SO3) directly address the identified knowledge gaps and technological limitations through a coordinated research strategy that advances the field on multiple fronts simultaneously.

**SO1 Innovation - Modular Multiphysics Framework:** The development of a modular staggered coupling framework addresses the fundamental limitations of existing simulation tools by providing unprecedented flexibility, extensibility, and numerical robustness. The integration of five specialized modules (MFEM, FEM, poroelasticity, SGBEM, and phase-field) within a unified platform represents a significant advance over current monolithic or loosely coupled approaches. The implementation of advanced coupling strategies, including fixed-stress splitting and operator splitting, ensures numerical stability while maintaining computational efficiency.

**SO2 Innovation - Surrogate-Driven Risk Assessment:** The development of KAE-based surrogate models addresses the critical computational barrier that prevents the use of high-fidelity models for uncertainty quantification and risk assessment. The combination of rigorous Koopman operator theory with practical deep learning techniques provides a scientifically sound approach to dimensionality reduction that preserves the essential physics of the system while achieving dramatic computational speedup.

**SO3 Innovation - Accessible Computational Platform:** The creation of the CUS-Sim Cloud platform addresses the critical gap between advanced scientific modeling and practical stakeholder engagement. By making sophisticated multiphysics modeling accessible through a user-friendly web interface, this project will democratize access to advanced modeling capabilities and facilitate informed decision-making by all stakeholders.

The synergistic combination of these three innovations will create a transformative computational framework that advances the scientific understanding of CUS and EGS systems while providing practical tools for their safe and efficient deployment. This project represents a paradigm shift from traditional academic modeling approaches toward integrated, accessible, and practically relevant computational tools that can support the widespread adoption of these critical climate technologies.

## References

Armbrust, M., Fox, A., Griffith, R., Joseph, A. D., Katz, R., Konwinski, A., ... & Zaharia, M. (2010). A view of cloud computing. *Communications of the ACM*, 53(4), 50-58.

Balay, S., Abhyankar, S., Adams, M. F., Brown, J., Brune, P., Buschelman, K., ... & Zhang, H. (2019). PETSc users manual. Argonne National Laboratory.

Bangerth, W., Hartmann, R., & Kanschat, G. (2007). deal.II—a general-purpose object-oriented finite element library. *ACM Transactions on Mathematical Software*, 33(4), 24-es.

Berre, I., Doster, F., & Keilegavlen, E. (2019). Flow in fractured porous media: A review of conceptual models and discretization approaches. *Transport in Porous Media*, 130(1), 215-236.

Bielicki, J. M., Pollak, M. F., Fitts, J. P., Peters, C. A., & Wilson, E. J. (2014). Causes and financial consequences of geologic CO2 storage reservoir leakage and interference with other subsurface resources. *Environmental Science & Technology*, 48(1), 4491-4501.

Bonnet, M., Maier, G., & Polizzotto, C. (1998). Symmetric Galerkin boundary element methods. *Applied Mechanics Reviews*, 51(11), 669-704.

Bourdin, B., Francfort, G. A., & Marigo, J. J. (2000). Numerical experiments in revisited brittle fracture. *Journal of the Mechanics and Physics of Solids*, 48(4), 797-826.

Brunton, S. L., Budišić, M., Kaiser, E., & Kutz, J. N. (2022). Modern Koopman theory for dynamical systems. *SIAM Review*, 64(2), 229-340.

Cahn, J. W., & Hilliard, J. E. (1958). Free energy of a nonuniform system. I. Interfacial free energy. *The Journal of Chemical Physics*, 28(2), 258-267.

Champion, K., Lusch, B., Kutz, J. N., & Brunton, S. L. (2019). Data-driven discovery of coordinates and governing equations. *Proceedings of the National Academy of Sciences*, 116(45), 22445-22451.

Computer Modelling Group. (2020). STARS User Guide. Computer Modelling Group Ltd.

Coussy, O. (2004). *Poromechanics*. John Wiley & Sons.

Flemisch, B., Darcis, M., Erbertseder, K., Faigle, B., Lauser, A., Mosthaf, K., ... & Helmig, R. (2011). DuMux: DUNE for multi-{phase, component, scale, physics, ...} flow and transport in porous media. *Advances in Water Resources*, 34(9), 1102-1112.

Francfort, G. A., & Marigo, J. J. (1998). Revisiting brittle fracture as an energy minimization problem. *Journal of the Mechanics and Physics of Solids*, 46(8), 1319-1342.

Gal, Y., & Ghahramani, Z. (2016). Dropout as a Bayesian approximation: Representing model uncertainty in deep learning. *International Conference on Machine Learning*, 1050-1059.

Gaston, D., Newman, C., Hansen, G., & Lebrun-Grandié, D. (2009). MOOSE: A parallel computational framework for coupled systems of nonlinear equations. *Nuclear Engineering and Design*, 239(10), 1768-1778.

Gaus, I., Azaroual, M., & Czernichowski-Lauriol, I. (2005). Reactive transport modelling of the impact of CO2 injection on the clayey cap rock at Sleipner (North Sea). *Chemical Geology*, 217(3-4), 319-337.

Gear, C. W., & Wells, D. R. (1984). Multirate linear multistep methods. *BIT Numerical Mathematics*, 24(4), 484-502.

Gherardi, F., Xu, T., & Pruess, K. (2007). Numerical modeling of self-limiting and self-enhancing caprock alteration induced by CO2 storage in a depleted gas reservoir. *Chemical Geology*, 244(1-2), 103-129.

Hageman, T., & Martínez-Pañeda, E. (2023). A phase field-based framework for electro-chemo-mechanical fracture: Crack-contained electrolytes, chemical reactions and stabilisation. *Computer Methods in Applied Mechanics and Engineering*, 415, 116235.

Huerta, N. J., Hesse, M. A., Bryant, S. L., Strazisar, B. R., & Lopano, C. (2013). Experimental evidence for self-limiting reactive flow through a fractured cement core: Implications for time-dependent wellbore leakage. *Environmental Science & Technology*, 47(1), 269-275.

Kim, J., Tchelepi, H. A., & Juanes, R. (2011). Stability and convergence of sequential methods for coupled flow and geomechanics: Fixed-stress and fixed-strain splits. *Computer Methods in Applied Mechanics and Engineering*, 200(13-16), 1591-1606.

Klinsmann, M., Rosato, D., Kamlah, M., & McMeeking, R. M. (2016). Modeling crack growth during Li insertion in storage particles using a fracture phase field approach. *Journal of the Mechanics and Physics of Solids*, 92, 313-344.

Kolditz, O., Bauer, S., Bilke, L., Böttcher, N., Delfs, J. O., Fischer, T., ... & Watanabe, N. (2012). OpenGeoSys: an open-source initiative for numerical simulation of thermo-hydro-mechanical/chemical (THM/C) processes in porous media. *Environmental Earth Sciences*, 67(2), 589-599.

Koopman, B. O. (1931). Hamiltonian systems and transformation in Hilbert space. *Proceedings of the National Academy of Sciences*, 17(5), 315-318.

Logg, A., Mardal, K. A., & Wells, G. (2012). *Automated solution of differential equations by the finite element method: The FEniCS book* (Vol. 84). Springer Science & Business Media.

Lusch, B., Kutz, J. N., & Brunton, S. L. (2018). Deep learning for universal linear embeddings of nonlinear dynamics. *Nature Communications*, 9(1), 4950.

Mezić, I. (2005). Spectral properties of dynamical systems, model reduction and decompositions. *Nonlinear Dynamics*, 41(1-3), 309-325.

Miehe, C., Hofacker, M., & Welschinger, F. (2010). A phase field model for rate-independent crack propagation: Robust algorithmic implementation based on operator splits. *Computer Methods in Applied Mechanics and Engineering*, 199(45-48), 2765-2778.

Miehe, C., Mauthe, S., & Teichtmeister, S. (2015). Minimization principles for the coupled problem of Darcy–Biot-type fluid transport in porous media linked to phase field modeling of fracture. *Journal of the Mechanics and Physics of Solids*, 82, 186-217.

Molins, S., Trebotich, D., Steefel, C. I., & Shen, C. (2012). An investigation of the effect of pore scale flow on average geochemical reaction rates using direct numerical simulation. *Water Resources Research*, 48(3).

Nielsen, J. (1994). *Usability engineering*. Morgan Kaufmann.

Nordbotten, J. M., Kavetski, D., Celia, M. A., & Bachu, S. (2009). A semi-analytical model for large-scale injection of CO2 into aquifers. *Transport in Porous Media*, 80(2), 269-294.

Parkhurst, D. L., & Wissmeier, L. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC. *Advances in Water Resources*, 83, 176-189.

Pawar, R. J., Bromhal, G. S., Carey, J. W., Foxall, W., Korre, A., Ringrose, P. S., ... & Zhang, Y. (2015). Recent advances in risk assessment and risk management of geologic CO2 storage. *International Journal of Greenhouse Gas Control*, 40, 292-311.

Peherstorfer, B., Willcox, K., & Gunzburger, M. (2018). Survey of multifidelity methods in uncertainty propagation, inference, and optimization. *SIAM Review*, 60(3), 550-591.

Pruess, K., Oldenburg, C., & Moridis, G. (1999). TOUGH2 user's guide version 2. Lawrence Berkeley National Laboratory.

Raviart, P. A., & Thomas, J. M. (1977). A mixed finite element method for 2-nd order elliptic problems. In *Mathematical aspects of finite element methods* (pp. 292-315). Springer.

Rutqvist, J. (2011). Status of the TOUGH-FLAC simulator and recent applications related to coupled fluid flow and crustal deformations. *Computers & Geosciences*, 37(6), 739-750.

Rutqvist, J., Wu, Y. S., Tsang, C. F., & Bodvarsson, G. (2002). A modeling approach for analysis of coupled multiphase fluid flow, heat transfer, and deformation in fractured porous rock. *International Journal of Rock Mechanics and Mining Sciences*, 39(4), 429-442.

Schlumberger. (2020). ECLIPSE reservoir simulation software. Schlumberger Limited.

Sedlmair, M., Meyer, M., & Munzner, T. (2012). Design study methodology: Reflections from the trenches and the stacks. *IEEE Transactions on Visualization and Computer Graphics*, 18(12), 2431-2440.

Settgast, R. R., Johnson, S. M., Walsh, S. D., Morris, J. P., Ryerson, F. J., & Friedmann, S. J. (2017). A fully coupled method for massively parallel simulation of hydraulically driven fractures in 3-dimensions. *International Journal for Numerical and Analytical Methods in Geomechanics*, 41(5), 627-653.

Steefel, C. I., Appelo, C. A. J., Arora, B., Jacques, D., Kalbacher, T., Kolditz, O., ... & Yeh, G. T. (2015). Reactive transport codes for subsurface environmental simulation. *Computational Geosciences*, 19(3), 445-478.

Stephens, J. C., Rand, G. M., & Melnick, L. L. (2009). Wind energy in US media: A comparative state-level analysis of a critical climate change mitigation technology. *Global Environmental Change*, 19(1), 89-97.

Sun, A. Y., Morris, A. P., & Mohanty, S. (2013). Sequential updating of multimodal hydrogeologic parameter fields using localization and clustering techniques. *Water Resources Research*, 49(10), 7204-7220.

Taron, J., & Elsworth, D. (2009). Thermal–hydrologic–mechanical–chemical processes in the evolution of engineered geothermal systems. *International Journal of Rock Mechanics and Mining Sciences*, 46(5), 855-864.

Taron, J., Elsworth, D., & Min, K. B. (2009). Numerical simulation of thermal-hydrologic-mechanical-chemical processes in deformable, fractured porous media. *International Journal of Rock Mechanics and Mining Sciences*, 46(5), 842-854.

Wheeler, M. F., & Yotov, I. (2006). A multipoint flux mixed finite element method. *SIAM Journal on Numerical Analysis*, 44(5), 2082-2106.

White, M. D., Oostrom, M., & Lenhard, R. J. (2004). Modeling fluid flow and transport in variably saturated porous media with the STOMP simulator. 1. Nonvolatile three-phase system. *Advances in Water Resources*, 27(1), 83-95.

Xu, T., Apps, J. A., & Pruess, K. (2005). Mineral sequestration of carbon dioxide in a sandstone–shale system. *Chemical Geology*, 217(3-4), 295-318.

Yasuhara, H., Polak, A., Mitani, Y., Grader, A. S., Halleck, P. M., & Elsworth, D. (2006). Evolution of fracture permeability through fluid–rock reaction under hydrothermal conditions. *Earth and Planetary Science Letters*, 244(1-2), 186-200.

Zimmerman, R. W., & Bodvarsson, G. S. (1996). Hydraulic conductivity of rock fractures. *Transport in Porous Media*, 23(1), 1-30.

Zyvoloski, G. A., Robinson, B. A., Dash, Z. V., & Trease, L. L. (1997). Summary of the models and methods for the FEHM application-a finite-element heat-and mass-transfer code. Los Alamos National Laboratory.
