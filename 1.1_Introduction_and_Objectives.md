# 1.1 Introduction and Objectives

## 1.1 Introduction and objectives

The European Union is at the forefront of the global effort to combat climate change, with the ambitious goal of achieving climate neutrality by 2050 as outlined in the European Green Deal. Carbon Capture, Utilisation, and Storage (CUS) is recognized by the Intergovernmental Panel on Climate Change (IPCC) and the European Commission as a critical component in the portfolio of mitigation actions to provide a strategic bridge away from fossil fuels and decarbonize hard-to-abate industries. To achieve the necessary scale, Europe must develop a robust portfolio of geological storage sites. For the EU, developing a robust CUS infrastructure, particularly for onshore geological storage, is not just an environmental necessity but a strategic one. It offers increased flexibility, reduced costs, and the ability for Member States to manage their decarbonisation strategies at a territorial level, thereby enhancing energy security and fostering local economic activity.

However, the success of onshore CUS hinges on one overriding factor: public acceptance, which is contingent on demonstrating uncompromising long-term safety and storage permanence. When CO2 is injected deep underground into saline aquifers or depleted hydrocarbon reservoirs, it exists in a supercritical state. Over time, this supercritical CO2 interacts with the surrounding rock and brine, triggering a cascade of coupled Hydro-Chemo-Mechanical (HCM) processes. These interactions, involving mineral dissolution and precipitation, can alter the fundamental properties of the reservoir rock including its porosity, permeability, and mechanical strength. These alterations, in turn, can affect the integrity of the caprock seal, and potentially create new pathways for CO2 to leak back into the atmosphere. Furthermore, the dual-purpose application of CO2 injection, for both storage and as in Enhanced Geothermal Systems (EGS), introduces additional complexities. While CO2 offers thermodynamic advantages over water in EGS, the long-term chemo-mechanical consequences of cycling CO2 in these systems are largely unknown.

Current computational models often struggle to capture the full complexity of these coupled multi-physics phenomena over the vast timescales (e.g. across decadal, centennial, and millennial timescales) relevant for geological storage. They either oversimplify the chemical-mechanical couplings or are too computationally expensive to be used for the large number of simulations required for robust uncertainty quantification and risk assessment. This represents a critical knowledge gap that hinders our ability to confidently select, operate, and monitor CUS and EGS sites. This project is directly motivated by the need to fill this gap by developing a next-generation computational framework capable of accurately and efficiently predicting the long-term fate of stored CO2, thereby paving the way for the safe and widespread deployment of CUS technologies across Europe.

**Overall Objective:**
The overall objective of this project is to develop a novel, robust, and computationally efficient modeling framework to predict the long-term multi-physical evolution of geological reservoirs subjected to CO2 injection for both storage and Enhanced Geothermal Systems (EGS) applications. This ambitious goal will be achieved through the creation of a modular, staggered simulation scheme that integrates high-fidelity models for the key physical processes governing reservoir behaviour. The framework will be built upon a flexible, open-source platform, allowing for the seamless coupling of different physical modules to address the specific challenges of CUS and EGS.

A cornerstone of this project will be the use of a novel staggered scheme to model the complex chemo-hydro-mechanical interactions and fracture propagation within the reservoir. This cutting-edge technique is particularly well-suited for capturing the intricate interplay between chemical reactions (dissolution and precipitation) and mechanical deformation, which is crucial for assessing the long-term integrity of the caprock and the risk of induced fracturing.

Furthermore, the project recognizes that high-fidelity simulations are often too slow for the extensive parametric studies needed for risk assessment and optimization. To overcome this limitation, this project will develop fast and accurate surrogate models from the simulation data. These surrogate models will capture the essential dynamics of the complex HCM system at a fraction of the computational cost, enabling comprehensive uncertainty quantification and robust risk analysis. This project will deliver a transformative tool for the design, operation, and monitoring of CUS and EGS projects, ultimately contributing to their safe and efficient deployment as key technologies in the fight against climate change.

**Specific Objectives**
To achieve the overall objective, this project is structured around three specific, interconnected objectives (SOs):

**SO1:** To develop a modular, fully coupled hydro-chemo-mechanical (HCM) numerical framework for fractured porous media. This objective focuses on the creation of the core simulation tool. It involves the development and integration of five distinct modules within a staggered coupling scheme: (1) a Mixed Finite Element Method (MFEM) module for Darcy's flow and mass conservation; (2) a Finite Element Method (FEM) module for Reynolds' lubrication in fractures; (3) a module for the mechanics of poroelastic media; (4) a Symmetric Galerkin Boundary Element Method (SGBEM) module for fracture mechanics; and (5) a phase-field module for advection-diffusion-reaction processes. The coupling between the fluid and solid mechanics will be achieved through a robust fixed-stress splitting scheme, while the reactive transport equations will be solved efficiently using operator splitting. The successful completion of this objective will result in a novel and powerful open-source simulation platform capable of capturing the complex, coupled physics of CUS and EGS with high fidelity.

**SO2:** To quantify long-term chemo-mechanical risks by developing a surrogate-model-driven assessment framework. This objective will use the high-fidelity model (SO1) to simulate CO2-rock-brine interactions and generate a comprehensive dataset on long-term reservoir evolution. This data will then be used to train a computationally efficient Koopman Autoencoder (KAE) surrogate model. The resulting KAE will predict reservoir behavior with unprecedented speed, enabling comprehensive risk assessment of potential leakage through extensive simulations. This provides a transformative tool for ensuring the long-term safety and public acceptance of CUS and EGS projects.

**SO3:** To develop and deploy "CUS sim Cloud," a web-based computational platform for long-term risk assessment and public engagement for CUS/EGS projects. This objective translates the scientific advances from SO1 into a tangible, high-impact tool. Using the HPC framework, a comprehensive database of long-term CUS/EGS evolution scenarios will be generated. CUS-Sim will serve a dual purpose: a) as a powerful tool for stakeholders (engineers, regulators) to perform rapid uncertainty quantification, optimize injection strategies, and assess long-term risks (e.g., leakage); and b) as a transparent and interactive tool for the public to visualize the long-term security of proposed projects, fostering understanding and building confidence.

# 1.2 State of the art and innovative aspects of the project

The successful deployment of Carbon Utilization and Storage (CUS) and Enhanced Geothermal Systems (EGS) using CO₂ as a working fluid requires a fundamental understanding of the complex, coupled hydro-chemo-mechanical (HCM) processes that govern the long-term evolution of geological reservoirs. This section presents a comprehensive review of the current state of the art in modeling these multiphysics phenomena, identifies critical knowledge gaps, and demonstrates how this project's innovative approach addresses these limitations through three key technological advances: (1) modular simulation frameworks for multiphysics coupling, (2) advanced modeling for long-term chemo-mechanical damage, and (3) computationally efficient approaches for practical applications.

## 1.2.1 The Need for Modular, Efficient Simulation Frameworks

### Current State of the Art

The complexity of CUS and EGS systems necessitates the coupling of multiple physical processes, each governed by different mathematical formulations and operating on distinct temporal and spatial scales. Current simulation tools for subsurface multiphysics problems can be broadly classified into three categories: monolithic codes, loosely coupled frameworks, and modular coupling platforms. Monolithic simulators, such as TOUGH2 (Pruess et al., 1999), ECLIPSE (Schlumberger, 2020), and CMG-STARS (Computer Modelling Group, 2020), integrate multiple physical processes within a single computational framework, offering computational efficiency and numerical stability for well-established coupling schemes but suffering from significant limitations in terms of flexibility and extensibility (Flemisch et al., 2011). Loosely coupled approaches, exemplified by codes such as TOUGH-FLAC (Rutqvist, 2011) and FEHM-ABAQUS (Zyvoloski et al., 2008), attempt to leverage the strengths of specialized codes by coupling them through external interfaces, but introduce significant challenges in terms of data transfer, temporal synchronization, and numerical stability due to file-based communication or simplified interface conditions (White et al., 2016). Recent advances have led to the development of modular coupling platforms such as MOOSE (Gaston et al., 2009), FEniCS (Logg et al., 2012), and deal.II (Bangerth et al., 2007), which provide sophisticated infrastructure for coupling different physics modules while maintaining flexibility and extensibility, though most existing platforms focus on traditional finite element methods and lack the specialized capabilities required for advanced subsurface applications, such as boundary element methods for fracture mechanics or phase-field methods for reactive transport.

### Critical Gaps

Despite significant progress in multiphysics simulation technology, several critical limitations persist that hinder the development of comprehensive CUS and EGS models. Most existing frameworks rely on simplified representations of fractures, such as discrete fracture networks or embedded discontinuities, which struggle to capture the complex evolution of fracture systems, particularly the initiation of new fractures and the interaction between multiple fracture sets, severely limiting the ability to predict the long-term integrity of storage formations and the performance of EGS systems (Settgast et al., 2017). Current frameworks are predominantly based on standard finite element or finite difference methods, with limited support for specialized techniques such as mixed finite element methods (MFEM) for flow problems, symmetric Galerkin boundary element methods (SGBEM) for fracture mechanics, or phase-field methods for reactive transport, constraining the ability to leverage the most appropriate numerical techniques for different physical processes (Berre et al., 2019). The vast range of time scales involved in CUS and EGS systems poses significant challenges for temporal coupling, with most existing frameworks relying on simple explicit or implicit coupling schemes that are either unstable for fast processes or inefficient for slow processes, while advanced temporal coupling strategies such as multirate methods or adaptive time stepping are rarely implemented in a robust and general manner (Gear and Wells, 1984). Additionally, the computational demands of high-fidelity multiphysics simulations often exceed the capabilities of existing frameworks, particularly when dealing with large-scale three-dimensional problems or long-term evolution studies, as many frameworks lack the sophisticated parallel computing capabilities and memory management strategies required for efficient execution on modern high-performance computing systems (Balay et al., 2019).

### Innovative Aspects of This Project

This project addresses these fundamental limitations through the development of a novel modular staggered coupling framework that integrates five specialized numerical modules within a unified computational platform, representing a significant advance over current monolithic or loosely coupled approaches. The framework combines a Mixed Finite Element Method (MFEM) module for Darcy flow and mass conservation in porous media, implementing advanced mixed formulations based on Raviart-Thomas elements (Raviart and Thomas, 1977) that provide superior mass conservation properties and accurate velocity fields compared to standard finite element methods, while handling complex fluid properties including the equation of state for CO₂ under supercritical conditions (Wheeler and Yotov, 2006). A specialized Finite Element Method (FEM) module will solve the Reynolds lubrication equation in fractures, accounting for the complex geometry of rough fracture surfaces and the effects of aperture variations on fluid flow, incorporating advanced contact mechanics algorithms to handle fracture closure and opening under varying stress conditions (Zimmerman and Bodvarsson, 1996). The mechanical behavior of the reservoir will be modeled using advanced poroelasticity formulations that account for the coupling between fluid pressure and mechanical deformation, implementing both linear and nonlinear constitutive models, including plasticity and damage mechanics, to capture the complex mechanical response of geological materials under varying stress and chemical conditions (Coussy, 2004). A cutting-edge Symmetric Galerkin Boundary Element Method (SGBEM) module will be developed specifically for fracture mechanics applications, offering significant advantages over traditional finite element methods for fracture problems, including the ability to handle infinite domains without artificial boundary conditions and superior accuracy for stress intensity factor calculations, with advanced features such as adaptive mesh refinement, fast multipole acceleration, and parallel computing capabilities to handle large-scale fracture systems efficiently (Bonnet et al., 1998). The phase-field module will implement state-of-the-art formulations for two-phase two-component advection-diffusion-reaction processes, where the phase-field variable tracks the interface between two phases, incorporating advanced numerical techniques such as operator splitting for handling the different time scales of advection, diffusion, and reaction processes, as well as adaptive time stepping algorithms for maintaining numerical stability and accuracy (Miehe et al., 2015). The integration of these five modules will be achieved through a sophisticated staggered coupling strategy based on a fixed-stress splitting scheme for the fluid-solid interaction, which has been proven to be unconditionally stable and efficient for poroelasticity problems (Kim et al., 2011), while for the reactive transport coupling, an operator splitting approach will be employed to handle the different time scales of transport and reaction processes efficiently, with the modular design providing unprecedented flexibility for researchers to adapt the code to specific applications, incorporate new physical processes, or experiment with different numerical methods.

## 1.2.2 The Challenge of Modeling Long-Term Chemo-Mechanical Damage

### Current State of the Art

The injection of CO₂ into geological formations triggers a complex cascade of long-term chemo-mechanical processes that fundamentally alter the physical and chemical properties of reservoir rocks over operational timescales spanning decades to centuries. The acidic nature of CO₂-saturated brine (pH typically ranging from 3.5 to 5.5) drives mineral dissolution reactions, particularly affecting carbonate minerals such as calcite (CaCO₃) and dolomite (CaMg(CO₃)₂), while simultaneously promoting the precipitation of secondary minerals including clays and zeolites, creating a feedback loop with mechanical processes where chemical weakening of the rock matrix leads to stress redistribution, potential fracture initiation, and altered permeability pathways (Gaus et al., 2005; Xu et al., 2005). Current approaches to modeling long-term chemo-mechanical damage rely primarily on continuum damage mechanics frameworks that treat chemical degradation as a gradual reduction in material properties through empirical damage variables, as exemplified by the work of Yasuhara et al. (2006) and Huerta et al. (2013), where chemical dissolution is coupled to mechanical weakening through phenomenological relationships that lack the physical rigor necessary for long-term predictions. Advanced chemo-mechanical models, such as those developed by Taron and Elsworth (2009) and implemented in codes like OpenGeoSys (Kolditz et al., 2012), attempt to capture the coupling between geochemical reactions and mechanical deformation through iterative solution schemes, but these approaches often suffer from numerical instabilities and convergence issues when dealing with strong coupling effects or rapid chemical reactions over extended time periods. Recent developments in chemo-mechanical modeling have focused on incorporating more sophisticated geochemical reaction networks using specialized codes such as PHREEQC (Parkhurst and Appelo, 2013) coupled with mechanical solvers, but these approaches typically treat the chemical and mechanical processes as loosely coupled phenomena with limited bidirectional feedback, failing to capture the fundamental thermodynamic consistency required for accurate long-term predictions (Steefel et al., 2015).

### Critical Gaps

Despite significant advances in chemo-mechanical modeling, several critical knowledge gaps persist that limit our ability to predict long-term reservoir behavior accurately under CO₂ injection conditions. Current models struggle to predict the initiation and propagation of fractures driven by chemical weakening processes, as traditional approaches rely on empirical failure criteria that do not adequately capture the gradual degradation of material properties due to chemical reactions, with the challenge compounded by the heterogeneous nature of geological formations where local variations in mineralogy and stress state can lead to preferential dissolution pathways and unexpected fracture patterns that are not captured by conventional continuum approaches (Molins et al., 2012). The coupling between pore-scale chemical reactions and continuum-scale mechanical behavior remains poorly understood, as chemical reactions occur at the mineral-fluid interface (nanometer scale) while their mechanical consequences manifest at the reservoir scale (kilometer scale), requiring sophisticated homogenization techniques that are currently underdeveloped for reactive systems and cannot adequately bridge the vast scale separation inherent in chemo-mechanical processes (Gherardi et al., 2007). Most existing models are validated against short-term laboratory experiments (days to months), but CUS and EGS systems must operate safely for decades to centuries, with the extrapolation of short-term behavior to long-term evolution involving significant uncertainties, particularly regarding the evolution of reaction rates and the potential for new reaction pathways to emerge over time as the chemical and physical properties of the reservoir evolve (White et al., 2005). Additionally, current modeling approaches lack the ability to capture the complex interplay between chemical reactions and fracture mechanics, as most frameworks treat fractures as pre-existing discrete features with fixed properties rather than as evolving interfaces whose geometry, aperture, and connectivity are continuously modified by ongoing chemical processes, leading to significant underestimation of long-term permeability evolution and potential leakage pathways (Rutqvist, 2012).

### Innovative Aspects of This Project

This project addresses these fundamental limitations through the implementation of a cutting-edge phase-field approach for modeling long-term chemo-mechanical damage that provides a thermodynamically consistent framework for coupling chemical reactions with mechanical deformation over extended time periods. The phase-field approach, building upon the expertise of the host supervisor Emilio Martínez-Pañeda in chemo-mechanical fracture modeling (Hageman and Martínez-Pañeda, 2023), offers revolutionary advantages over traditional methods by representing chemical damage as a continuous field variable that evolves according to thermodynamic principles, naturally capturing the gradual transition from intact rock to fully damaged material without the artificial discontinuities inherent in traditional failure criteria. Unlike discrete fracture models that require explicit tracking of crack surfaces and suffer from topological limitations, phase-field methods represent fractures and chemical damage as diffuse interfaces characterized by continuous field variables, enabling natural handling of complex damage patterns including branching, coalescence, and interaction with heterogeneities without the need for remeshing or special interface tracking algorithms (Miehe et al., 2015). The phase-field framework provides a natural platform for coupling chemical reactions with mechanical damage through a unified energy functional that incorporates both chemical and mechanical driving forces, where chemical dissolution can be directly linked to the evolution of the damage field variable, creating a physically consistent representation of chemically-induced weakening that captures the fundamental thermodynamic coupling between chemical and mechanical processes (Klinsmann et al., 2016). The implementation will be enhanced by integration with PhreeqcRM, a state-of-the-art geochemical reaction module developed by the United States Geological Survey (Parkhurst and Wissmeier, 2015), which provides a comprehensive database of thermodynamic and kinetic parameters for mineral-water reactions under reservoir conditions, enabling accurate prediction of reaction rates and equilibrium states while maintaining computational efficiency through advanced operator splitting schemes that preserve the physical coupling between processes. To address the computational challenges associated with detailed geochemical modeling over long time periods, this project will develop advanced surrogate models for chemical reactions using machine learning techniques, where high-fidelity PhreeqcRM simulations will be used to generate comprehensive datasets of reaction behavior under various conditions (temperature, pressure, fluid composition, mineral assemblage), which will then be used to train neural network-based surrogate models that can predict reaction rates and products with high accuracy but at a fraction of the computational cost, enabling the integration of detailed geochemical modeling into large-scale reservoir simulations for long-term evolution studies without prohibitive computational overhead.

## 1.2.3 The Computational Barrier to Practical Application

### Current State of the Art

While significant advances have been made in developing sophisticated multiphysics models for CUS and EGS systems, a fundamental disconnect exists between the scientific capabilities of these models and their practical application for decision-making and operational deployment. High-fidelity coupled HCM simulations, while scientifically rigorous, typically require substantial computational resources and execution times that range from hours to weeks for a single scenario analysis, creating a significant barrier to their use in practical applications where rapid scenario evaluation and interactive exploration of design parameters are essential (Steefel et al., 2015). State-of-the-art coupled HCM models involve the simultaneous solution of multiple nonlinear partial differential equations with strong coupling terms and vastly different characteristic time scales, with computational complexity further exacerbated by the need for fine spatial discretization to capture local phenomena such as reaction fronts and stress concentrations, as well as long temporal integration periods to assess long-term behavior, resulting in typical three-dimensional reservoir-scale simulations with detailed geochemical modeling requiring millions of degrees of freedom and thousands of time steps (Nordbotten et al., 2009). Current phase-field approaches for fracture modeling, while theoretically superior for capturing complex fracture evolution, suffer from extremely low computational efficiency due to the requirement for very fine mesh resolution to accurately resolve the diffuse fracture interface, with typical phase-field simulations requiring mesh sizes on the order of the phase-field length scale, leading to computational costs that scale prohibitively with problem size and making them impractical for large-scale reservoir applications (Miehe et al., 2010). Existing multiphysics frameworks often rely on monolithic solution approaches that solve all coupled equations simultaneously, leading to large, ill-conditioned systems of equations that require sophisticated preconditioners and iterative solvers, with convergence rates that deteriorate significantly as the coupling strength increases or as the problem size grows, making them unsuitable for the extensive parametric studies required for robust system design and optimization (Flemisch et al., 2011).

### Critical Gaps

Several critical gaps exist between current scientific modeling capabilities and the practical needs of CUS and EGS deployment that limit the widespread adoption of advanced modeling tools. Current modeling workflows typically involve offline simulation runs followed by post-processing and analysis, making them unsuitable for real-time decision support during project operation, with the inability to rapidly evaluate the consequences of operational changes or unexpected events limiting the effectiveness of adaptive management strategies and increasing operational risks (Pawar et al., 2015). Most existing models operate as forward prediction tools with limited capability for integration with real-time monitoring data, lacking robust data assimilation and model updating capabilities that would enable models to be easily calibrated or validated against field observations, reducing confidence in their predictions and limiting their utility for operational decision-making (Sun et al., 2013). The complexity of current modeling tools requires specialized expertise in numerical methods, high-performance computing, and subsurface physics, making them inaccessible to many stakeholders who need to use model results for decision-making, with the lack of user-friendly interfaces and automated workflows creating a significant barrier to the widespread adoption of advanced modeling capabilities (Bielicki et al., 2014). Current modeling platforms typically provide limited visualization capabilities and lack the sophisticated tools necessary for effective communication of complex multiphysics results to diverse stakeholder groups, with the inability to create compelling, interactive visualizations that clearly communicate model predictions hampering public engagement and regulatory acceptance of CUS and EGS technologies (Stephens et al., 2009). Additionally, the high computational cost of detailed HCM models severely limits their applicability for extensive scenario analysis, which requires hundreds or thousands of simulation runs to adequately explore the parameter space and assess system performance under various operating conditions, forcing researchers to rely on simplified models or reduced parameter sets that may not capture the full complexity of the system (White et al., 2004).

### Innovative Aspects of This Project

This project addresses these fundamental limitations through the development of a computationally efficient modeling framework that combines the accuracy of high-fidelity multiphysics simulation with the speed required for practical applications, representing a paradigm shift from traditional computationally expensive approaches to efficient, accessible modeling tools. The key innovation lies in the strategic combination of Symmetric Galerkin Boundary Element Method (SGBEM) for fracture mechanics with other specialized numerical methods, which provides dramatic computational advantages over traditional phase-field approaches for fracture modeling by eliminating the need for fine mesh resolution throughout the entire domain and instead requiring discretization only on fracture surfaces, reducing the computational complexity from three-dimensional volume problems to two-dimensional surface problems and achieving orders of magnitude improvement in computational efficiency (Bonnet et al., 1998). The SGBEM approach offers significant advantages for fracture problems by naturally handling infinite domains without artificial boundary conditions, providing superior accuracy for stress intensity factor calculations, and enabling efficient treatment of multiple interacting fractures without the topological limitations of discrete fracture network approaches, while maintaining the physical rigor necessary for accurate long-term predictions (Aliabadi, 2002). The modular staggered coupling strategy developed in this project leverages the computational strengths of each numerical method while avoiding the computational penalties associated with monolithic approaches, with the fixed-stress splitting scheme for fluid-solid coupling providing unconditional stability and enabling larger time steps compared to traditional iterative coupling schemes, while the operator splitting approach for reactive transport allows for efficient handling of the different time scales of transport and reaction processes (Kim et al., 2011). To further enhance computational efficiency, this project will develop advanced surrogate modeling techniques based on Koopman Autoencoders (KAEs), a cutting-edge machine learning approach that combines the theoretical rigor of Koopman operator theory with the practical capabilities of deep neural networks to learn low-dimensional representations of high-dimensional multiphysics systems (Lusch et al., 2018). The KAE approach provides a rigorous mathematical framework for analyzing the dynamics of nonlinear systems by embedding them in an infinite-dimensional linear space, enabling the identification of coherent structures and dominant modes in the system dynamics while constraining the latent space dynamics to follow linear evolution equations, allowing accurate long-term prediction with minimal computational cost (Brunton et al., 2022). The practical impact of these advanced modeling capabilities will be maximized through the creation of "CUS-Sim Cloud," a web-based computational platform built on modern cloud computing architecture that provides scalable computational resources and enables access from any internet-connected device, featuring advanced interactive visualization capabilities that enable users to explore simulation results in real-time, adjust parameters dynamically, and visualize the consequences of different scenarios immediately, with customized interfaces tailored to the specific needs of different stakeholder groups including project developers, regulatory agencies, and the general public (Armbrust et al., 2010). The platform will be designed to integrate seamlessly with real-time monitoring systems, enabling continuous model updating and validation against field observations to support adaptive management strategies and provide early warning of potential issues, thereby bridging the gap between cutting-edge scientific modeling and practical application while enabling the widespread deployment of CUS and EGS technologies with enhanced safety and environmental protection (Sedlmair et al., 2012).

## 1.2.4 Connection to Research Objectives and Innovation Summary

The comprehensive review of the state of the art presented above clearly demonstrates the critical need for the innovative approaches proposed in this project. The three specific objectives (SO1, SO2, and SO3) directly address the identified knowledge gaps and technological limitations through a coordinated research strategy that advances the field on multiple fronts simultaneously.

**SO1 Innovation - Modular Multiphysics Framework:** The development of a modular staggered coupling framework addresses the fundamental limitations of existing simulation tools by providing unprecedented flexibility, extensibility, and numerical robustness. The integration of five specialized modules (MFEM for Darcy flow and mass conservation, FEM for Reynolds lubrication, poroelasticity for mechanics, SGBEM for fracture mechanics, and phase-field for two-phase two-component advection-diffusion-reaction) within a unified platform represents a significant advance over current monolithic or loosely coupled approaches. The strategic use of SGBEM for fracture mechanics provides dramatic computational advantages over traditional phase-field approaches by eliminating the need for fine mesh resolution throughout the entire domain, while the implementation of advanced coupling strategies, including fixed-stress splitting and operator splitting, ensures numerical stability while maintaining computational efficiency.

**SO2 Innovation - Long-term Chemo-Mechanical Risk Assessment:** The development of advanced phase-field methods for long-term chemo-mechanical damage, building upon the host supervisor's expertise in chemo-mechanical fracture modeling, addresses the critical knowledge gap regarding the coupling between chemical reactions and mechanical deformation over extended time periods. The integration with PhreeqcRM and the development of machine learning-based surrogate models for chemical reactions provides a computationally efficient approach to capturing detailed geochemical processes while maintaining the physical rigor necessary for accurate long-term predictions.

**SO3 Innovation - Accessible Computational Platform:** The creation of the CUS-Sim Cloud platform addresses the critical gap between advanced scientific modeling and practical application by leveraging Koopman Autoencoders to achieve dramatic computational speedup while preserving the essential physics of the system. By making sophisticated multiphysics modeling accessible through a user-friendly web interface with real-time visualization and scenario exploration capabilities, this project will democratize access to advanced modeling capabilities and facilitate informed decision-making by all stakeholders.

The synergistic combination of these three innovations will create a transformative computational framework that advances the scientific understanding of CUS and EGS systems while providing practical tools for their safe and efficient deployment. This project represents a paradigm shift from traditional computationally expensive academic modeling approaches toward integrated, efficient, and practically relevant computational tools that can support the widespread adoption of these critical climate technologies.

## References

Aliabadi, M. H. (2002). *The boundary element method, volume 2: applications in solids and structures*. John Wiley & Sons.

Armbrust, M., Fox, A., Griffith, R., Joseph, A. D., Katz, R., Konwinski, A., ... & Zaharia, M. (2010). A view of cloud computing. *Communications of the ACM*, 53(4), 50-58.

Balay, S., Abhyankar, S., Adams, M. F., Brown, J., Brune, P., Buschelman, K., ... & Zhang, H. (2019). PETSc users manual. Argonne National Laboratory.

Bangerth, W., Hartmann, R., & Kanschat, G. (2007). deal.II—a general-purpose object-oriented finite element library. *ACM Transactions on Mathematical Software*, 33(4), 24-es.

Berre, I., Doster, F., & Keilegavlen, E. (2019). Flow in fractured porous media: A review of conceptual models and discretization approaches. *Transport in Porous Media*, 130(1), 215-236.

Bielicki, J. M., Pollak, M. F., Fitts, J. P., Peters, C. A., & Wilson, E. J. (2014). Causes and financial consequences of geologic CO2 storage reservoir leakage and interference with other subsurface resources. *Environmental Science & Technology*, 48(1), 4491-4501.

Bonnet, M., Maier, G., & Polizzotto, C. (1998). Symmetric Galerkin boundary element methods. *Applied Mechanics Reviews*, 51(11), 669-704.

Brunton, S. L., Budišić, M., Kaiser, E., & Kutz, J. N. (2022). Modern Koopman theory for dynamical systems. *SIAM Review*, 64(2), 229-340.

Computer Modelling Group. (2020). STARS User Guide. Computer Modelling Group Ltd.

Coussy, O. (2004). *Poromechanics*. John Wiley & Sons.

Flemisch, B., Darcis, M., Erbertseder, K., Faigle, B., Lauser, A., Mosthaf, K., ... & Helmig, R. (2011). DuMux: DUNE for multi-{phase, component, scale, physics, ...} flow and transport in porous media. *Advances in Water Resources*, 34(9), 1102-1112.

Gaston, D., Newman, C., Hansen, G., & Lebrun-Grandié, D. (2009). MOOSE: A parallel computational framework for coupled systems of nonlinear equations. *Nuclear Engineering and Design*, 239(10), 1768-1778.

Gaus, I., Azaroual, M., & Czernichowski-Lauriol, I. (2005). Reactive transport modelling of the impact of CO2 injection on the clayey cap rock at Sleipner (North Sea). *Chemical Geology*, 217(3-4), 319-337.

Gear, C. W., & Wells, D. R. (1984). Multirate linear multistep methods. *BIT Numerical Mathematics*, 24(4), 484-502.

Gherardi, F., Xu, T., & Pruess, K. (2007). Numerical modeling of self-limiting and self-enhancing caprock alteration induced by CO2 storage in a depleted gas reservoir. *Chemical Geology*, 244(1-2), 103-129.

Hageman, T., & Martínez-Pañeda, E. (2023). A phase field-based framework for electro-chemo-mechanical fracture: Crack-contained electrolytes, chemical reactions and stabilisation. *Computer Methods in Applied Mechanics and Engineering*, 415, 116235.

Huerta, N. J., Hesse, M. A., Bryant, S. L., Strazisar, B. R., & Lopano, C. (2013). Experimental evidence for self-limiting reactive flow through a fractured cement core: Implications for time-dependent wellbore leakage. *Environmental Science & Technology*, 47(1), 269-275.

Kim, J., Tchelepi, H. A., & Juanes, R. (2011). Stability and convergence of sequential methods for coupled flow and geomechanics: Fixed-stress and fixed-strain splits. *Computer Methods in Applied Mechanics and Engineering*, 200(13-16), 1591-1606.

Klinsmann, M., Rosato, D., Kamlah, M., & McMeeking, R. M. (2016). Modeling crack growth during Li insertion in storage particles using a fracture phase field approach. *Journal of the Mechanics and Physics of Solids*, 92, 313-344.

Kolditz, O., Bauer, S., Bilke, L., Böttcher, N., Delfs, J. O., Fischer, T., ... & Watanabe, N. (2012). OpenGeoSys: an open-source initiative for numerical simulation of thermo-hydro-mechanical/chemical (THM/C) processes in porous media. *Environmental Earth Sciences*, 67(2), 589-599.

Logg, A., Mardal, K. A., & Wells, G. (2012). *Automated solution of differential equations by the finite element method: The FEniCS book* (Vol. 84). Springer Science & Business Media.

Lusch, B., Kutz, J. N., & Brunton, S. L. (2018). Deep learning for universal linear embeddings of nonlinear dynamics. *Nature Communications*, 9(1), 4950.

Miehe, C., Hofacker, M., & Welschinger, F. (2010). A phase field model for rate-independent crack propagation: Robust algorithmic implementation based on operator splits. *Computer Methods in Applied Mechanics and Engineering*, 199(45-48), 2765-2778.

Miehe, C., Mauthe, S., & Teichtmeister, S. (2015). Minimization principles for the coupled problem of Darcy–Biot-type fluid transport in porous media linked to phase field modeling of fracture. *Journal of the Mechanics and Physics of Solids*, 82, 186-217.

Molins, S., Trebotich, D., Steefel, C. I., & Shen, C. (2012). An investigation of the effect of pore scale flow on average geochemical reaction rates using direct numerical simulation. *Water Resources Research*, 48(3).

Nordbotten, J. M., Kavetski, D., Celia, M. A., & Bachu, S. (2009). A semi-analytical model for large-scale injection of CO2 into aquifers. *Transport in Porous Media*, 80(2), 269-294.

Parkhurst, D. L., & Appelo, C. A. J. (2013). Description of input and examples for PHREEQC version 3—a computer program for speciation, batch-reaction, one-dimensional transport, and inverse geochemical calculations. US Geological Survey Techniques and Methods, 6(A43), 497.

Parkhurst, D. L., & Wissmeier, L. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC. *Advances in Water Resources*, 83, 176-189.

Pawar, R. J., Bromhal, G. S., Carey, J. W., Foxall, W., Korre, A., Ringrose, P. S., ... & Zhang, Y. (2015). Recent advances in risk assessment and risk management of geologic CO2 storage. *International Journal of Greenhouse Gas Control*, 40, 292-311.

Pruess, K., Oldenburg, C., & Moridis, G. (1999). TOUGH2 user's guide version 2. Lawrence Berkeley National Laboratory.

Raviart, P. A., & Thomas, J. M. (1977). A mixed finite element method for 2-nd order elliptic problems. In *Mathematical aspects of finite element methods* (pp. 292-315). Springer.

Rutqvist, J. (2011). Status of the TOUGH-FLAC simulator and recent applications related to coupled fluid flow and crustal deformations. *Computers & Geosciences*, 37(6), 739-750.

Rutqvist, J. (2012). The geomechanics of CO2 storage in deep sedimentary formations. *Geotechnical and Geological Engineering*, 30(3), 525-551.

Schlumberger. (2020). ECLIPSE reservoir simulation software. Schlumberger Limited.

Sedlmair, M., Meyer, M., & Munzner, T. (2012). Design study methodology: Reflections from the trenches and the stacks. *IEEE Transactions on Visualization and Computer Graphics*, 18(12), 2431-2440.

Settgast, R. R., Johnson, S. M., Walsh, S. D., Morris, J. P., Ryerson, F. J., & Friedmann, S. J. (2017). A fully coupled method for massively parallel simulation of hydraulically driven fractures in 3-dimensions. *International Journal for Numerical and Analytical Methods in Geomechanics*, 41(5), 627-653.

Steefel, C. I., Appelo, C. A. J., Arora, B., Jacques, D., Kalbacher, T., Kolditz, O., ... & Yeh, G. T. (2015). Reactive transport codes for subsurface environmental simulation. *Computational Geosciences*, 19(3), 445-478.

Stephens, J. C., Rand, G. M., & Melnick, L. L. (2009). Wind energy in US media: A comparative state-level analysis of a critical climate change mitigation technology. *Global Environmental Change*, 19(1), 89-97.

Sun, A. Y., Morris, A. P., & Mohanty, S. (2013). Sequential updating of multimodal hydrogeologic parameter fields using localization and clustering techniques. *Water Resources Research*, 49(10), 7204-7220.

Taron, J., & Elsworth, D. (2009). Thermal–hydrologic–mechanical–chemical processes in the evolution of engineered geothermal systems. *International Journal of Rock Mechanics and Mining Sciences*, 46(5), 855-864.

Wheeler, M. F., & Yotov, I. (2006). A multipoint flux mixed finite element method. *SIAM Journal on Numerical Analysis*, 44(5), 2082-2106.

White, M. D., Oostrom, M., & Lenhard, R. J. (2004). Modeling fluid flow and transport in variably saturated porous media with the STOMP simulator. 1. Nonvolatile three-phase system. *Advances in Water Resources*, 27(1), 83-95.

White, S. P., Allis, R. G., Moore, J., Chidsey, T., Morgan, C., Gwynn, W., & Adams, M. (2005). Simulation of reactive transport of injected CO2 on the Colorado Plateau, Utah, USA. *Chemical Geology*, 217(3-4), 387-405.

Xu, T., Apps, J. A., & Pruess, K. (2005). Mineral sequestration of carbon dioxide in a sandstone–shale system. *Chemical Geology*, 217(3-4), 295-318.

Yasuhara, H., Polak, A., Mitani, Y., Grader, A. S., Halleck, P. M., & Elsworth, D. (2006). Evolution of fracture permeability through fluid–rock reaction under hydrothermal conditions. *Earth and Planetary Science Letters*, 244(1-2), 186-200.

Zimmerman, R. W., & Bodvarsson, G. S. (1996). Hydraulic conductivity of rock fractures. *Transport in Porous Media*, 23(1), 1-30.

Zyvoloski, G. A., Robinson, B. A., Dash, Z. V., & Trease, L. L. (1997). Summary of the models and methods for the FEHM application-a finite-element heat-and mass-transfer code. Los Alamos National Laboratory.
