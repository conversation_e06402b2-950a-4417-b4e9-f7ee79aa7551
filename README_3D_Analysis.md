# 3D Geothermal Pile Thermo-Mechanical Analysis

A comprehensive MATLAB implementation for **true 3D analysis** of geothermal pile systems in Cartesian coordinates (x, y, z).

## 🌟 **Key Advantages of 3D Analysis**

### **🔄 From Axisymmetric to True 3D**

| Aspect | Axisymmetric (r, θ, z) | **True 3D (x, y, z)** |
|--------|-------------------------|------------------------|
| **Geometry** | Perfect circular symmetry | ✅ Realistic rectangular/square domains |
| **Tube Configuration** | Single centered tube | ✅ Multiple tubes, U-loops, complex layouts |
| **Boundary Conditions** | Uniform radial boundaries | ✅ Different conditions on each face |
| **Soil Heterogeneity** | Radially symmetric only | ✅ Full 3D material variations |
| **Heat Transfer** | 2D + symmetry | ✅ True 3D heat conduction |
| **Deformation** | Radial + axial only | ✅ Full 3D displacement field |

## 📐 **3D Governing Equations**

### **1. 3D Heat Equation**
```
∂T/∂t = α[∂²T/∂x² + ∂²T/∂y² + ∂²T/∂z²] + Q(x,y,z,t)
```

### **2. 3D Equilibrium Equations**
```
∂σxx/∂x + ∂σxy/∂y + ∂σxz/∂z = 0
∂σxy/∂x + ∂σyy/∂y + ∂σyz/∂z = 0  
∂σxz/∂x + ∂σyz/∂y + ∂σzz/∂z = 0
```

### **3. 3D Strain-Displacement Relations**
```
εxx = ∂ux/∂x,  εyy = ∂uy/∂y,  εzz = ∂uz/∂z
γxy = ∂ux/∂y + ∂uy/∂x
γxz = ∂ux/∂z + ∂uz/∂x  
γyz = ∂uy/∂z + ∂uz/∂y
```

### **4. 3D Constitutive Relations**
```
σxx = λ(εxx + εyy + εzz) + 2μεxx - (3λ + 2μ)αT ΔT
σyy = λ(εxx + εyy + εzz) + 2μεyy - (3λ + 2μ)αT ΔT
σzz = λ(εxx + εyy + εzz) + 2μεzz - (3λ + 2μ)αT ΔT
σxy = μγxy,  σxz = μγxz,  σyz = μγyz
```

## 🚀 **Quick Start**

### **Basic 3D Analysis**
```matlab
% Run complete 3D analysis
geothermal_pile_3D_analysis();
```

### **Key Parameters**
```matlab
% 3D Domain
Lx = 4.0;              % Domain width [m]
Ly = 4.0;              % Domain length [m]  
Lz = 25.0;             % Domain depth [m]

% 3D Grid
nx = 40;               % Grid points in x
ny = 40;               % Grid points in y
nz = 50;               % Grid points in z

% Multiple Tubes
n_tubes = 2;           % U-loop configuration
tube_spacing = 0.2;    % Distance between tubes [m]
```

## 🔧 **3D Implementation Features**

### **🏗️ Realistic Geometry**
- **Rectangular domains** instead of circular
- **Multiple tube configurations** (U-loops, parallel tubes)
- **Complex boundary conditions** on each face
- **3D tube masks** for accurate heat source modeling

### **🌡️ Advanced Thermal Analysis**
- **True 3D heat conduction** in all directions
- **Multiple heat sources** from tube array
- **Geothermal gradient** with depth
- **Surface temperature effects**
- **3D heat flux calculations**

### **🏗️ Complete Mechanical Analysis**
- **Full 3D stress tensor** (6 components)
- **3D displacement field** (ux, uy, uz)
- **Thermal expansion** in all directions
- **Complex boundary constraints**
- **Realistic deformation patterns**

## 📊 **3D Visualization Capabilities**

### **Temperature Analysis**
1. **Horizontal slices** at different depths
2. **Vertical cross-sections** through tubes
3. **3D isosurfaces** for temperature levels
4. **Temperature evolution** at key points

### **Mechanical Analysis**
1. **Displacement magnitude** contours
2. **3D displacement vectors** (subsampled)
3. **Stress distributions** (all components)
4. **Deformation patterns** visualization

### **Advanced Plots**
1. **Heat flux magnitude** distributions
2. **Cross-sections** through tube arrays
3. **Time evolution** plots
4. **Summary statistics** display

## 🎯 **Practical Applications**

### **🏢 Building Foundation Design**
- **Multiple pile analysis** with interaction effects
- **Non-uniform soil conditions** modeling
- **Complex loading patterns** consideration
- **Settlement analysis** for nearby structures

### **🌍 Geothermal System Optimization**
- **U-loop configuration** optimization
- **Tube spacing** effects analysis
- **Heat extraction** maximization
- **Thermal interference** minimization

### **🔬 Research Applications**
- **3D heat transfer** validation studies
- **Soil-structure interaction** research
- **Thermal-mechanical coupling** investigation
- **Parameter sensitivity** studies

## ⚙️ **Computational Considerations**

### **Memory Requirements**
- **4D arrays**: nx × ny × nz × nt
- **Typical case**: 40×40×50×1000 ≈ 320 MB per field
- **Multiple fields**: Temperature + 9 mechanical fields
- **Total memory**: ~3-4 GB for typical analysis

### **Performance Optimization**
- **Vectorized operations** where possible
- **Efficient boundary condition** application
- **Progress monitoring** for long runs
- **Selective output** saving

### **Stability Considerations**
- **3D CFL condition**: Δt ≤ min(Δx², Δy², Δz²)/(6α)
- **Mechanical stability**: Proper boundary constraints
- **Convergence monitoring** for iterative solvers

## 🔍 **Validation and Verification**

### **Analytical Benchmarks**
- **3D heat conduction** in rectangular domains
- **Point/line heat sources** solutions
- **Thermal expansion** of 3D bodies
- **Stress concentration** around cavities

### **Comparison Studies**
- **Axisymmetric vs 3D** for circular cases
- **Single vs multiple tubes** effects
- **Different boundary conditions** impact
- **Grid convergence** studies

## 📈 **Results Interpretation**

### **Temperature Fields**
- **Hot zones** around tubes clearly visible
- **3D heat plumes** extending into soil
- **Thermal interference** between tubes
- **Boundary layer** effects at domain edges

### **Displacement Patterns**
- **3D expansion** around heated zones
- **Surface heave/settlement** patterns
- **Asymmetric deformation** due to boundaries
- **Stress concentration** near tubes

### **Design Insights**
- **Optimal tube spacing** for heat extraction
- **Minimum distances** to avoid interference
- **Foundation settlement** predictions
- **Thermal stress** management

## 🔮 **Future Enhancements**

### **Advanced Physics**
- **Groundwater flow** coupling (3D Darcy flow)
- **Phase change** modeling (freeze-thaw)
- **Nonlinear soil behavior** (plasticity)
- **Dynamic loading** effects

### **Computational Improvements**
- **Adaptive mesh refinement** (AMR)
- **Parallel computing** (GPU acceleration)
- **Implicit time integration** for larger time steps
- **Multigrid solvers** for faster convergence

### **Engineering Features**
- **CAD geometry import** for complex shapes
- **Real soil data** integration
- **Optimization algorithms** for design
- **Uncertainty quantification** methods

## 🎛️ **Customization Options**

### **Geometry Modifications**
```matlab
% Custom tube arrangements
tube_positions = [x1, y1; x2, y2; x3, y3; ...];

% Heterogeneous soil properties
soil_props.k = k_field(x, y, z);  % 3D conductivity field

% Complex boundary conditions
T_boundary = T_func(x, y, z, t);  % Time-dependent boundaries
```

### **Advanced Material Models**
```matlab
% Temperature-dependent properties
k_soil = k0 * (1 + beta * (T - T0));

% Anisotropic conductivity
k_tensor = [kxx, kxy, kxz; kxy, kyy, kyz; kxz, kyz, kzz];

% Layered soil profiles
soil_layers = define_soil_layers(z_interfaces, properties);
```

## 📚 **Theoretical Background**

### **3D Heat Transfer**
- **Fourier's law** in 3D Cartesian coordinates
- **Finite difference** discretization schemes
- **Stability analysis** for explicit methods
- **Boundary condition** implementation

### **3D Elasticity Theory**
- **Navier equations** for displacement
- **Stress-strain relationships** in 3D
- **Thermal stress** formulation
- **Boundary value problems** in elasticity

### **Numerical Methods**
- **Finite difference** on structured grids
- **Explicit time integration** schemes
- **Boundary condition** enforcement
- **Convergence criteria** and error analysis

## 🚨 **Limitations and Assumptions**

### **Current Limitations**
1. **Structured grid** only (no complex geometries)
2. **Explicit time stepping** (stability constraints)
3. **Linear elasticity** (small deformations)
4. **Homogeneous materials** within grid cells
5. **No fluid flow** in soil (dry conditions)

### **Key Assumptions**
1. **Constant material properties** (with temperature dependence options)
2. **Perfect thermal contact** at interfaces
3. **No heat generation** in soil
4. **Quasi-static** mechanical response
5. **Isotropic materials** (can be extended)

---

*This 3D implementation provides a significant advancement over axisymmetric models, enabling realistic analysis of complex geothermal pile systems with multiple tubes and heterogeneous conditions.*

**Developed for advanced geothermal pile analysis and design**  
**Compatible with MATLAB R2018b and later**  
**Last updated: 2025-07-03**
