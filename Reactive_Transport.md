﻿State-of-the-Art Reactive Transport Modeling for Geological Carbon Sequestration




Introduction


Reactive Transport Modeling (RTM) has emerged as an indispensable discipline in the Earth and environmental sciences, providing a quantitative framework for understanding the complex interplay of fluid flow, mass transport, and geochemical reactions within porous media. These models are designed to predict the spatiotemporal evolution of chemical systems, integrating principles from hydrology, geochemistry, and physics to simulate processes that are fundamental to a vast array of natural and engineered systems.1 The applications of RTM are exceptionally broad, underpinning our ability to manage critical environmental challenges. A primary application in the modern era is the design and risk assessment of geological carbon sequestration (GCS) sites, a critical technology for mitigating climate change.4 RTM is also essential for predicting the fate of contaminants in groundwater, designing secure geological repositories for nuclear waste, and optimizing geothermal energy extraction.2
The central challenge that RTM addresses is the profound and dynamic coupling between chemical reactions and the physical environment. In the context of GCS, mineral dissolution and precipitation do not occur within a static container; these reactions actively reshape the porous medium of the storage reservoir and the overlying caprock.7 As minerals dissolve, the pore space (porosity) increases, and as new minerals precipitate, it decreases. These changes in pore geometry directly alter the medium's permeability—its ability to transmit fluids. This creates a critical feedback loop: chemical reactions modify the flow pathways, which in turn alters the transport of reactive fluids, thereby influencing where subsequent reactions occur.9 This intricate coupling can lead to complex phenomena, such as the clogging of pore throats that impedes injectivity or the alteration of caprock integrity over geological timescales.10
The history of mathematical modeling in this field reflects a continuous journey from simplification to sophistication. Early models necessarily made significant simplifying assumptions to render problems computationally tractable. However, as these models were tested against laboratory experiments and field observations, their limitations became apparent, creating the impetus for the development of more refined successors.2 This iterative cycle has propelled the field forward, transforming application-specific tools into general-purpose scientific frameworks capable of capturing multiphysics phenomena with increasing fidelity. This article provides a comprehensive overview of the state-of-the-art in reactive transport modeling, with a specific focus on its application to the geochemical reactions governing the long-term fate and security of geologically stored CO₂.


The Fundamental Principles of Reactive Transport


At its core, reactive transport modeling is built upon a consistent mathematical framework that describes how the concentration of a chemical substance changes in a porous medium as a result of being carried by fluid, spreading out, and reacting with its surroundings. The sophistication of any given model lies in how it defines these processes and solves the resulting equations.


The Governing Equation: Advection-Dispersion-Reaction


The cornerstone of virtually all continuum-scale reactive transport models is a partial differential equation (PDE) known as the advection-dispersion-reaction (ADR) equation. This equation represents a mass balance on a chemical species, accounting for the key processes that govern its concentration in space and time.11 The general form of the ADR equation for a species
i with concentration Ci​ in a porous medium with porosity θ can be expressed as:
∂t∂(θCi​)​=∇⋅(θD∗∇Ci​)−∇⋅(qCi​)+Ri​
Each term in this equation corresponds to a distinct physical process:
1. Accumulation Term (∂t∂(θCi​)​): This term on the left-hand side represents the rate of change of the species' mass per unit volume of the porous medium over time.
2. Dispersion Term ($\nabla \cdot (\theta D^ \nabla C_i)$):* This term describes the spreading of the solute. It combines two effects: molecular diffusion (the random thermal motion of molecules) and mechanical dispersion (the mixing that occurs as fluid navigates the tortuous pore network at varying velocities). D∗ is the hydrodynamic dispersion tensor, which captures this combined effect.11
3. Advection Term (∇⋅(qCi​)): This term accounts for the transport of the solute due to the bulk movement of the fluid. The vector q represents the Darcy velocity, which is the volumetric flow rate of the fluid per unit area of the porous medium.11
4. Reaction Term (Ri​): This is the source/sink term. It represents the rate at which the species i is produced or consumed by chemical, physical, or biological reactions within the system. For the dissolution of a mineral, Ri​ would be positive (a source of dissolved ions), while for precipitation, it would be negative (a sink).11
The ADR equation provides a universal language for describing reactive transport. The primary differences between various models arise from the specific formulations used to define the reaction term Ri​ and the numerical methods employed to solve the equation.


Characterizing the Reaction Term (R)


The reaction term Ri​ is where the geochemistry is encoded into the model. The choice of how to represent this term is one of the most fundamental decisions in RTM, dictating the model's accuracy, complexity, and computational cost. Two principal approaches are used: the local equilibrium assumption and kinetic rate laws.
* Local Equilibrium Assumption (LEA): This approach assumes that all chemical reactions, including mineral dissolution/precipitation and aqueous speciation, occur instantaneously relative to the timescale of fluid transport. Consequently, the chemical system at any point in space is always considered to be in thermodynamic equilibrium. This was a common feature of early geochemical models and is suitable for reactions that are known to be very fast compared to the flow rate. However, for many geological processes, especially the dissolution and precipitation of silicate minerals, this assumption is invalid.
* Kinetic Rate Laws: For processes that are not instantaneous, a kinetic approach is necessary. This approach defines the reaction rate as a finite quantity governed by a specific rate law. For mineral-fluid reactions, the rate law is often derived from Transition State Theory and generally takes the form 12:
r=As​k+​(1−KQ​)n
where:
* r is the reaction rate (e.g., in mol⋅m−2⋅s−1).
* As​ is the reactive surface area of the mineral per unit volume of the medium. This is a critical parameter that can itself evolve as reactions proceed.
* k+​ is the intrinsic rate constant, which quantifies the reaction speed under conditions far from equilibrium. This constant is highly dependent on temperature, a relationship often described by the Arrhenius equation, making it a crucial factor in modeling high-temperature systems like deep petroleum reservoirs or geothermal fields.8
* (1−Q/K) is the chemical affinity term, which describes how far the system is from equilibrium. Q is the ion activity product for the reaction, and K is the equilibrium constant. As the fluid composition approaches saturation (Q→K), the affinity term approaches zero, and the reaction rate slows to a halt.
* n is an empirical exponent.
The choice between equilibrium and kinetics often depends on the relative timescales of reaction and transport, a concept quantified by the dimensionless Damköhler number (Da). The Damköhler number is the ratio of the characteristic transport time to the characteristic reaction time. For very large Da (slow transport or fast reaction), the system behaves as if it is in local equilibrium. For very small Da (fast transport or slow reaction), the fluid composition may not change significantly as it flows. For intermediate Da, a full kinetic reactive transport calculation is required, as both processes are of comparable importance.12


Numerical Solution Strategies: Operator Splitting


Solving the fully coupled ADR equation, which is a system of nonlinear PDEs, is a formidable computational challenge. While it is possible to solve the transport and reaction equations simultaneously (a "global implicit" or "one-step" method), this approach is computationally intensive and complex to implement.3
A more common and pragmatic approach is operator splitting. This technique decouples the overall problem into a series of simpler steps that are solved sequentially over a given time increment, Δt. The most prevalent scheme is the Sequential Non-Iterative Approach (SNIA), which involves two main steps 3:
1. Transport Step: First, all chemical reactions are temporarily ignored (Ri​=0), and only the advection and dispersion of all chemical species are calculated for the time step Δt. This step essentially moves the solutes through the domain.
2. Reaction Step: Next, transport is temporarily ignored, and the chemical reactions are calculated for each grid cell (or node) independently, as if each were a closed-system batch reactor. The concentrations updated in the transport step are used as the initial conditions for this reaction step.
This decoupling strategy is exceptionally powerful because it allows for the use of numerical methods that are optimally suited for each part of the problem.18 More importantly, it enables a modular approach to code development, where a highly optimized fluid flow and transport simulator can be linked with a separate, comprehensive geochemical reaction code.16 This modularity has been a major driver of progress in the RTM field, allowing complex transport codes to leverage the power of established geochemical engines like PHREEQC. The development of specialized interfaces, such as PhreeqcRM, was specifically intended to facilitate this coupling, providing a robust bridge between the transport and reaction "operators".16


State-of-the-Art Modeling for Supercritical CO₂ Reactions


Geological Carbon Sequestration (GCS) is a critical technology for mitigating climate change, involving the injection of carbon dioxide, typically in a supercritical state (scCO₂), into deep geological formations like saline aquifers.4 The long-term security of this storage depends on a series of trapping mechanisms, with the most permanent being mineral trapping, where CO₂ reacts with the host rock to form stable carbonate minerals. Modeling these complex interactions is a frontier application for RTM.
When scCO₂ is injected, it dissolves into the formation brine, forming carbonic acid (H₂CO₃), which subsequently dissociates and lowers the ambient pH.22 This acidified brine becomes undersaturated with respect to many primary minerals in the host rock, driving their dissolution. This, in turn, can lead to the precipitation of secondary carbonate minerals, effectively locking the CO₂ into a solid, stable phase.22 Key reactions include:
* CO₂ Dissolution: CO2​(g)↔CO2​(aq)
* Carbonic Acid Formation: CO2​(aq)+H2​O↔H2​CO3​
* Acid Dissociation: H2​CO3​↔H++HCO3−​
* Mineral Dissolution (e.g., Anorthite): CaAl2​Si2​O8​+8H+→Ca2++2Al3++2SiO2​(aq)+4H2​O
* Mineral Precipitation (e.g., Calcite): Ca2++HCO3−​↔CaCO3​(s)+H+
The geochemical processes in GCS are defined by vastly different timescales. While aqueous speciation and CO₂ dissolution are rapid, the dissolution of silicate minerals is a kinetically-controlled process that can take hundreds to thousands of years.22 Consequently, the
Local Equilibrium Assumption (LEA) is generally invalid for GCS modeling. The system's evolution is governed by these slow, finite reaction rates, making kinetic models essential for accurate long-term prediction of storage security and capacity.25
Modeling GCS requires simulators that can handle multiphase flow (immiscible scCO₂ and aqueous brine), multicomponent transport, and complex geochemical reactions under high-pressure, high-temperature conditions.4 This necessitates the use of
Detailed Chemistry Models. State-of-the-art simulators such as TOUGHREACT, PFLOTRAN, and GEOSX are designed for this purpose.4 These codes couple multiphase flow equations with comprehensive geochemical packages (often based on the core capabilities of engines like
PHREEQC) that use extensive thermodynamic databases to solve for both equilibrium and kinetic reactions simultaneously. This approach allows for a fundamental, first-principles simulation of how the injected CO₂ plume will interact with the reservoir over geological timescales, predicting the extent of mineral trapping and its impact on the physical properties of the formation.30
These detailed chemistry models represent a shift in philosophy away from simplified, application-specific reaction networks toward a full, thermodynamically consistent geochemical framework. They do not rely on a small, predefined set of reactions. Instead, they leverage comprehensive thermodynamic databases to solve for the equilibrium distribution of all relevant aqueous species, gases, and mineral phases based on the law of mass action, while simultaneously accounting for the kinetic rates of slow reactions.2 The capabilities of such models are vast and general, allowing them to simulate a wide spectrum of geochemical processes, including:3
* Equilibrium and kinetic mineral dissolution/precipitation.
* Aqueous speciation and complexation.
* Redox reactions.
* Surface complexation and ion exchange.
* Gas-phase interactions (dissolution and exsolution).
* Solid-solution behavior.
These models are general-purpose scientific tools that represent the current state-of-the-art in chemical fidelity and flexibility. Their primary limitations are their significant computational cost and their reliance on extensive, high-quality thermodynamic and kinetic data, which may not always be available for all minerals and conditions of interest.33


System-Level Feedbacks and Advanced Coupling


The most sophisticated reactive transport models go beyond simply calculating chemical concentrations. They account for the critical feedback mechanisms through which chemical reactions alter the physical and mechanical properties of the porous medium.9 This evolution towards fully coupled multiphysics represents the frontier of the discipline, transforming the porous medium from a passive backdrop into a dynamic, co-evolving component of the system.2


Porosity and Permeability Evolution


The most direct physical consequence of mineral dissolution and precipitation is a change in the pore volume of the rock. This change in porosity is the crucial link between the geochemical reaction model and the physical transport properties of the medium.7
The update to porosity (n) is calculated from a simple mass balance. It is a direct function of the initial porosity (n0​) and the net change in the solid volume fraction of all reacting minerals, which is the sum of the volumes of dissolved minerals minus the volumes of precipitated minerals.8
This change in porosity, in turn, has a profound effect on the permeability (k) of the medium. While the exact relationship is complex and site-specific, it is often described using empirical or semi-empirical models. One of the most widely used is the Kozeny-Carman equation, which relates the change in permeability to the change in porosity 8:
k0​k​=(n0​n​)3(1−n1−n0​​)2
where k0​ and n0​ are the initial permeability and porosity, respectively. This equation captures the intuitive concept that as porosity increases, permeability increases at an even faster rate.
This coupling between reaction, porosity, and permeability creates a powerful, and often highly nonlinear, feedback loop. In GCS, mineral precipitation can lead to pore clogging, a dramatic reduction in permeability, and a potential decrease in the reservoir's capacity to accept CO₂.9 Conversely, dissolution can enhance permeability, but if it occurs in the caprock, it could potentially compromise the seal's integrity.10


Chemo-Mechanical Coupling


Beyond altering fluid flow pathways, mineral reactions can fundamentally change the mechanical integrity of the rock itself. The dissolution of minerals and, crucially, the cement that binds grains together, weakens the solid skeleton, reducing its strength and stiffness. This chemo-mechanical coupling is essential for modeling the long-term caprock integrity for CO₂ sequestration, where chemical alteration could lead to mechanical failure.10
Modern modeling frameworks capture this coupling through several mechanisms:
* Porosity-Dependent Elastic Properties: The rock's elastic moduli, such as Young's modulus (E) and Poisson's ratio (ν), are no longer treated as constants. Instead, they are defined as functions of the evolving porosity. As dissolution increases porosity, the rock becomes less stiff (lower E), making it more susceptible to deformation and failure under stress.8
* Chemical Damage Variable: To provide a more direct link between chemistry and mechanics, advanced models introduce a "chemical damage" variable, ϕchem. This scalar variable quantifies the degradation of material strength due to chemical processes, distinct from damage caused by mechanical strain. It is often formulated as an exponential function of the porosity change, where a small increase in porosity can lead to a significant drop in chemical integrity.8 This chemical damage term is then incorporated into the material's constitutive law, directly reducing the stress it can bear.
This synergistic interaction between chemical degradation and mechanical stress is a prime example of the fully coupled multiphysics phenomena that state-of-the-art reactive transport models now aim to capture, which is critical for assessing long-term risks in GCS projects.8


Conclusion: Synthesis and Future Directions


The mathematical modeling of mineral dissolution and precipitation has evolved into a comprehensive scientific framework, essential for tackling complex challenges like geological carbon sequestration. The state-of-the-art approach for GCS relies on detailed chemistry models that can simulate the full suite of geochemical reactions under reservoir conditions. Because the key mineral trapping reactions are kinetically slow, these models must move beyond the local equilibrium assumption to accurately predict the long-term fate of injected CO₂.
The frontier of reactive transport modeling is now firmly in the realm of fully coupled multiphysics, where chemical reactions are understood not as isolated events but as active agents that dynamically reshape the physical and mechanical world they inhabit.2 The feedback between mineral reactions, porosity-permeability evolution, and geomechanical response is no longer a secondary consideration but a central focus of modern research.9 As this review has shown, understanding these couplings is essential for accurately predicting the long-term security and capacity of geological storage sites.8
Looking forward, several key frontiers are poised to define the next generation of reactive transport modeling:
* Scale Integration: A persistent grand challenge is bridging the vast range of scales, from the molecular interactions at mineral surfaces, to the flow patterns within individual pores, up to the continuum behavior of entire reservoirs. Integrating insights from pore-scale models into the parameterizations of large-scale continuum models remains a critical area of research that promises to improve the physical basis of our simulations.11
* Machine Learning and Data-Driven Modeling: The increasing complexity of RTMs and the large datasets generated by them make the field ripe for the application of machine learning. AI and statistical methods are emerging as powerful tools for conducting sensitivity and uncertainty analyses, for optimizing computationally expensive simulations, and for developing fast, accurate surrogate models that can be used for rapid design and forecasting.35
* Broader Multiphysics Coupling: The future of RTM lies in expanding the scope of coupled processes. This includes more sophisticated integration with thermal-hydro-mechanical-chemical (THMC) processes, which is vital for GCS, geothermal energy, and nuclear waste disposal.37 It also involves coupling with biological processes, which can dramatically alter flow and reactivity in near-surface environments.3
In conclusion, the modeling of mineral dissolution and precipitation has evolved into a sophisticated and powerful predictive science. By continuing to push the boundaries of multiphysics coupling, scale integration, and computational methods, the field of reactive transport modeling will remain an essential tool for addressing some of the most pressing energy and environmental challenges of our time.


References


8 Guo, Y., & Na, S. (2024). A reactive-transport phase-field modelling approach of chemo-assisted cracking in saturated sandstone.
Computer Methods in Applied Mechanics and Engineering, 419, 116645.
11 Steefel, C. I., et al. (2019). Reactive transport at the crossroads.
Reviews in Mineralogy and Geochemistry, 85(1), 1-13.
1 Steefel, C. I. (2006). Reactive Transport Modeling.
Lawrence Berkeley National Laboratory.
7 Osei-Bonsu, K., et al. (2022). A review on reactive transport modelling in porous media.
Geomechanics and Geophysics for Geo-Energy and Geo-Resources, 8(3), 85.
2 Li, L., et al. (2021). Reactive transport modeling in environmental and engineering sciences: A review and future directions.
Environmental Science & Technology, 55(15), 10245-10266.
25 Xu, T., Apps, J. A., & Pruess, K. (2004). Reactive transport modeling of CO2 storage in saline aquifers to secure long-term safety and effectiveness.
Lawrence Berkeley National Laboratory.
3 Wikipedia. (n.d.).
Reactive transport modeling in porous media.
20 Parkhurst, D. L., & Wissmeier, L. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC.
Advances in Water Resources, 83, 176-189.
1 Steefel, C. I., DePaolo, D. J., & Lichtner, P. C. (2005). Reactive transport modeling: An essential tool and a new research approach for the Earth sciences.
Earth and Planetary Science Letters, 240(3-4), 539-558.
9 Li, L., et al. (2019). Reactive Transport in Evolving Porous Media.
Reviews in Mineralogy and Geochemistry, 85(1), 197-234.
16 Parkhurst, D. L., & Wissmeier, L. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC.
USGS Publication.
21 ResearchGate. (2015). PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC.
12 GWB Academy. (n.d.). Dissolution and Precipitation Kinetics.
The Geochemist's Workbench.
40 Hu, M., & Rutqvist, J. (2022). Reactive Transport Modeling of Mineral Precipitation and Carbon Trapping in Discrete Fracture Networks.
Water Resources Research, 58(9), e2022WR032333.
41 Hekim, Y., & Fogler, H. S. (1980). On the movement of multiple reaction zones in porous media.
AIChE Journal, 26(3), 403-411.
42 Hill, A. D., Lindsay, D. M., Schechter, R. S., & Silberberg, I. H. (1979). Sandstone acidizing: the development of design methods.
SPE Annual Technical Conference and Exhibition.
14 Fogler, H. S., & McCune, C. C. (1976). On the extension of the model of matrix acid stimulation to different sandstones.
AIChE Journal, 22(4), 799-805.
34 Tang, Y., et al. (2023). Review of Pore-scale Reactive Transport Modeling for Microbial Processes in Subsurface Environments.
ResearchGate.
13 Palandri, J. L., & Kharaka, Y. K. (2004). A compilation of rate parameters of water-mineral interaction kinetics for application to geochemical modeling.
USGS Open File Report 2004-1068.
15 Nardi, A., et al. (2014). A comparison of two approaches for reactive transport modeling.
Procedia Earth and Planetary Science, 10, 10-17.
17 Wang, Y., et al. (2020). An integration-point-based operator-splitting finite element scheme for reactive transport modeling in saturated porous media.
Journal of Contaminant Hydrology, 234, 103688.
18 Boesenhofer, M., et al. (2021). Operator splitting for reactive flows: A new consistent staggered scheme.
Combustion and Flame, 231, 111475.
19 Al-Yaseri, A., et al. (2020). IC-FERST–PHREEQCRM: a parallel coupling of a 3D reservoir simulator and a geochemical reaction package.
Computational Geosciences.
31 Parkhurst, D. L. (1999). User's Guide to PHREEQC (Version 2)—A Computer Program for Speciation, Batch-Reaction, One-Dimensional Transport, and Inverse Geochemical Calculations.
USGS.
32 Number Analytics. (n.d.). Mastering PHREEQC for Geochemical Analysis.
4 Doughty, C., & Pruess, K. (2003). Modeling Supercritical Carbon Dioxide Injection in Heterogeneous Porous Media.
Lawrence Berkeley National Laboratory.
5 Nole, M., et al. (2025). Modeling Supercritical CO2 Flow and Mineralization in Reactive Host Rocks with PFLOTRAN v7.0.
EGUsphere.
6 Nole, M., et al. (2025). Modeling Supercritical CO2 Flow and Mineralization in Reactive Host Rocks with PFLOTRAN v7.0.
EGUsphere.
22 Liu, F., et al. (2019). A tutorial review of reactive transport modeling and risk assessment for geologic CO2 sequestration.
ResearchGate.
23 Mitchell, M. J., et al. (2010). A model of carbon dioxide dissolution and mineral carbonation kinetics.
Proceedings of the Royal Society A: Mathematical, Physical and Engineering Sciences.
24 Kaszuba, J. P., et al. (2012). Geochemical effects of CO2 sequestration in sandstone under simulated in situ conditions of deep saline aquifers.
ResearchGate.
26 Zhang, R., et al. (2014). Reactive transport modeling for CO2 geological sequestration.
ResearchGate.
27 Doughty, C., & Pruess, K. (2003). Modeling Supercritical Carbon Dioxide Injection in Heterogeneous Porous Media.
TOUGH Symposium.
28 GEOSX Simulates Carbon Dioxide Storage. (2022).
Science & Technology Review.
29 GEOSX Simulates Carbon Dioxide Storage. (2022).
Science & Technology Review.
10 Rutqvist, J. (2015). Geomechanics of CO2 storage in deep saline formations.
Peterhead 11.115 - Geomechanics Report.
35 Wen, G., et al. (2021). CCSNet: A deep learning modeling suite for CO2 storage.
arXiv.
36 CCSNet: Subsurface Modeling for Carbon Sequestration. (n.d.).
Stanford University.
37 Abd, A. S., & Abushaikha, A. (2022). A Numerical Subsurface Simulator for Coupled Flow, Geomechanics, and Geochemistry.
InterPore.
38 Wang, S., et al. (2021). CO2-Fluid-Rock Interactions and the Coupled Geomechanical Response during CCUS Processes in Unconventional Reservoirs.
ResearchGate.
39 Carbon Transport and Storage Multi-Year Program Plan. (2024).
U.S. Department of Energy.
33 McGrail, B. P., et al. (2014). Adequacy of Thermodynamic Data for Geochemical Modeling of CO2 Sequestration in Carbonates.
PNNL.
30 Xu, T., et al. (2004). Reactive transport modelling of CO2 storage in saline aquifers to elucidate fundamental processes, trapping mechanisms and sequestration partitioning.
Geological Society, London, Special Publications.
Works cited
1. Reactive transport modeling: An essential tool and a new research approach for the Earth sciences, accessed July 28, 2025, https://www2.lbl.gov/Science-Articles/Archive/sabl/2006/Mar/Reactive-Transport-Modeling.pdf
2. Addressing Water and Energy Challenges with Reactive Transport Modeling, accessed July 28, 2025, https://par.nsf.gov/servlets/purl/10288761
3. Reactive transport modeling in porous media - Wikipedia, accessed July 28, 2025, https://en.wikipedia.org/wiki/Reactive_transport_modeling_in_porous_media
4. MODELING SUPERCRITICAL CARBON DIOXIDE INJECTION IN HETEROGENEOUS POROUS MEDIA Christine Doughty - OSTI, accessed July 28, 2025, https://www.osti.gov/servlets/purl/835349
5. Modeling Supercritical CO 2 Flow and Mineralization in Reactive Host Rocks with PFLOTRAN v7.0 - EGUsphere, accessed July 28, 2025, https://egusphere.copernicus.org/preprints/2025/egusphere-2025-1343/
6. Modeling Supercritical CO2 Flow and Mineralization in Reactive Host Rocks with PFLOTRAN v7.0 - EGUsphere, accessed July 28, 2025, https://egusphere.copernicus.org/preprints/2025/egusphere-2025-1343/egusphere-2025-1343.pdf
7. A review on reactive transport model and porosity evolution in the porous media - PMC, accessed July 28, 2025, https://pmc.ncbi.nlm.nih.gov/articles/PMC9252980/
8. accessed January 1, 1970,
9. Reactive Transport in Evolving Porous Media | Reviews in Mineralogy and Geochemistry | GeoScienceWorld, accessed July 28, 2025, https://pubs.geoscienceworld.org/msa/rimg/article/85/1/197/573305/Reactive-Transport-in-Evolving-Porous-Media
10. Peterhead CCS Project - GOV.UK, accessed July 28, 2025, https://assets.publishing.service.gov.uk/media/5a75adcae5274a436829921e/Peterhead_11.115_-_Geomechanics_Report.pdf
11. Reactive Transport at the Crossroads - Mineralogical Society of America, accessed July 28, 2025, https://msaweb.org/wp-content/uploads/2022/05/RiMG085_Ch01-1.pdf
12. Dissolution and Precipitation - GWB Online Academy - The Geochemist's Workbench®, accessed July 28, 2025, https://academy.gwb.com/dissolution.php
13. A COMPILATION OF RATE PARAMETERS OF WATER-MINERAL INTERACTION KINETICS FOR APPLICATION TO GEOCHEMICAL MODELING, accessed July 28, 2025, https://pubs.usgs.gov/of/2004/1068/pdf/OFR_2004_1068.pdf
14. On the Extension of the Model of Matrix Acid Stimulation to Different Sandstones, accessed July 28, 2025, https://deepblue.lib.umich.edu/bitstream/handle/2027.42/37374/690220426_ftp.pdf;sequence=1
15. A comparison of two approaches for reactive transport modeling - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/248532647_A_comparison_of_two_approaches_for_reactive_transport_modeling
16. PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC - Water Resources Mission Area, accessed July 28, 2025, https://water.usgs.gov/water-resources/software/VS2DRTI/ParkhurstWissmeier2015.pdf
17. A new operator-splitting finite element scheme for reactive transport modeling in saturated porous media - UFZ, accessed July 28, 2025, https://www.ufz.de/index.php?de=20939&pub_data[function]=showFileFinalDraftAuthorVersion&pub_data[PUB_ID]=26014
18. Analysis and evaluation of steady-state and non-steady-state preserving operator splitting schemes for reaction-diffusion - K1-MET, accessed July 28, 2025, https://www.k1-met.com/fileadmin/user_upload/Publications/Journal_articles_open_access/Boesenhofer_Operator_Splitting.pdf
19. MIT Open Access Articles Reactive transport modeling in heterogeneous porous media with dynamic mesh optimization, accessed July 28, 2025, https://dspace.mit.edu/bitstream/handle/1721.1/131986/10596_2020_10009_ReferencePDF.pdf?sequence=1&isAllowed=y
20. PhreeqcRM: A reaction module for transport simulators based on the ..., accessed July 28, 2025, https://pubs.usgs.gov/publication/70189910
21. PhreeqcRM: A reaction module for transport simulators based on the geochemical model PHREEQC | Request PDF - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/277818195_PhreeqcRM_A_reaction_module_for_transport_simulators_based_on_the_geochemical_model_PHREEQC
22. (PDF) A tutorial review of reactive transport modeling and risk ..., accessed July 28, 2025, https://www.researchgate.net/publication/331550594_A_tutorial_review_of_reactive_transport_modeling_and_risk_assessment_for_geologic_CO2_sequestration
23. (PDF) Geochemical interactions of supercritical CO2-brine-rock under varying injection strategies: implications for mechanical integrity in aquifers - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/390353251_Geochemical_interactions_of_supercritical_CO2-brine-rock_under_varying_injection_strategies_implications_for_mechanical_integrity_in_aquifers
24. Geochemical effects of CO2 sequestration in sandstone under simulated in situ conditions of deep saline aquifers - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/233954832_Geochemical_effects_of_CO2_sequestration_in_sandstone_under_simulated_in_situ_conditions_of_deep_saline_aquifers
25. Reactive Transport Modeling for CO2 Geological Sequestration - OSTI, accessed July 28, 2025, https://www.osti.gov/servlets/purl/1210905
26. Reactive transport modeling for CO 2 geological sequestration | Request PDF - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/241098862_Reactive_transport_modeling_for_CO_2_geological_sequestration
27. MODELING SUPERCRITICAL CO2 INJECTION IN HETEROGENEOUS POROUS MEDIA - TOUGH, accessed July 28, 2025, https://tough.lbl.gov/assets/files/02/documentation/proceedings/2003-DoughtyPruess.pdf
28. LLNL, partners open access to CO2 storage simulator, accessed July 28, 2025, https://www.llnl.gov/article/46901/llnl-partners-open-access-co2-storage-simulator
29. GEOSX Simulates Carbon Dioxide Storage - Science & Technology Review, accessed July 28, 2025, https://str.llnl.gov/past-issues/march-2022/geosx-simulates-carbon-dioxide-storage
30. Reactive transport modelling of CO2 storage in saline aquifers to elucidate fundamental processes, trapping mechanisms and sequestration partitioning | Geological Society, London, Special Publications - Lyell Collection, accessed July 28, 2025, https://www.lyellcollection.org/doi/10.1144/gsl.sp.2004.233.01.08
31. Modules Based on the Geochemical Model PHREEQC for Use in Scripting and Programming Languages - USGS.gov, accessed July 28, 2025, https://water.usgs.gov/water-resources/software/PHREEQC/IPhreeqc.pdf
32. Mastering PHREEQC for Geochemical Analysis - Number Analytics, accessed July 28, 2025, https://www.numberanalytics.com/blog/mastering-phreeqc-geochemical-analysis
33. On the extension of the model of matrix acid stimulation to different sandstones - SciSpace, accessed July 28, 2025, https://scispace.com/pdf/on-the-extension-of-the-model-of-matrix-acid-stimulation-to-40e0c6mx5r.pdf
34. Review of Pore-scale Reactive Transport Modeling for Microbial Processes in Subsurface Environments - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/389923059_Review_of_Pore-scale_Reactive_Transport_Modeling_for_Microbial_Processes_in_Subsurface_Environments
35. CCSNet: a deep learning modeling suite for CO2 storage - arXiv, accessed July 28, 2025, https://arxiv.org/pdf/2104.01795
36. CCSNet: Subsurface intelligence for carbon storage | Office of Technology Licensing, accessed July 28, 2025, https://otl.stanford.edu/researchers/high-impact-technology-hit-fund/hit-portfolio%23sustainability/ccsnet-subsurface
37. Integration of geochemistry into a geomechanical subsurface flow simulator, accessed July 28, 2025, https://ipjournal.interpore.org/index.php/interpore/article/view/6
38. CO2-Fluid-Rock Interactions and the Coupled Geomechanical Response during CCUS Processes in Unconventional Reservoirs - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/349651086_CO2-Fluid-Rock_Interactions_and_the_Coupled_Geomechanical_Response_during_CCUS_Processes_in_Unconventional_Reservoirs
39. Carbon Transport and Storage Multi-Year Program Plan - Department of Energy, accessed July 28, 2025, https://www.energy.gov/sites/default/files/2024-12/Carbon%20Transport%20and%20Storage%20Multi-Year%20Program%20Plan.pdf
40. (PDF) Reactive Transport Modeling of Mineral Precipitation and Carbon Trapping in Discrete Fracture Networks - ResearchGate, accessed July 28, 2025, https://www.researchgate.net/publication/363523038_Reactive_Transport_Modeling_of_Mineral_Precipitation_and_Carbon_Trapping_in_Discrete_Fracture_Networks
41. On the Movement of Multiple Reaction Zones in Porous Media, accessed July 28, 2025, https://deepblue.lib.umich.edu/bitstream/handle/2027.42/37381/690260312_ftp.pdf?sequence=1
42. Sandstone acidizing: the development of design methods (Conference) | OSTI.GOV, accessed July 28, 2025, https://www.osti.gov/biblio/6543410