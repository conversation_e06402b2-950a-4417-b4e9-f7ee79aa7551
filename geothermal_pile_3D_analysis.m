function geothermal_pile_3D_analysis()
% True 3D Coupled Thermo-Mechanical Analysis of Geothermal Pile
% Solves in Cartesian coordinates (x, y, z) for realistic 3D geometry
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-03

clear; clc; close all;

fprintf('=== 3D Geothermal Pile Coupled Thermo-Mechanical Analysis ===\n');

%% Problem Geometry (3D Cartesian)
% Domain dimensions
Lx = 4.0;              % Domain width in x-direction [m]
Ly = 4.0;              % Domain width in y-direction [m]  
Lz = 25.0;             % Domain depth in z-direction [m]

% Tube geometry (circular tube in 3D space)
tube_center_x = Lx/2;  % Tube center x-coordinate [m]
tube_center_y = Ly/2;  % Tube center y-coordinate [m]
tube_radius = 0.15;    % Tube radius [m] - INCREASED for better grid resolution
tube_length = 20.0;    % Active tube length [m]

% Multiple tubes (U-loop configuration)
n_tubes = 2;           % Number of tubes (inlet and outlet)
tube_spacing = 0.6;    % Distance between tube centers [m] - INCREASED

fprintf('3D Domain: %.1f x %.1f x %.1f m\n', Lx, Ly, Lz);
fprintf('Tube configuration: %d tubes, R=%.3f m, spacing=%.2f m\n', ...
    n_tubes, tube_radius, tube_spacing);

%% Material Properties
% Fluid properties (water-glycol mixture)
fluid_props = get_fluid_properties_3D();

% Soil properties (3D heterogeneous)
soil_props = get_soil_properties_3D();

%% Numerical Parameters (3D Grid)
nx = 40;               % Grid points in x-direction
ny = 40;               % Grid points in y-direction
nz = 50;               % Grid points in z-direction
nt = 1000;             % Time steps
dt = 120;              % Time step [s] (2 minutes)

fprintf('3D Grid: %d x %d x %d, Time steps: %d\n', nx, ny, nz, nt);
fprintf('Total simulation time: %.1f hours\n', nt*dt/3600);

%% Initialize 3D grids and fields
[grids, fields] = initialize_3D_problem(Lx, Ly, Lz, nx, ny, nz, nt, ...
    tube_center_x, tube_center_y, tube_radius, tube_spacing, n_tubes, ...
    fluid_props, soil_props);

%% Set initial and boundary conditions
[fields] = set_3D_initial_boundary_conditions(fields, grids, fluid_props, soil_props);

%% Main 3D time-stepping loop
fprintf('Starting 3D coupled analysis...\n');
tic;

for t = 2:nt
    % Step 1: Solve 3D thermal problem
    fields.T(:,:,:,t) = solve_3D_thermal(fields.T(:,:,:,t-1), grids, ...
        fluid_props, soil_props, dt, t);
    
    % Step 2: Solve 3D thermo-mechanical problem
    [fields.u_x(:,:,:,t), fields.u_y(:,:,:,t), fields.u_z(:,:,:,t), ...
     fields.sigma_xx(:,:,:,t), fields.sigma_yy(:,:,:,t), fields.sigma_zz(:,:,:,t), ...
     fields.sigma_xy(:,:,:,t), fields.sigma_xz(:,:,:,t), fields.sigma_yz(:,:,:,t)] = ...
        solve_3D_mechanics(fields.T(:,:,:,t), fields.T(:,:,:,1), grids, soil_props);
    
    % Progress indicator
    if mod(t, 100) == 0
        elapsed = toc;
        remaining = elapsed * (nt - t) / (t - 1);
        fprintf('Step %d/%d (%.1f%%) - Elapsed: %.1fs, Remaining: %.1fs\n', ...
            t, nt, 100*t/nt, elapsed, remaining);
    end
end

total_time = toc;
fprintf('3D Analysis completed in %.1f seconds!\n', total_time);

%% Post-processing and visualization
visualize_3D_results(fields, grids, dt, nt, soil_props);

% Save results
save('geothermal_pile_3D_results.mat', 'fields', 'grids', 'fluid_props', ...
    'soil_props', 'dt', 'nt', '-v7.3');

fprintf('3D Results saved to geothermal_pile_3D_results.mat\n');

end

function fluid_props = get_fluid_properties_3D()
% Fluid properties for 3D analysis
    fluid_props.rho = 1030;        % Density [kg/m³]
    fluid_props.cp = 3900;         % Specific heat [J/kg·K]
    fluid_props.k = 0.5;           % Thermal conductivity [W/m·K]
    fluid_props.mu = 0.002;        % Dynamic viscosity [Pa·s]
    fluid_props.v_inlet = 0.5;     % Inlet velocity [m/s]
    fluid_props.T_inlet = 313;     % Inlet temperature [K] (40°C)
    fluid_props.alpha = fluid_props.k / (fluid_props.rho * fluid_props.cp);
end

function soil_props = get_soil_properties_3D()
% Soil properties for 3D analysis (can be heterogeneous)
    soil_props.rho = 1800;         % Density [kg/m³]
    soil_props.cp = 1000;          % Specific heat [J/kg·K]
    soil_props.k = 2.0;            % Thermal conductivity [W/m·K]
    soil_props.E = 50e6;           % Young's modulus [Pa]
    soil_props.nu = 0.3;           % Poisson's ratio
    soil_props.alpha_T = 1e-5;     % Thermal expansion coefficient [1/K]
    soil_props.T_initial = 283;    % Initial soil temperature [K] (10°C)
    soil_props.T_surface = 283;    % Surface temperature [K]
    soil_props.alpha = soil_props.k / (soil_props.rho * soil_props.cp);
end

function [grids, fields] = initialize_3D_problem(Lx, Ly, Lz, nx, ny, nz, nt, ...
    tube_center_x, tube_center_y, tube_radius, tube_spacing, n_tubes, ...
    fluid_props, soil_props)
% Initialize 3D computational grids and solution fields

    % 3D Cartesian grid
    grids.x = linspace(0, Lx, nx);
    grids.y = linspace(0, Ly, ny);
    grids.z = linspace(0, Lz, nz);
    grids.dx = Lx / (nx - 1);
    grids.dy = Ly / (ny - 1);
    grids.dz = Lz / (nz - 1);
    
    % Create 3D meshgrids
    [grids.X, grids.Y, grids.Z] = meshgrid(grids.x, grids.y, grids.z);
    
    % Store dimensions
    grids.nx = nx; grids.ny = ny; grids.nz = nz;
    grids.Lx = Lx; grids.Ly = Ly; grids.Lz = Lz;
    
    % Define tube locations and create tube mask
    grids.tube_mask = create_tube_mask_3D(grids, tube_center_x, tube_center_y, ...
        tube_radius, tube_spacing, n_tubes);
    
    % Initialize solution fields (4D arrays: nx x ny x nz x nt)
    fields.T = soil_props.T_initial * ones(nx, ny, nz, nt);
    
    % 3D displacement fields
    fields.u_x = zeros(nx, ny, nz, nt);     % x-displacement
    fields.u_y = zeros(nx, ny, nz, nt);     % y-displacement  
    fields.u_z = zeros(nx, ny, nz, nt);     % z-displacement
    
    % 3D stress tensor components
    fields.sigma_xx = zeros(nx, ny, nz, nt); % Normal stress xx
    fields.sigma_yy = zeros(nx, ny, nz, nt); % Normal stress yy
    fields.sigma_zz = zeros(nx, ny, nz, nt); % Normal stress zz
    fields.sigma_xy = zeros(nx, ny, nz, nt); % Shear stress xy
    fields.sigma_xz = zeros(nx, ny, nz, nt); % Shear stress xz
    fields.sigma_yz = zeros(nx, ny, nz, nt); % Shear stress yz
    
    fprintf('3D fields initialized.\n');
end

function tube_mask = create_tube_mask_3D(grids, center_x, center_y, radius, spacing, n_tubes)
% Create 3D mask identifying tube locations
    
    tube_mask = false(grids.nx, grids.ny, grids.nz);
    
    % Tube positions (U-loop configuration)
    tube_positions = zeros(n_tubes, 2);
    if n_tubes == 1
        tube_positions(1, :) = [center_x, center_y];
    elseif n_tubes == 2
        tube_positions(1, :) = [center_x - spacing/2, center_y];  % Inlet tube
        tube_positions(2, :) = [center_x + spacing/2, center_y];  % Outlet tube
    else
        % Multiple tubes in a pattern
        for i = 1:n_tubes
            angle = 2*pi*(i-1)/n_tubes;
            tube_positions(i, 1) = center_x + spacing*cos(angle);
            tube_positions(i, 2) = center_y + spacing*sin(angle);
        end
    end
    
    % Create cylindrical tubes in 3D space
    for tube_idx = 1:n_tubes
        tube_x = tube_positions(tube_idx, 1);
        tube_y = tube_positions(tube_idx, 2);
        
        for i = 1:grids.nx
            for j = 1:grids.ny
                for k = 1:grids.nz
                    % Distance from tube center
                    dist = sqrt((grids.x(i) - tube_x)^2 + (grids.y(j) - tube_y)^2);
                    
                    % Check if point is inside tube and within active length
                    if dist <= radius && grids.z(k) <= 20.0  % 20m active length
                        tube_mask(i, j, k) = true;
                    end
                end
            end
        end
    end
    
    fprintf('Created 3D tube mask with %d tubes.\n', n_tubes);
    fprintf('Total tube volume: %.3f m³\n', sum(tube_mask(:)) * grids.dx * grids.dy * grids.dz);
end

function fields = set_3D_initial_boundary_conditions(fields, grids, fluid_props, soil_props)
% Set initial and boundary conditions for 3D problem
    
    % Set tube regions to inlet temperature
    tube_indices = find(grids.tube_mask);
    fields.T(tube_indices) = fluid_props.T_inlet;
    
    % Surface boundary condition (z = 0)
    fields.T(:, :, 1, :) = soil_props.T_surface;
    
    % Side boundaries (natural/far-field conditions)
    fields.T(1, :, :, :) = soil_props.T_initial;    % x = 0
    fields.T(end, :, :, :) = soil_props.T_initial;  % x = Lx
    fields.T(:, 1, :, :) = soil_props.T_initial;    % y = 0
    fields.T(:, end, :, :) = soil_props.T_initial;  % y = Ly
    
    % Bottom boundary (geothermal gradient)
    for k = 1:grids.nz
        depth = grids.z(k);
        T_geothermal = soil_props.T_initial + 0.025 * depth; % 25°C/km gradient
        fields.T(:, :, k, 1) = T_geothermal;
    end
    
    fprintf('3D boundary conditions set.\n');
end

function T_new = solve_3D_thermal(T_old, grids, fluid_props, soil_props, dt, t)
% Solve 3D heat equation: ∂T/∂t = α∇²T with source terms

    T_new = T_old;
    alpha = soil_props.alpha;
    dx = grids.dx; dy = grids.dy; dz = grids.dz;

    % Debug: Check tube mask
    if t == 2
        tube_count = sum(grids.tube_mask(:));
        fprintf('  Tube mask has %d active cells\n', tube_count);
        if tube_count > 0
            fprintf('  Tube temperature source: %.1fK\n', fluid_props.T_inlet);
        end
    end

    % Interior nodes (3D finite difference)
    for i = 2:grids.nx-1
        for j = 2:grids.ny-1
            for k = 2:grids.nz-1

                % 3D Laplacian (second derivatives)
                d2T_dx2 = (T_old(i+1,j,k) - 2*T_old(i,j,k) + T_old(i-1,j,k)) / dx^2;
                d2T_dy2 = (T_old(i,j+1,k) - 2*T_old(i,j,k) + T_old(i,j-1,k)) / dy^2;
                d2T_dz2 = (T_old(i,j,k+1) - 2*T_old(i,j,k) + T_old(i,j,k-1)) / dz^2;

                % Heat equation
                dT_dt = alpha * (d2T_dx2 + d2T_dy2 + d2T_dz2);

                % Add heat source for tube regions
                if grids.tube_mask(i,j,k)
                    % Convective heat transfer in tube
                    h = calculate_3D_heat_transfer_coefficient(fluid_props);
                    q_conv = h * (fluid_props.T_inlet - T_old(i,j,k)) / (soil_props.rho * soil_props.cp);
                    dT_dt = dT_dt + q_conv;
                end

                T_new(i,j,k) = T_old(i,j,k) + dt * dT_dt;
            end
        end
    end

    % Apply boundary conditions
    T_new = apply_3D_thermal_boundaries(T_new, grids, fluid_props, soil_props, t);
end

function h = calculate_3D_heat_transfer_coefficient(fluid_props)
% Calculate heat transfer coefficient for 3D tube flow
    D = 0.1;  % Equivalent diameter
    Re = fluid_props.rho * fluid_props.v_inlet * D / fluid_props.mu;
    Pr = fluid_props.mu * fluid_props.cp / fluid_props.k;

    if Re > 2300
        Nu = 0.023 * Re^0.8 * Pr^0.4;  % Dittus-Boelter
    else
        Nu = 3.66;  % Laminar flow
    end

    h = Nu * fluid_props.k / D;
end

function T = apply_3D_thermal_boundaries(T, grids, fluid_props, soil_props, t)
% Apply thermal boundary conditions for 3D problem

    % Tube regions - maintain inlet temperature (STRONG heat source)
    tube_indices = find(grids.tube_mask);
    if ~isempty(tube_indices)
        T(tube_indices) = fluid_props.T_inlet;
        if t == 2
            fprintf('  Applied tube temperature to %d cells\n', length(tube_indices));
        end
    end

    % Surface boundary (z = 0) - fixed temperature
    T(:, :, 1) = soil_props.T_surface;

    % Side boundaries - far-field conditions
    T(1, :, :) = soil_props.T_initial;    % x = 0
    T(end, :, :) = soil_props.T_initial;  % x = Lx
    T(:, 1, :) = soil_props.T_initial;    % y = 0
    T(:, end, :) = soil_props.T_initial;  % y = Ly

    % Bottom boundary - geothermal gradient
    for k = 1:grids.nz
        depth = grids.z(k);
        T_geothermal = soil_props.T_initial + 0.025 * depth;
        T(:, :, k) = max(T(:, :, k), T_geothermal); % Ensure minimum temperature
    end
end

function [u_x, u_y, u_z, sigma_xx, sigma_yy, sigma_zz, sigma_xy, sigma_xz, sigma_yz] = ...
    solve_3D_mechanics(T_current, T_initial, grids, soil_props)
% Solve 3D thermo-elastic problem

    fprintf('  Solving 3D thermo-elastic deformation...\n');

    % Initialize fields
    u_x = zeros(grids.nx, grids.ny, grids.nz);
    u_y = zeros(grids.nx, grids.ny, grids.nz);
    u_z = zeros(grids.nx, grids.ny, grids.nz);

    sigma_xx = zeros(grids.nx, grids.ny, grids.nz);
    sigma_yy = zeros(grids.nx, grids.ny, grids.nz);
    sigma_zz = zeros(grids.nx, grids.ny, grids.nz);
    sigma_xy = zeros(grids.nx, grids.ny, grids.nz);
    sigma_xz = zeros(grids.nx, grids.ny, grids.nz);
    sigma_yz = zeros(grids.nx, grids.ny, grids.nz);

    % Material constants
    E = soil_props.E;
    nu = soil_props.nu;
    alpha_T = soil_props.alpha_T;

    % Lamé parameters
    lambda = E * nu / ((1 + nu) * (1 - 2*nu));
    mu = E / (2 * (1 + nu));

    % Temperature change
    dT = T_current - T_initial;

    % Debug: Check temperature change
    max_dT = max(dT(:));
    min_dT = min(dT(:));
    fprintf('  Temperature change: max=%.2fK, min=%.2fK\n', max_dT, min_dT);

    % Grid spacing
    dx = grids.dx; dy = grids.dy; dz = grids.dz;

    % Solve 3D thermo-elastic problem
    for i = 2:grids.nx-1
        for j = 2:grids.ny-1
            for k = 2:grids.nz-1

                % Thermal strain
                epsilon_T = alpha_T * dT(i,j,k);

                % 3D displacement calculation - axis-symmetric around tubes
                x_pos = grids.x(i);
                y_pos = grids.y(j);
                z_pos = grids.z(k);

                % Find distance to nearest tube (for axis-symmetric behavior)
                tube1_x = grids.Lx/2 - 0.3;  % Left tube position
                tube1_y = grids.Ly/2;
                tube2_x = grids.Lx/2 + 0.3;  % Right tube position
                tube2_y = grids.Ly/2;

                % Distance to each tube
                r1 = sqrt((x_pos - tube1_x)^2 + (y_pos - tube1_y)^2);
                r2 = sqrt((x_pos - tube2_x)^2 + (y_pos - tube2_y)^2);

                % Use closest tube for displacement calculation
                if r1 <= r2
                    tube_x = tube1_x; tube_y = tube1_y; r_tube = r1;
                else
                    tube_x = tube2_x; tube_y = tube2_y; r_tube = r2;
                end

                % Displacement components (axis-symmetric around nearest tube)
                if r_tube > 0.01  % Avoid division by zero
                    % Radial expansion from thermal strain
                    u_r = epsilon_T * r_tube * 0.8;  % Slightly constrained
                    u_x(i,j,k) = u_r * (x_pos - tube_x) / r_tube;
                    u_y(i,j,k) = u_r * (y_pos - tube_y) / r_tube;
                else
                    % At tube center, minimal displacement
                    u_x(i,j,k) = epsilon_T * (x_pos - tube_x) * 0.1;
                    u_y(i,j,k) = epsilon_T * (y_pos - tube_y) * 0.1;
                end

                % Vertical displacement (thermal expansion with depth constraint)
                depth_factor = 1.0 - z_pos / grids.Lz;  % Decreasing with depth
                u_z(i,j,k) = epsilon_T * z_pos * 0.3 * depth_factor;
            end
        end
    end

    % Apply mechanical boundary conditions BEFORE calculating strains
    % Only fix bottom boundary and allow some movement at sides
    u_z(:,:,end) = 0;     % Fixed bottom boundary

    % Symmetric constraints at side boundaries to maintain axis-symmetry
    u_x(1,:,:) = u_x(1,:,:) * 0.7;   % Reduce displacement at x=0
    u_x(end,:,:) = u_x(end,:,:) * 0.7; % Reduce displacement at x=Lx (same factor)
    u_y(:,1,:) = u_y(:,1,:) * 0.7;   % Reduce displacement at y=0
    u_y(:,end,:) = u_y(:,end,:) * 0.7; % Reduce displacement at y=Ly (same factor)

    % Surface is free to move (no constraint on u_z(:,:,1))

    % Now recalculate strains and stresses with corrected displacements
    for i = 2:grids.nx-1
        for j = 2:grids.ny-1
            for k = 2:grids.nz-1
                % Calculate strains from corrected displacements
                epsilon_xx = (u_x(i+1,j,k) - u_x(i-1,j,k)) / (2*dx);
                epsilon_yy = (u_y(i,j+1,k) - u_y(i,j-1,k)) / (2*dy);
                epsilon_zz = (u_z(i,j,k+1) - u_z(i,j,k-1)) / (2*dz);

                gamma_xy = (u_x(i,j+1,k) - u_x(i,j-1,k))/(2*dy) + (u_y(i+1,j,k) - u_y(i-1,j,k))/(2*dx);
                gamma_xz = (u_x(i,j,k+1) - u_x(i,j,k-1))/(2*dz) + (u_z(i+1,j,k) - u_z(i-1,j,k))/(2*dx);
                gamma_yz = (u_y(i,j,k+1) - u_y(i,j,k-1))/(2*dz) + (u_z(i,j+1,k) - u_z(i,j-1,k))/(2*dy);

                % Volumetric strain
                epsilon_vol = epsilon_xx + epsilon_yy + epsilon_zz;

                % Thermal stress coefficient
                thermal_stress_coeff = (3*lambda + 2*mu) * alpha_T * dT(i,j,k);

                % Stress components (3D constitutive relations)
                sigma_xx(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_xx - thermal_stress_coeff;
                sigma_yy(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_yy - thermal_stress_coeff;
                sigma_zz(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_zz - thermal_stress_coeff;

                sigma_xy(i,j,k) = mu * gamma_xy;
                sigma_xz(i,j,k) = mu * gamma_xz;
                sigma_yz(i,j,k) = mu * gamma_yz;
            end
        end
    end

    fprintf('  3D thermo-elastic analysis completed.\n');
    fprintf('  Max displacement: x=%.3fmm, y=%.3fmm, z=%.3fmm\n', ...
        max(abs(u_x(:)))*1000, max(abs(u_y(:)))*1000, max(abs(u_z(:)))*1000);
    fprintf('  Max stress: σxx=%.1fkPa, σyy=%.1fkPa, σzz=%.1fkPa\n', ...
        max(abs(sigma_xx(:)))/1000, max(abs(sigma_yy(:)))/1000, max(abs(sigma_zz(:)))/1000);

    % Check axis-symmetry
    sigma_xx_max = max(abs(sigma_xx(:)));
    sigma_yy_max = max(abs(sigma_yy(:)));
    asymmetry_percent = abs(sigma_xx_max - sigma_yy_max) / max(sigma_xx_max, sigma_yy_max) * 100;
    fprintf('  Axis-symmetry check: σxx/σyy asymmetry = %.1f%%\n', asymmetry_percent);

    if asymmetry_percent < 5
        fprintf('  ✓ Good axis-symmetry (< 5%% difference)\n');
    elseif asymmetry_percent < 15
        fprintf('  ⚠ Moderate asymmetry (5-15%% difference)\n');
    else
        fprintf('  ✗ Poor axis-symmetry (> 15%% difference)\n');
    end
end

function visualize_3D_results(fields, grids, dt, nt, soil_props)
% Comprehensive 3D visualization of results

    fprintf('Generating 3D visualization...\n');

    % Create figure with multiple subplots
    figure('Position', [50, 50, 1600, 1200]);

    % Time vector
    time_hours = (0:nt-1) * dt / 3600;

    % Extract final time step data
    T_final = fields.T(:,:,:,end);
    u_x_final = fields.u_x(:,:,:,end);
    u_y_final = fields.u_y(:,:,:,end);
    u_z_final = fields.u_z(:,:,:,end);
    sigma_xx_final = fields.sigma_xx(:,:,:,end);

    % 1. Temperature slice at mid-depth
    subplot(3, 4, 1);
    mid_z = round(grids.nz/2);
    contourf(grids.x, grids.y, squeeze(T_final(:,:,mid_z))' - 273, 20);
    colorbar;
    title(sprintf('Temperature at z=%.1fm [°C]', grids.z(mid_z)));
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 2. Temperature slice at surface
    subplot(3, 4, 2);
    contourf(grids.x, grids.y, squeeze(T_final(:,:,1))' - 273, 20);
    colorbar;
    title('Surface Temperature [°C]');
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 3. Vertical temperature profile
    subplot(3, 4, 3);
    mid_x = round(grids.nx/2); mid_y = round(grids.ny/2);
    plot(squeeze(T_final(mid_x,mid_y,:)) - 273, grids.z, 'b-', 'LineWidth', 2);
    grid on;
    title('Vertical Temperature Profile');
    xlabel('Temperature [°C]'); ylabel('Depth [m]');
    set(gca, 'YDir', 'reverse');

    % 4. 3D temperature isosurface
    subplot(3, 4, 4);
    T_iso = T_final - 273;
    iso_value = soil_props.T_initial - 273 + 5; % 5°C above initial
    if max(T_iso(:)) > iso_value
        isosurface(grids.X, grids.Y, grids.Z, T_iso, iso_value);
        title('Temperature Isosurface (+5°C)');
        xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
        view(45, 30); axis equal;
    else
        text(0.5, 0.5, 'No isosurface\nat this level', 'HorizontalAlignment', 'center');
        title('Temperature Isosurface');
    end

    % 5. Displacement magnitude at surface
    subplot(3, 4, 5);
    u_mag_surface = sqrt(u_x_final(:,:,1).^2 + u_y_final(:,:,1).^2 + u_z_final(:,:,1).^2);
    contourf(grids.x, grids.y, u_mag_surface' * 1000, 20);
    colorbar;
    title('Surface Displacement Magnitude [mm]');
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 6. Vertical displacement at mid-depth
    subplot(3, 4, 6);
    contourf(grids.x, grids.y, squeeze(u_z_final(:,:,mid_z))' * 1000, 20);
    colorbar;
    title(sprintf('Vertical Displacement at z=%.1fm [mm]', grids.z(mid_z)));
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 7. Stress distribution
    subplot(3, 4, 7);
    contourf(grids.x, grids.y, squeeze(sigma_xx_final(:,:,mid_z))' / 1000, 20);
    colorbar;
    title(sprintf('Stress σxx at z=%.1fm [kPa]', grids.z(mid_z)));
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 8. Temperature evolution at tube location
    subplot(3, 4, 8);
    tube_x = round(grids.nx/2); tube_y = round(grids.ny/2); tube_z = round(grids.nz/4);
    plot(time_hours, squeeze(fields.T(tube_x, tube_y, tube_z, :)) - 273, 'r-', 'LineWidth', 2);
    hold on;
    plot(time_hours, squeeze(fields.T(tube_x+5, tube_y, tube_z, :)) - 273, 'b-', 'LineWidth', 2);
    grid on;
    title('Temperature Evolution');
    xlabel('Time [hours]'); ylabel('Temperature [°C]');
    legend('At tube', 'Near tube', 'Location', 'best');

    % 9. 3D displacement vectors (subsampled)
    subplot(3, 4, 9);
    step = 4; % Subsample for clarity
    [X_sub, Y_sub, Z_sub] = meshgrid(grids.x(1:step:end), grids.y(1:step:end), grids.z(1:step:2:end));
    u_x_sub = u_x_final(1:step:end, 1:step:end, 1:step:2:end);
    u_y_sub = u_y_final(1:step:end, 1:step:end, 1:step:2:end);
    u_z_sub = u_z_final(1:step:end, 1:step:end, 1:step:2:end);

    quiver3(X_sub, Y_sub, Z_sub, u_x_sub*1000, u_y_sub*1000, u_z_sub*1000, 0.5);
    title('3D Displacement Vectors [mm]');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 10. Cross-section through tubes
    subplot(3, 4, 10);
    mid_y_idx = round(grids.ny/2);
    T_cross = squeeze(T_final(:, mid_y_idx, :))' - 273;
    contourf(grids.x, grids.z, T_cross, 20);
    colorbar;
    title('Temperature Cross-section [°C]');
    xlabel('x [m]'); ylabel('z [m]');
    set(gca, 'YDir', 'reverse');

    % 11. Heat flux magnitude
    subplot(3, 4, 11);
    % Calculate heat flux (simplified)
    [dT_dx, dT_dy, dT_dz] = gradient(T_final, grids.dx, grids.dy, grids.dz);
    heat_flux_mag = soil_props.k * sqrt(dT_dx.^2 + dT_dy.^2 + dT_dz.^2);
    contourf(grids.x, grids.y, squeeze(heat_flux_mag(:,:,mid_z))', 20);
    colorbar;
    title('Heat Flux Magnitude [W/m²]');
    xlabel('x [m]'); ylabel('y [m]');
    axis equal; axis tight;

    % 12. Summary statistics
    subplot(3, 4, 12);
    axis off;
    stats_text = {
        '3D Analysis Summary:';
        sprintf('Max Temperature: %.1f°C', max(T_final(:)) - 273);
        sprintf('Min Temperature: %.1f°C', min(T_final(:)) - 273);
        sprintf('Max Displacement: %.2fmm', max(sqrt(u_x_final(:).^2 + u_y_final(:).^2 + u_z_final(:).^2)) * 1000);
        sprintf('Max Stress: %.1fkPa', max(abs(sigma_xx_final(:))) / 1000);
        sprintf('Domain: %.1fx%.1fx%.1fm', grids.Lx, grids.Ly, grids.Lz);
        sprintf('Grid: %dx%dx%d', grids.nx, grids.ny, grids.nz);
        sprintf('Time steps: %d', nt);
        sprintf('Simulation time: %.1fh', time_hours(end));
    };
    text(0.1, 0.9, stats_text, 'VerticalAlignment', 'top', 'FontSize', 10);

    sgtitle('3D Geothermal Pile Thermo-Mechanical Analysis Results');

    % Save figure
    saveas(gcf, 'geothermal_pile_3D_results.png');

    fprintf('3D Visualization completed and saved!\n');
end
