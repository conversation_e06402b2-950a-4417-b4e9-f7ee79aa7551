function geothermal_pile_U_tube_analysis()
% Enhanced 3D Geothermal Pile Analysis with U-shaped Tube
% Includes proper convection-conduction coupling and realistic U-tube geometry
% Author: Generated for Advanced Geothermal Pile Analysis
% Date: 2025-07-03

clear; clc; close all;

fprintf('=== Enhanced 3D Geothermal Pile with U-Tube Analysis ===\n');

%% Problem Geometry (3D U-shaped tube)
% Domain dimensions
Lx = 4.0;              % Domain width in x-direction [m]
Ly = 4.0;              % Domain width in y-direction [m]  
Lz = 25.0;             % Domain depth in z-direction [m]

% U-tube geometry
tube_radius = 0.08;    % Tube radius [m] (larger for better resolution)
tube_depth = 20.0;     % Active tube depth [m]
tube_separation = 0.4; % Distance between inlet and outlet tubes [m]
u_bend_radius = 0.3;   % Radius of U-bend at bottom [m]

% Tube positions
inlet_x = Lx/2 - tube_separation/2;   % Inlet tube x-position
outlet_x = Lx/2 + tube_separation/2;  % Outlet tube x-position
tube_y = Ly/2;                        % Both tubes at domain center in y

fprintf('U-Tube Configuration:\n');
fprintf('  Tube radius: %.3f m\n', tube_radius);
fprintf('  Tube separation: %.3f m\n', tube_separation);
fprintf('  Active depth: %.1f m\n', tube_depth);
fprintf('  U-bend radius: %.3f m\n', u_bend_radius);

%% Material Properties
% Enhanced fluid properties with flow characteristics
fluid_props = get_enhanced_fluid_properties();

% Soil properties
soil_props = get_soil_properties_enhanced();

%% Numerical Parameters (3D Grid)
nx = 50;               % Grid points in x-direction (increased resolution)
ny = 50;               % Grid points in y-direction
nz = 60;               % Grid points in z-direction (increased for depth)
nt = 800;              % Time steps
dt = 180;              % Time step [s] (3 minutes)

fprintf('Enhanced 3D Grid: %d x %d x %d, Time steps: %d\n', nx, ny, nz, nt);
fprintf('Total simulation time: %.1f hours\n', nt*dt/3600);

%% Initialize enhanced 3D grids and fields
[grids, fields] = initialize_U_tube_problem(Lx, Ly, Lz, nx, ny, nz, nt, ...
    inlet_x, outlet_x, tube_y, tube_radius, tube_depth, u_bend_radius, ...
    fluid_props, soil_props);

%% Set initial and boundary conditions for U-tube
[fields] = set_U_tube_initial_boundary_conditions(fields, grids, fluid_props, soil_props);

%% Main enhanced time-stepping loop
fprintf('Starting enhanced U-tube coupled analysis...\n');
tic;

for t = 2:nt
    % Step 1: Solve enhanced thermal problem with convection-conduction coupling
    fields.T(:,:,:,t) = solve_convection_conduction_3D(fields.T(:,:,:,t-1), ...
        grids, fluid_props, soil_props, dt, t);
    
    % Step 2: Update fluid velocity field based on temperature (buoyancy effects)
    grids = update_velocity_field(grids, fields.T(:,:,:,t), fluid_props, dt);
    
    % Step 3: Solve 3D thermo-mechanical problem
    [fields.u_x(:,:,:,t), fields.u_y(:,:,:,t), fields.u_z(:,:,:,t), ...
     fields.sigma_xx(:,:,:,t), fields.sigma_yy(:,:,:,t), fields.sigma_zz(:,:,:,t), ...
     fields.sigma_xy(:,:,:,t), fields.sigma_xz(:,:,:,t), fields.sigma_yz(:,:,:,t)] = ...
        solve_3D_mechanics_enhanced(fields.T(:,:,:,t), fields.T(:,:,:,1), grids, soil_props);
    
    % Progress indicator
    if mod(t, 80) == 0
        elapsed = toc;
        remaining = elapsed * (nt - t) / (t - 1);
        fprintf('Step %d/%d (%.1f%%) - Elapsed: %.1fs, Remaining: %.1fs\n', ...
            t, nt, 100*t/nt, elapsed, remaining);
        
        % Display key results
        max_T = max(fields.T(:,:,:,t), [], 'all') - 273;
        min_T = min(fields.T(:,:,:,t), [], 'all') - 273;
        fprintf('  Temperature range: %.1f°C to %.1f°C\n', min_T, max_T);
    end
end

total_time = toc;
fprintf('Enhanced U-tube analysis completed in %.1f seconds!\n', total_time);

%% Enhanced post-processing and visualization
visualize_U_tube_results(fields, grids, dt, nt, soil_props, fluid_props);

% Save enhanced results
save('geothermal_pile_U_tube_results.mat', 'fields', 'grids', 'fluid_props', ...
    'soil_props', 'dt', 'nt', '-v7.3');

fprintf('Enhanced U-tube results saved to geothermal_pile_U_tube_results.mat\n');

end

function fluid_props = get_enhanced_fluid_properties()
% Enhanced fluid properties with convection characteristics
    fluid_props.rho = 1030;        % Density [kg/m³]
    fluid_props.cp = 3900;         % Specific heat [J/kg·K]
    fluid_props.k = 0.5;           % Thermal conductivity [W/m·K]
    fluid_props.mu = 0.002;        % Dynamic viscosity [Pa·s]
    fluid_props.v_inlet = 0.8;     % Inlet velocity [m/s] (increased)
    fluid_props.T_inlet = 318;     % Inlet temperature [K] (45°C)
    fluid_props.T_outlet = 283;    % Expected outlet temperature [K] (10°C)
    fluid_props.alpha = fluid_props.k / (fluid_props.rho * fluid_props.cp);
    
    % Thermal expansion coefficient for buoyancy
    fluid_props.beta = 3e-4;       % Thermal expansion coefficient [1/K]
    fluid_props.g = 9.81;          % Gravitational acceleration [m/s²]
    
    % Flow characteristics
    fluid_props.Re = fluid_props.rho * fluid_props.v_inlet * 0.16 / fluid_props.mu; % Reynolds number
    fluid_props.Pr = fluid_props.mu * fluid_props.cp / fluid_props.k;               % Prandtl number
    
    fprintf('Fluid properties:\n');
    fprintf('  Reynolds number: %.0f\n', fluid_props.Re);
    fprintf('  Prandtl number: %.2f\n', fluid_props.Pr);
    fprintf('  Inlet velocity: %.2f m/s\n', fluid_props.v_inlet);
end

function soil_props = get_soil_properties_enhanced()
% Enhanced soil properties
    soil_props.rho = 1800;         % Density [kg/m³]
    soil_props.cp = 1000;          % Specific heat [J/kg·K]
    soil_props.k = 2.2;            % Thermal conductivity [W/m·K] (slightly higher)
    soil_props.E = 50e6;           % Young's modulus [Pa]
    soil_props.nu = 0.3;           % Poisson's ratio
    soil_props.alpha_T = 1e-5;     % Thermal expansion coefficient [1/K]
    soil_props.T_initial = 283;    % Initial soil temperature [K] (10°C)
    soil_props.T_surface = 283;    % Surface temperature [K]
    soil_props.alpha = soil_props.k / (soil_props.rho * soil_props.cp);
    
    % Geothermal gradient
    soil_props.geothermal_gradient = 0.025; % [K/m] (25°C/km)
end

function [grids, fields] = initialize_U_tube_problem(Lx, Ly, Lz, nx, ny, nz, nt, ...
    inlet_x, outlet_x, tube_y, tube_radius, tube_depth, u_bend_radius, ...
    fluid_props, soil_props)
% Initialize 3D computational grids and solution fields for U-tube

    % 3D Cartesian grid
    grids.x = linspace(0, Lx, nx);
    grids.y = linspace(0, Ly, ny);
    grids.z = linspace(0, Lz, nz);
    grids.dx = Lx / (nx - 1);
    grids.dy = Ly / (ny - 1);
    grids.dz = Lz / (nz - 1);
    
    % Create 3D meshgrids
    [grids.X, grids.Y, grids.Z] = meshgrid(grids.x, grids.y, grids.z);
    
    % Store dimensions and geometry
    grids.nx = nx; grids.ny = ny; grids.nz = nz;
    grids.Lx = Lx; grids.Ly = Ly; grids.Lz = Lz;
    grids.inlet_x = inlet_x; grids.outlet_x = outlet_x; grids.tube_y = tube_y;
    grids.tube_radius = tube_radius; grids.tube_depth = tube_depth;
    grids.u_bend_radius = u_bend_radius;
    
    % Create U-tube mask and velocity field
    [grids.tube_mask, grids.inlet_mask, grids.outlet_mask, grids.u_bend_mask] = ...
        create_U_tube_mask_3D(grids);
    
    % Initialize 3D velocity field for U-tube flow
    [grids.u_vel, grids.v_vel, grids.w_vel] = initialize_U_tube_velocity_field(grids, fluid_props);
    
    % Initialize solution fields (4D arrays: nx x ny x nz x nt)
    fields.T = soil_props.T_initial * ones(nx, ny, nz, nt);
    
    % Apply geothermal gradient to initial temperature
    for k = 1:nz
        depth = grids.z(k);
        T_geothermal = soil_props.T_initial + soil_props.geothermal_gradient * depth;
        fields.T(:, :, k, 1) = T_geothermal;
    end
    
    % 3D displacement fields
    fields.u_x = zeros(nx, ny, nz, nt);     % x-displacement
    fields.u_y = zeros(nx, ny, nz, nt);     % y-displacement  
    fields.u_z = zeros(nx, ny, nz, nt);     % z-displacement
    
    % 3D stress tensor components
    fields.sigma_xx = zeros(nx, ny, nz, nt); % Normal stress xx
    fields.sigma_yy = zeros(nx, ny, nz, nt); % Normal stress yy
    fields.sigma_zz = zeros(nx, ny, nz, nt); % Normal stress zz
    fields.sigma_xy = zeros(nx, ny, nz, nt); % Shear stress xy
    fields.sigma_xz = zeros(nx, ny, nz, nt); % Shear stress xz
    fields.sigma_yz = zeros(nx, ny, nz, nt); % Shear stress yz
    
    fprintf('U-tube 3D fields initialized.\n');
    fprintf('Total tube volume: %.3f m³\n', sum(grids.tube_mask(:)) * grids.dx * grids.dy * grids.dz);
    fprintf('Inlet cells: %d, Outlet cells: %d, U-bend cells: %d\n', ...
        sum(grids.inlet_mask(:)), sum(grids.outlet_mask(:)), sum(grids.u_bend_mask(:)));
end

function [tube_mask, inlet_mask, outlet_mask, u_bend_mask] = create_U_tube_mask_3D(grids)
% Create 3D masks for U-shaped tube geometry

    tube_mask = false(grids.nx, grids.ny, grids.nz);
    inlet_mask = false(grids.nx, grids.ny, grids.nz);
    outlet_mask = false(grids.nx, grids.ny, grids.nz);
    u_bend_mask = false(grids.nx, grids.ny, grids.nz);

    % U-bend depth (near bottom)
    u_bend_depth_start = grids.tube_depth - 2*grids.u_bend_radius;

    for i = 1:grids.nx
        for j = 1:grids.ny
            for k = 1:grids.nz
                x = grids.x(i);
                y = grids.y(j);
                z = grids.z(k);

                % Inlet tube (vertical, flowing down)
                dist_inlet = sqrt((x - grids.inlet_x)^2 + (y - grids.tube_y)^2);
                if dist_inlet <= grids.tube_radius && z <= grids.tube_depth
                    tube_mask(i,j,k) = true;
                    inlet_mask(i,j,k) = true;
                end

                % Outlet tube (vertical, flowing up)
                dist_outlet = sqrt((x - grids.outlet_x)^2 + (y - grids.tube_y)^2);
                if dist_outlet <= grids.tube_radius && z <= grids.tube_depth
                    tube_mask(i,j,k) = true;
                    outlet_mask(i,j,k) = true;
                end

                % U-bend (horizontal connection at bottom)
                if z >= u_bend_depth_start && z <= grids.tube_depth
                    % Center of U-bend
                    u_center_x = (grids.inlet_x + grids.outlet_x) / 2;
                    u_center_z = grids.tube_depth - grids.u_bend_radius;

                    % Distance from U-bend center in x-z plane
                    dist_u_bend = sqrt((x - u_center_x)^2 + (z - u_center_z)^2);

                    % U-bend geometry (semicircular)
                    if abs(y - grids.tube_y) <= grids.tube_radius && ...
                       dist_u_bend <= grids.u_bend_radius && ...
                       dist_u_bend >= grids.u_bend_radius - grids.tube_radius
                        tube_mask(i,j,k) = true;
                        u_bend_mask(i,j,k) = true;
                    end
                end
            end
        end
    end

    fprintf('Created U-tube 3D mask.\n');
end

function [u_vel, v_vel, w_vel] = initialize_U_tube_velocity_field(grids, fluid_props)
% Initialize 3D velocity field for U-tube flow

    u_vel = zeros(grids.nx, grids.ny, grids.nz);  % x-velocity
    v_vel = zeros(grids.nx, grids.ny, grids.nz);  % y-velocity
    w_vel = zeros(grids.nx, grids.ny, grids.nz);  % z-velocity (vertical)

    % Parabolic velocity profile in tubes (Poiseuille flow)
    for i = 1:grids.nx
        for j = 1:grids.ny
            for k = 1:grids.nz
                x = grids.x(i);
                y = grids.y(j);
                z = grids.z(k);

                % Inlet tube (downward flow)
                if grids.inlet_mask(i,j,k)
                    r_inlet = sqrt((x - grids.inlet_x)^2 + (y - grids.tube_y)^2);
                    velocity_factor = 1 - (r_inlet / grids.tube_radius)^2;  % Parabolic profile
                    w_vel(i,j,k) = -fluid_props.v_inlet * velocity_factor;  % Negative (downward)
                end

                % Outlet tube (upward flow)
                if grids.outlet_mask(i,j,k)
                    r_outlet = sqrt((x - grids.outlet_x)^2 + (y - grids.tube_y)^2);
                    velocity_factor = 1 - (r_outlet / grids.tube_radius)^2;  % Parabolic profile
                    w_vel(i,j,k) = fluid_props.v_inlet * velocity_factor * 0.8;  % Positive (upward), reduced due to heat loss
                end

                % U-bend (horizontal flow)
                if grids.u_bend_mask(i,j,k)
                    % Simplified horizontal flow in U-bend
                    if x < (grids.inlet_x + grids.outlet_x) / 2
                        u_vel(i,j,k) = fluid_props.v_inlet * 0.6;  % Flow from inlet to outlet
                    else
                        u_vel(i,j,k) = fluid_props.v_inlet * 0.6;
                    end
                end
            end
        end
    end

    fprintf('U-tube velocity field initialized.\n');
    fprintf('Max velocities: u=%.3f, v=%.3f, w=%.3f m/s\n', ...
        max(abs(u_vel(:))), max(abs(v_vel(:))), max(abs(w_vel(:))));
end

function fields = set_U_tube_initial_boundary_conditions(fields, grids, fluid_props, soil_props)
% Set initial and boundary conditions for U-tube problem

    % Apply inlet temperature to inlet tube
    for t = 1:size(fields.T, 4)
        fields.T(grids.inlet_mask) = fluid_props.T_inlet;
    end

    % Surface boundary condition (constant temperature)
    fields.T(:, :, 1, :) = soil_props.T_surface;

    % Deep boundary condition (geothermal gradient)
    deep_temp = soil_props.T_initial + soil_props.geothermal_gradient * grids.Lz;
    fields.T(:, :, end, :) = deep_temp;

    fprintf('U-tube boundary conditions set.\n');
    fprintf('Inlet temperature: %.1f°C\n', fluid_props.T_inlet - 273);
    fprintf('Deep boundary temperature: %.1f°C\n', deep_temp - 273);
end

function T_new = solve_convection_conduction_3D(T_old, grids, fluid_props, soil_props, dt, t)
% Solve 3D convection-conduction equation with U-tube coupling
% ∂T/∂t + u·∇T = α∇²T + source terms

    T_new = T_old;
    dx = grids.dx; dy = grids.dy; dz = grids.dz;

    % Stability criteria for explicit scheme
    alpha_max = max(fluid_props.alpha, soil_props.alpha);
    dt_max = 0.4 * min([dx^2, dy^2, dz^2]) / (6 * alpha_max);
    if dt > dt_max
        fprintf('Warning: Time step %.1fs exceeds stability limit %.1fs\n', dt, dt_max);
    end

    % Main convection-conduction loop
    for i = 2:grids.nx-1
        for j = 2:grids.ny-1
            for k = 2:grids.nz-1

                % Determine local properties
                if grids.tube_mask(i,j,k)
                    % Inside tube: use fluid properties
                    alpha = fluid_props.alpha;
                    u = grids.u_vel(i,j,k);
                    v = grids.v_vel(i,j,k);
                    w = grids.w_vel(i,j,k);

                    % Apply inlet boundary condition
                    if grids.inlet_mask(i,j,k)
                        T_new(i,j,k) = fluid_props.T_inlet;
                        continue;
                    end
                else
                    % In soil: use soil properties, no convection
                    alpha = soil_props.alpha;
                    u = 0; v = 0; w = 0;
                end

                % Temperature gradients (central differences)
                dT_dx = (T_old(i+1,j,k) - T_old(i-1,j,k)) / (2*dx);
                dT_dy = (T_old(i,j+1,k) - T_old(i,j-1,k)) / (2*dy);
                dT_dz = (T_old(i,j,k+1) - T_old(i,j,k-1)) / (2*dz);

                % Second derivatives (Laplacian)
                d2T_dx2 = (T_old(i+1,j,k) - 2*T_old(i,j,k) + T_old(i-1,j,k)) / dx^2;
                d2T_dy2 = (T_old(i,j+1,k) - 2*T_old(i,j,k) + T_old(i,j-1,k)) / dy^2;
                d2T_dz2 = (T_old(i,j,k+1) - 2*T_old(i,j,k) + T_old(i,j,k-1)) / dz^2;

                % Convection term: u·∇T
                convection = u * dT_dx + v * dT_dy + w * dT_dz;

                % Conduction term: α∇²T
                conduction = alpha * (d2T_dx2 + d2T_dy2 + d2T_dz2);

                % Heat exchange between tube and soil
                heat_exchange = 0;
                if grids.tube_mask(i,j,k)
                    % Enhanced heat transfer coefficient for tube-soil interface
                    h_exchange = 500;  % [W/m²·K] Enhanced heat transfer coefficient

                    % Average soil temperature around tube
                    T_soil_avg = 0;
                    count = 0;
                    for di = -1:1
                        for dj = -1:1
                            for dk = -1:1
                                ii = i + di; jj = j + dj; kk = k + dk;
                                if ii >= 1 && ii <= grids.nx && jj >= 1 && jj <= grids.ny && ...
                                   kk >= 1 && kk <= grids.nz && ~grids.tube_mask(ii,jj,kk)
                                    T_soil_avg = T_soil_avg + T_old(ii,jj,kk);
                                    count = count + 1;
                                end
                            end
                        end
                    end

                    if count > 0
                        T_soil_avg = T_soil_avg / count;
                        % Heat exchange rate per unit volume
                        surface_area_ratio = 4 * grids.tube_radius / (dx * dy * dz);
                        heat_exchange = h_exchange * surface_area_ratio * (T_soil_avg - T_old(i,j,k)) / ...
                                      (fluid_props.rho * fluid_props.cp);
                    end
                end

                % Time integration (explicit Euler)
                dT_dt = -convection + conduction + heat_exchange;
                T_new(i,j,k) = T_old(i,j,k) + dt * dT_dt;
            end
        end
    end

    % Apply boundary conditions
    T_new = apply_thermal_boundary_conditions(T_new, grids, fluid_props, soil_props);

    % Ensure physical temperature bounds
    T_new = max(T_new, 273);  % Above freezing
    T_new = min(T_new, 373);  % Below boiling
end

function T = apply_thermal_boundary_conditions(T, grids, fluid_props, soil_props)
% Apply thermal boundary conditions

    % Surface boundary (constant temperature)
    T(:, :, 1) = soil_props.T_surface;

    % Deep boundary (geothermal gradient)
    deep_temp = soil_props.T_initial + soil_props.geothermal_gradient * grids.Lz;
    T(:, :, end) = deep_temp;

    % Side boundaries (no-flux, Neumann)
    T(1, :, :) = T(2, :, :);
    T(end, :, :) = T(end-1, :, :);
    T(:, 1, :) = T(:, 2, :);
    T(:, end, :) = T(:, end-1, :);

    % Inlet boundary condition (constant temperature)
    T(grids.inlet_mask) = fluid_props.T_inlet;
end

function grids = update_velocity_field(grids, T, fluid_props, dt)
% Update velocity field based on temperature (buoyancy effects)

    % Reference temperature for buoyancy
    T_ref = fluid_props.T_inlet;

    % Update velocity field with buoyancy effects
    for i = 1:grids.nx
        for j = 1:grids.ny
            for k = 1:grids.nz
                if grids.tube_mask(i,j,k)
                    % Buoyancy force
                    dT = T(i,j,k) - T_ref;
                    buoyancy_factor = 1 + fluid_props.beta * dT;

                    % Update vertical velocity with buoyancy
                    if grids.inlet_mask(i,j,k)
                        % Inlet: enhanced downward flow when hot
                        grids.w_vel(i,j,k) = grids.w_vel(i,j,k) * buoyancy_factor;
                    elseif grids.outlet_mask(i,j,k)
                        % Outlet: enhanced upward flow when hot
                        grids.w_vel(i,j,k) = grids.w_vel(i,j,k) * buoyancy_factor;
                    end
                end
            end
        end
    end
end

function [u_x, u_y, u_z, sigma_xx, sigma_yy, sigma_zz, sigma_xy, sigma_xz, sigma_yz] = ...
    solve_3D_mechanics_enhanced(T_current, T_initial, grids, soil_props)
% Enhanced 3D thermo-mechanical analysis with proper axis-symmetry

    % Initialize displacement and stress fields
    u_x = zeros(grids.nx, grids.ny, grids.nz);
    u_y = zeros(grids.nx, grids.ny, grids.nz);
    u_z = zeros(grids.nx, grids.ny, grids.nz);

    sigma_xx = zeros(grids.nx, grids.ny, grids.nz);
    sigma_yy = zeros(grids.nx, grids.ny, grids.nz);
    sigma_zz = zeros(grids.nx, grids.ny, grids.nz);
    sigma_xy = zeros(grids.nx, grids.ny, grids.nz);
    sigma_xz = zeros(grids.nx, grids.ny, grids.nz);
    sigma_yz = zeros(grids.nx, grids.ny, grids.nz);

    % Temperature change
    dT = T_current - T_initial;

    % Material constants
    E = soil_props.E;
    nu = soil_props.nu;
    alpha_T = soil_props.alpha_T;

    % Lamé parameters
    lambda = E * nu / ((1 + nu) * (1 - 2*nu));
    mu = E / (2 * (1 + nu));

    dx = grids.dx; dy = grids.dy; dz = grids.dz;

    % Calculate displacements using enhanced thermal expansion model
    for i = 1:grids.nx
        for j = 1:grids.ny
            for k = 1:grids.nz
                x = grids.x(i);
                y = grids.y(j);
                z = grids.z(k);

                % Enhanced displacement calculation considering U-tube geometry
                epsilon_T = alpha_T * dT(i,j,k);

                % Distance to nearest tube for axis-symmetric behavior
                dist_to_tubes = min([
                    sqrt((x - grids.inlet_x)^2 + (y - grids.tube_y)^2),
                    sqrt((x - grids.outlet_x)^2 + (y - grids.tube_y)^2)
                ]);

                % Displacement components with enhanced axis-symmetry
                if dist_to_tubes > 0.01
                    % Radial expansion from nearest tube
                    if sqrt((x - grids.inlet_x)^2 + (y - grids.tube_y)^2) < ...
                       sqrt((x - grids.outlet_x)^2 + (y - grids.tube_y)^2)
                        % Closer to inlet tube
                        tube_x = grids.inlet_x; tube_y = grids.tube_y;
                    else
                        % Closer to outlet tube
                        tube_x = grids.outlet_x; tube_y = grids.tube_y;
                    end

                    r_tube = sqrt((x - tube_x)^2 + (y - tube_y)^2);
                    if r_tube > 0.01
                        u_r = epsilon_T * r_tube * 0.7;  % Constrained expansion
                        u_x(i,j,k) = u_r * (x - tube_x) / r_tube;
                        u_y(i,j,k) = u_r * (y - tube_y) / r_tube;
                    end
                end

                % Vertical displacement with depth constraint
                depth_factor = 1.0 - z / grids.Lz;
                u_z(i,j,k) = epsilon_T * z * 0.4 * depth_factor;
            end
        end
    end

    % Apply enhanced boundary conditions for better axis-symmetry
    u_z(:,:,end) = 0;  % Fixed bottom boundary

    % Symmetric side constraints
    constraint_factor = 0.8;
    u_x(1,:,:) = u_x(1,:,:) * constraint_factor;
    u_x(end,:,:) = u_x(end,:,:) * constraint_factor;
    u_y(:,1,:) = u_y(:,1,:) * constraint_factor;
    u_y(:,end,:) = u_y(:,end,:) * constraint_factor;

    % Calculate strains and stresses with corrected displacements
    for i = 2:grids.nx-1
        for j = 2:grids.ny-1
            for k = 2:grids.nz-1
                % Strain components
                epsilon_xx = (u_x(i+1,j,k) - u_x(i-1,j,k)) / (2*dx);
                epsilon_yy = (u_y(i,j+1,k) - u_y(i,j-1,k)) / (2*dy);
                epsilon_zz = (u_z(i,j,k+1) - u_z(i,j,k-1)) / (2*dz);

                gamma_xy = (u_x(i,j+1,k) - u_x(i,j-1,k))/(2*dy) + ...
                          (u_y(i+1,j,k) - u_y(i-1,j,k))/(2*dx);
                gamma_xz = (u_x(i,j,k+1) - u_x(i,j,k-1))/(2*dz) + ...
                          (u_z(i+1,j,k) - u_z(i-1,j,k))/(2*dx);
                gamma_yz = (u_y(i,j,k+1) - u_y(i,j,k-1))/(2*dz) + ...
                          (u_z(i,j+1,k) - u_z(i,j-1,k))/(2*dy);

                % Volumetric strain
                epsilon_vol = epsilon_xx + epsilon_yy + epsilon_zz;

                % Thermal stress coefficient
                thermal_stress_coeff = (3*lambda + 2*mu) * alpha_T * dT(i,j,k);

                % Stress components (3D constitutive relations)
                sigma_xx(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_xx - thermal_stress_coeff;
                sigma_yy(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_yy - thermal_stress_coeff;
                sigma_zz(i,j,k) = lambda*epsilon_vol + 2*mu*epsilon_zz - thermal_stress_coeff;

                sigma_xy(i,j,k) = mu * gamma_xy;
                sigma_xz(i,j,k) = mu * gamma_xz;
                sigma_yz(i,j,k) = mu * gamma_yz;
            end
        end
    end

    % Check axis-symmetry
    sigma_xx_max = max(abs(sigma_xx(:)));
    sigma_yy_max = max(abs(sigma_yy(:)));
    if sigma_xx_max > 0 && sigma_yy_max > 0
        asymmetry_percent = abs(sigma_xx_max - sigma_yy_max) / max(sigma_xx_max, sigma_yy_max) * 100;
        if asymmetry_percent < 5
            fprintf('  ✓ Good axis-symmetry (%.1f%% difference)\n', asymmetry_percent);
        elseif asymmetry_percent < 15
            fprintf('  ⚠ Moderate asymmetry (%.1f%% difference)\n', asymmetry_percent);
        else
            fprintf('  ✗ Poor axis-symmetry (%.1f%% difference)\n', asymmetry_percent);
        end
    end
end

function visualize_U_tube_results(fields, grids, dt, nt, soil_props, fluid_props)
% Enhanced visualization for U-tube geothermal pile results

    fprintf('Generating enhanced U-tube visualization...\n');

    % Create comprehensive figure
    figure('Position', [100, 100, 1600, 1200], 'Name', 'U-Tube Geothermal Pile Analysis');

    % Final time step results
    T_final = fields.T(:,:,:,end) - 273;  % Convert to Celsius
    u_x_final = fields.u_x(:,:,:,end) * 1000;  % Convert to mm
    u_y_final = fields.u_y(:,:,:,end) * 1000;
    u_z_final = fields.u_z(:,:,:,end) * 1000;
    sigma_xx_final = fields.sigma_xx(:,:,:,end) / 1000;  % Convert to kPa
    sigma_yy_final = fields.sigma_yy(:,:,:,end) / 1000;
    sigma_zz_final = fields.sigma_zz(:,:,:,end) / 1000;

    % 1. Temperature distribution (3D cross-sections)
    subplot(3,4,1);
    mid_z = round(grids.nz/3);  % Shallow depth
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), T_final(:,:,mid_z), 20, 'LineStyle', 'none');
    colorbar; title('Temperature at Shallow Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');
    hold on;
    % Mark tube positions
    plot(grids.inlet_x, grids.tube_y, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    plot(grids.outlet_x, grids.tube_y, 'bo', 'MarkerSize', 8, 'LineWidth', 2);
    legend('', 'Inlet', 'Outlet', 'Location', 'best');

    subplot(3,4,2);
    mid_z = round(2*grids.nz/3);  % Deep depth
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), T_final(:,:,mid_z), 20, 'LineStyle', 'none');
    colorbar; title('Temperature at Deep Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');
    hold on;
    plot(grids.inlet_x, grids.tube_y, 'ro', 'MarkerSize', 8, 'LineWidth', 2);
    plot(grids.outlet_x, grids.tube_y, 'bo', 'MarkerSize', 8, 'LineWidth', 2);

    % 2. Temperature along U-tube path
    subplot(3,4,3);
    T_inlet = squeeze(T_final(round(grids.inlet_x/grids.dx), round(grids.tube_y/grids.dy), :));
    T_outlet = squeeze(T_final(round(grids.outlet_x/grids.dx), round(grids.tube_y/grids.dy), :));
    plot(grids.z, T_inlet, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet Tube');
    hold on;
    plot(grids.z, T_outlet, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet Tube');
    xlabel('Depth [m]'); ylabel('Temperature [°C]');
    title('Temperature Along U-Tube');
    legend('Location', 'best');
    grid on;

    % 3. Displacement fields
    subplot(3,4,4);
    mid_z = round(grids.nz/2);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), ...
        sqrt(u_x_final(:,:,mid_z).^2 + u_y_final(:,:,mid_z).^2), 15, 'LineStyle', 'none');
    colorbar; title('Total Horizontal Displacement [mm]');
    xlabel('x [m]'); ylabel('y [m]');

    subplot(3,4,5);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), u_z_final(:,:,mid_z), 15, 'LineStyle', 'none');
    colorbar; title('Vertical Displacement [mm]');
    xlabel('x [m]'); ylabel('y [m]');

    % 4. Stress distributions
    subplot(3,4,6);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), sigma_xx_final(:,:,mid_z), 15, 'LineStyle', 'none');
    colorbar; title('σ_{xx} Stress [kPa]');
    xlabel('x [m]'); ylabel('y [m]');

    subplot(3,4,7);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), sigma_yy_final(:,:,mid_z), 15, 'LineStyle', 'none');
    colorbar; title('σ_{yy} Stress [kPa]');
    xlabel('x [m]'); ylabel('y [m]');

    subplot(3,4,8);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), sigma_zz_final(:,:,mid_z), 15, 'LineStyle', 'none');
    colorbar; title('σ_{zz} Stress [kPa]');
    xlabel('x [m]'); ylabel('y [m]');

    % 5. Time evolution plots
    subplot(3,4,9);
    time_hours = (1:nt) * dt / 3600;

    % Temperature evolution at key points
    inlet_idx = [round(grids.inlet_x/grids.dx), round(grids.tube_y/grids.dy), round(grids.nz/2)];
    outlet_idx = [round(grids.outlet_x/grids.dx), round(grids.tube_y/grids.dy), round(grids.nz/2)];

    T_inlet_time = squeeze(fields.T(inlet_idx(1), inlet_idx(2), inlet_idx(3), :)) - 273;
    T_outlet_time = squeeze(fields.T(outlet_idx(1), outlet_idx(2), outlet_idx(3), :)) - 273;

    plot(time_hours, T_inlet_time, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet');
    hold on;
    plot(time_hours, T_outlet_time, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet');
    xlabel('Time [hours]'); ylabel('Temperature [°C]');
    title('Temperature Evolution');
    legend('Location', 'best');
    grid on;

    % 6. Heat transfer efficiency
    subplot(3,4,10);
    heat_extracted = T_inlet_time - T_outlet_time;
    plot(time_hours, heat_extracted, 'g-', 'LineWidth', 2);
    xlabel('Time [hours]'); ylabel('ΔT [°C]');
    title('Heat Extraction (Inlet - Outlet)');
    grid on;

    % 7. Velocity field visualization
    subplot(3,4,11);
    mid_z = round(grids.nz/3);
    u_mag = sqrt(grids.u_vel(:,:,mid_z).^2 + grids.v_vel(:,:,mid_z).^2);
    contourf(grids.X(:,:,mid_z), grids.Y(:,:,mid_z), u_mag, 10, 'LineStyle', 'none');
    colorbar; title('Velocity Magnitude [m/s]');
    xlabel('x [m]'); ylabel('y [m]');

    % Add velocity vectors
    step = 3;
    quiver(grids.X(1:step:end,1:step:end,mid_z), grids.Y(1:step:end,1:step:end,mid_z), ...
           grids.u_vel(1:step:end,1:step:end,mid_z), grids.v_vel(1:step:end,1:step:end,mid_z), ...
           'k', 'AutoScale', 'on', 'AutoScaleFactor', 2);

    % 8. Summary statistics
    subplot(3,4,12);
    axis off;

    % Calculate key performance metrics
    max_temp = max(T_final(:));
    min_temp = min(T_final(:));
    avg_heat_extraction = mean(heat_extracted(end-50:end));  % Average over last period
    max_displacement = max(sqrt(u_x_final(:).^2 + u_y_final(:).^2 + u_z_final(:).^2));
    max_stress = max([abs(sigma_xx_final(:)); abs(sigma_yy_final(:)); abs(sigma_zz_final(:))]);

    % Display summary
    text(0.1, 0.9, 'U-TUBE PERFORMANCE SUMMARY', 'FontSize', 12, 'FontWeight', 'bold');
    text(0.1, 0.8, sprintf('Max Temperature: %.1f°C', max_temp), 'FontSize', 10);
    text(0.1, 0.7, sprintf('Min Temperature: %.1f°C', min_temp), 'FontSize', 10);
    text(0.1, 0.6, sprintf('Avg Heat Extraction: %.1f°C', avg_heat_extraction), 'FontSize', 10);
    text(0.1, 0.5, sprintf('Max Displacement: %.2f mm', max_displacement), 'FontSize', 10);
    text(0.1, 0.4, sprintf('Max Stress: %.1f kPa', max_stress), 'FontSize', 10);
    text(0.1, 0.3, sprintf('Simulation Time: %.1f hours', time_hours(end)), 'FontSize', 10);
    text(0.1, 0.2, sprintf('Inlet Velocity: %.2f m/s', fluid_props.v_inlet), 'FontSize', 10);
    text(0.1, 0.1, sprintf('Reynolds Number: %.0f', fluid_props.Re), 'FontSize', 10);

    sgtitle('Enhanced U-Tube Geothermal Pile Analysis Results', 'FontSize', 14, 'FontWeight', 'bold');

    % Save the figure
    saveas(gcf, 'U_tube_geothermal_analysis.png');
    fprintf('Enhanced U-tube visualization completed and saved!\n');
end
