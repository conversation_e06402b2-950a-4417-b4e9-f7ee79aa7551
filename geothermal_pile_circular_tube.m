function geothermal_pile_circular_tube()
% Simplified Geothermal Pile Analysis with Circular Tube Mesh
% Focuses on proper circular tube representation for thermal-fluid analysis
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-15

clear; clc; close all;

fprintf('=== Geothermal Pile with Circular Tube Analysis ===\n');

%% Problem Geometry and Material Properties
% Domain and tube geometry
domain = struct();
domain.Lx = 4.0;              % Domain width [m]
domain.Ly = 4.0;              % Domain height [m]  
domain.Lz = 25.0;             % Domain depth [m]
domain.tube_radius = 0.08;    % Tube radius [m]
domain.tube_separation = 0.4; % Distance between inlet/outlet [m]
domain.tube_depth = 20.0;     % Active tube depth [m]
domain.u_bend_radius = 0.3;   % U-bend radius [m]

% Material properties
materials = get_material_properties();

% Numerical parameters
params = struct();
params.nx = 40;               % Grid points in x-direction
params.ny = 40;               % Grid points in y-direction
params.nz = 50;               % Grid points in z-direction
params.nt = 500;              % Time steps
params.dt = 180;              % Time step [s] (3 minutes)

% Tube mesh parameters
params.n_circ = 8;            % Circumferential elements
params.n_radial = 2;          % Radial elements
params.n_axial = 100;         % Axial elements

fprintf('Configuration:\n');
fprintf('  Grid: %d x %d x %d\n', params.nx, params.ny, params.nz);
fprintf('  Tube radius: %.3f m\n', domain.tube_radius);
fprintf('  Tube mesh: %d circumferential × %d radial × %d axial\n', ...
    params.n_circ, params.n_radial, params.n_axial);
fprintf('  Time steps: %d (%.1f hours)\n', params.nt, params.nt*params.dt/3600);

%% Generate Circular Tube Mesh
fprintf('Generating circular tube mesh...\n');
[tube_mesh] = generate_circular_tube_mesh(domain, params);

%% Initialize 3D Grid and Fields
fprintf('Initializing 3D grid and fields...\n');
[grid, fields] = initialize_grid_and_fields(domain, params, tube_mesh);

%% Set Initial and Boundary Conditions
fprintf('Setting initial and boundary conditions...\n');
[fields] = set_initial_boundary_conditions(grid, fields, materials, tube_mesh);

%% Main Time-Stepping Loop
fprintf('Starting coupled thermal-fluid analysis...\n');
tic;

for t = 2:params.nt
    % Step 1: Update fluid velocity field
    grid = update_velocity_field(grid, fields.T(:,:,:,t-1), materials, tube_mesh);
    
    % Step 2: Solve thermal problem with convection-conduction
    fields.T(:,:,:,t) = solve_thermal_step(fields.T(:,:,:,t-1), grid, materials, params.dt, tube_mesh);
    
    % Progress indicator
    if mod(t, 50) == 0
        elapsed = toc;
        remaining = elapsed * (params.nt - t) / (t - 1);
        fprintf('Step %d/%d (%.1f%%) - Elapsed: %.1fs, Remaining: %.1fs\n', ...
            t, params.nt, 100*t/params.nt, elapsed, remaining);
        
        % Display key results
        max_T = max(fields.T(:,:,:,t), [], 'all') - 273;
        min_T = min(fields.T(:,:,:,t), [], 'all') - 273;
        fprintf('  Temperature range: %.1f°C to %.1f°C\n', min_T, max_T);
    end
end

total_time = toc;
fprintf('Analysis completed in %.1f seconds!\n', total_time);

%% Post-processing and Visualization
fprintf('Generating visualization...\n');
visualize_results(grid, fields, tube_mesh, params, materials);

% Save results
save('geothermal_pile_circular_tube_results.mat', 'grid', 'fields', 'tube_mesh', 'params', 'materials', '-v7.3');
fprintf('Results saved to geothermal_pile_circular_tube_results.mat\n');

end

function materials = get_material_properties()
% Material properties for thermal-fluid analysis
    
    % Fluid properties (water-glycol mixture)
    materials.fluid = struct();
    materials.fluid.rho = 1030;        % Density [kg/m³]
    materials.fluid.cp = 3900;         % Specific heat [J/kg·K]
    materials.fluid.k = 0.5;           % Thermal conductivity [W/m·K]
    materials.fluid.mu = 0.002;        % Dynamic viscosity [Pa·s]
    materials.fluid.v_inlet = 0.8;     % Inlet velocity [m/s]
    materials.fluid.T_inlet = 318;     % Inlet temperature [K] (45°C)
    materials.fluid.alpha = materials.fluid.k / (materials.fluid.rho * materials.fluid.cp);
    materials.fluid.beta = 3e-4;       % Thermal expansion coefficient [1/K]
    
    % Soil properties
    materials.soil = struct();
    materials.soil.rho = 1800;         % Density [kg/m³]
    materials.soil.cp = 1000;          % Specific heat [J/kg·K]
    materials.soil.k = 2.2;            % Thermal conductivity [W/m·K]
    materials.soil.T_initial = 283;    % Initial temperature [K] (10°C)
    materials.soil.alpha = materials.soil.k / (materials.soil.rho * materials.soil.cp);
    
    % Tube wall properties (HDPE)
    materials.tube_wall = struct();
    materials.tube_wall.rho = 950;     % Density [kg/m³]
    materials.tube_wall.cp = 2300;     % Specific heat [J/kg·K]
    materials.tube_wall.k = 0.4;       % Thermal conductivity [W/m·K]
    materials.tube_wall.thickness = 0.005; % Wall thickness [m]
    
    fprintf('Material properties initialized.\n');
end

function [tube_mesh] = generate_circular_tube_mesh(domain, params)
% Generate detailed circular mesh for U-shaped tube
    
    % Tube geometry parameters
    R_inner = domain.tube_radius - 0.005;  % Inner radius (fluid)
    R_outer = domain.tube_radius;          % Outer radius (tube wall)
    
    % Inlet tube center
    inlet_x = domain.Lx/2 - domain.tube_separation/2;
    inlet_y = domain.Ly/2;
    
    % Outlet tube center  
    outlet_x = domain.Lx/2 + domain.tube_separation/2;
    outlet_y = domain.Ly/2;
    
    % Mesh parameters
    n_circ = params.n_circ;
    n_radial = params.n_radial;
    n_axial = params.n_axial;
    
    % Generate circular cross-section coordinates
    [r, theta] = meshgrid(linspace(0, R_outer, n_radial+1), linspace(0, 2*pi, n_circ+1));
    r = r(1:end-1, :);  % Remove duplicate points
    theta = theta(1:end-1, :);
    
    % Generate tube coordinates
    tube_mesh = struct();
    
    % Inlet tube
    tube_mesh.inlet = struct();
    tube_mesh.inlet.x = zeros(n_circ, n_radial+1, n_axial+1);
    tube_mesh.inlet.y = zeros(n_circ, n_radial+1, n_axial+1);
    tube_mesh.inlet.z = zeros(n_circ, n_radial+1, n_axial+1);
    
    for k = 1:n_axial+1
        z = (k-1) * domain.tube_depth / n_axial;
        for j = 1:n_circ
            for i = 1:n_radial+1
                tube_mesh.inlet.x(j,i,k) = inlet_x + r(j,i) * cos(theta(j,i));
                tube_mesh.inlet.y(j,i,k) = inlet_y + r(j,i) * sin(theta(j,i));
                tube_mesh.inlet.z(j,i,k) = z;
            end
        end
    end
    
    % Outlet tube
    tube_mesh.outlet = struct();
    tube_mesh.outlet.x = zeros(n_circ, n_radial+1, n_axial+1);
    tube_mesh.outlet.y = zeros(n_circ, n_radial+1, n_axial+1);
    tube_mesh.outlet.z = zeros(n_circ, n_radial+1, n_axial+1);
    
    for k = 1:n_axial+1
        z = (k-1) * domain.tube_depth / n_axial;
        for j = 1:n_circ
            for i = 1:n_radial+1
                tube_mesh.outlet.x(j,i,k) = outlet_x + r(j,i) * cos(theta(j,i));
                tube_mesh.outlet.y(j,i,k) = outlet_y + r(j,i) * sin(theta(j,i));
                tube_mesh.outlet.z(j,i,k) = z;
            end
        end
    end
    
    % U-bend
    tube_mesh.ubend = struct();
    n_bend = 10;  % Number of segments in U-bend
    tube_mesh.ubend.x = zeros(n_circ, n_radial+1, n_bend+1);
    tube_mesh.ubend.y = zeros(n_circ, n_radial+1, n_bend+1);
    tube_mesh.ubend.z = zeros(n_circ, n_radial+1, n_bend+1);
    
    bend_center_x = (inlet_x + outlet_x) / 2;
    bend_center_z = domain.tube_depth;
    bend_radius = domain.u_bend_radius;
    
    for b = 1:n_bend+1
        angle = pi * (b-1) / n_bend;  % 0 to π
        path_x = bend_center_x - bend_radius * cos(angle);
        path_z = bend_center_z + bend_radius * sin(angle);
        
        for j = 1:n_circ
            for i = 1:n_radial+1
                % Local coordinate system for tube cross-section
                local_x = r(j,i) * cos(theta(j,i));
                local_y = r(j,i) * sin(theta(j,i));
                
                % Transform to global coordinates
                tube_mesh.ubend.x(j,i,b) = path_x + local_x * cos(angle);
                tube_mesh.ubend.y(j,i,b) = inlet_y + local_y;
                tube_mesh.ubend.z(j,i,b) = path_z - local_x * sin(angle);
            end
        end
    end
    
    % Create masks for fluid and wall regions
    tube_mesh.fluid_mask = r <= R_inner;
    tube_mesh.wall_mask = r > R_inner & r <= R_outer;
    
    fprintf('  Circular tube mesh generated.\n');
    fprintf('  Inlet tube: %d points\n', numel(tube_mesh.inlet.x));
    fprintf('  Outlet tube: %d points\n', numel(tube_mesh.outlet.x));
    fprintf('  U-bend: %d points\n', numel(tube_mesh.ubend.x));
end

function [grid, fields] = initialize_grid_and_fields(domain, params, tube_mesh)
% Initialize 3D computational grid and solution fields

    % 3D Cartesian grid
    grid = struct();
    grid.x = linspace(0, domain.Lx, params.nx);
    grid.y = linspace(0, domain.Ly, params.ny);
    grid.z = linspace(0, domain.Lz, params.nz);
    grid.dx = domain.Lx / (params.nx - 1);
    grid.dy = domain.Ly / (params.ny - 1);
    grid.dz = domain.Lz / (params.nz - 1);

    % Create 3D meshgrids
    [grid.X, grid.Y, grid.Z] = meshgrid(grid.x, grid.y, grid.z);

    % Create tube masks on Cartesian grid
    grid.tube_mask = false(params.nx, params.ny, params.nz);
    grid.inlet_mask = false(params.nx, params.ny, params.nz);
    grid.outlet_mask = false(params.nx, params.ny, params.nz);

    % Map circular tube mesh to Cartesian grid
    inlet_x = domain.Lx/2 - domain.tube_separation/2;
    outlet_x = domain.Lx/2 + domain.tube_separation/2;
    tube_y = domain.Ly/2;

    for i = 1:params.nx
        for j = 1:params.ny
            for k = 1:params.nz
                x = grid.x(i);
                y = grid.y(j);
                z = grid.z(k);

                % Check if point is inside inlet tube
                dist_inlet = sqrt((x - inlet_x)^2 + (y - tube_y)^2);
                if dist_inlet <= domain.tube_radius && z <= domain.tube_depth
                    grid.tube_mask(i,j,k) = true;
                    grid.inlet_mask(i,j,k) = true;
                end

                % Check if point is inside outlet tube
                dist_outlet = sqrt((x - outlet_x)^2 + (y - tube_y)^2);
                if dist_outlet <= domain.tube_radius && z <= domain.tube_depth
                    grid.tube_mask(i,j,k) = true;
                    grid.outlet_mask(i,j,k) = true;
                end

                % Check if point is in U-bend
                if z >= domain.tube_depth - domain.u_bend_radius && z <= domain.tube_depth + domain.u_bend_radius
                    bend_center_x = (inlet_x + outlet_x) / 2;
                    bend_center_z = domain.tube_depth;

                    dist_bend = sqrt((x - bend_center_x)^2 + (z - bend_center_z)^2);
                    if abs(y - tube_y) <= domain.tube_radius && ...
                       dist_bend <= domain.u_bend_radius + domain.tube_radius && ...
                       dist_bend >= domain.u_bend_radius - domain.tube_radius
                        grid.tube_mask(i,j,k) = true;
                    end
                end
            end
        end
    end

    % Initialize velocity field
    grid.u_vel = zeros(params.nx, params.ny, params.nz);
    grid.v_vel = zeros(params.nx, params.ny, params.nz);
    grid.w_vel = zeros(params.nx, params.ny, params.nz);

    % Initialize solution fields
    fields = struct();
    fields.T = zeros(params.nx, params.ny, params.nz, params.nt);

    fprintf('  Grid and fields initialized.\n');
    fprintf('  Tube volume: %.3f m³\n', sum(grid.tube_mask(:)) * grid.dx * grid.dy * grid.dz);
end

function [fields] = set_initial_boundary_conditions(grid, fields, materials, tube_mesh)
% Set initial and boundary conditions

    % Initial temperature field with geothermal gradient
    for i = 1:size(fields.T, 1)
        for j = 1:size(fields.T, 2)
            for k = 1:size(fields.T, 3)
                depth = grid.z(k);
                T_geothermal = materials.soil.T_initial + 0.025 * depth; % 25°C/km gradient
                fields.T(i,j,k,1) = T_geothermal;
            end
        end
    end

    % Set inlet temperature
    fields.T(grid.inlet_mask) = materials.fluid.T_inlet;

    fprintf('  Initial and boundary conditions set.\n');
end

function grid = update_velocity_field(grid, T, materials, tube_mesh)
% Update velocity field based on temperature and tube geometry

    % Initialize velocity field
    grid.u_vel(:) = 0;
    grid.v_vel(:) = 0;
    grid.w_vel(:) = 0;

    % Set velocity in tubes based on circular geometry
    for i = 1:size(grid.X, 1)
        for j = 1:size(grid.X, 2)
            for k = 1:size(grid.X, 3)
                if grid.inlet_mask(i,j,k)
                    % Downward flow in inlet tube
                    grid.w_vel(i,j,k) = -materials.fluid.v_inlet;
                elseif grid.outlet_mask(i,j,k)
                    % Upward flow in outlet tube
                    grid.w_vel(i,j,k) = materials.fluid.v_inlet * 0.8; % Reduced due to heat loss
                end
            end
        end
    end
end

function T_new = solve_thermal_step(T_old, grid, materials, dt, tube_mesh)
% Solve thermal problem with convection-conduction coupling

    T_new = T_old;
    dx = grid.dx; dy = grid.dy; dz = grid.dz;

    % Stability check
    alpha_max = max(materials.fluid.alpha, materials.soil.alpha);
    dt_max = 0.4 * min([dx^2, dy^2, dz^2]) / (6 * alpha_max);
    if dt > dt_max
        fprintf('Warning: Time step %.1fs exceeds stability limit %.1fs\n', dt, dt_max);
    end

    % Main thermal loop
    for i = 2:size(T_old,1)-1
        for j = 2:size(T_old,2)-1
            for k = 2:size(T_old,3)-1

                % Determine local properties
                if grid.tube_mask(i,j,k)
                    % Inside tube: use fluid properties
                    alpha = materials.fluid.alpha;
                    u = grid.u_vel(i,j,k);
                    v = grid.v_vel(i,j,k);
                    w = grid.w_vel(i,j,k);

                    % Apply inlet boundary condition
                    if grid.inlet_mask(i,j,k)
                        T_new(i,j,k) = materials.fluid.T_inlet;
                        continue;
                    end
                else
                    % In soil: use soil properties, no convection
                    alpha = materials.soil.alpha;
                    u = 0; v = 0; w = 0;
                end

                % Temperature gradients
                dT_dx = (T_old(i+1,j,k) - T_old(i-1,j,k)) / (2*dx);
                dT_dy = (T_old(i,j+1,k) - T_old(i,j-1,k)) / (2*dy);
                dT_dz = (T_old(i,j,k+1) - T_old(i,j,k-1)) / (2*dz);

                % Second derivatives (Laplacian)
                d2T_dx2 = (T_old(i+1,j,k) - 2*T_old(i,j,k) + T_old(i-1,j,k)) / dx^2;
                d2T_dy2 = (T_old(i,j+1,k) - 2*T_old(i,j,k) + T_old(i,j-1,k)) / dy^2;
                d2T_dz2 = (T_old(i,j,k+1) - 2*T_old(i,j,k) + T_old(i,j,k-1)) / dz^2;

                % Convection term: u·∇T
                convection = u * dT_dx + v * dT_dy + w * dT_dz;

                % Conduction term: α∇²T
                conduction = alpha * (d2T_dx2 + d2T_dy2 + d2T_dz2);

                % Heat exchange between tube and soil
                heat_exchange = 0;
                if grid.tube_mask(i,j,k)
                    h_exchange = 500;  % Heat transfer coefficient [W/m²·K]

                    % Average soil temperature around tube
                    T_soil_avg = 0;
                    count = 0;
                    for di = -1:1
                        for dj = -1:1
                            for dk = -1:1
                                ii = i + di; jj = j + dj; kk = k + dk;
                                if ii >= 1 && ii <= size(T_old,1) && jj >= 1 && jj <= size(T_old,2) && ...
                                   kk >= 1 && kk <= size(T_old,3) && ~grid.tube_mask(ii,jj,kk)
                                    T_soil_avg = T_soil_avg + T_old(ii,jj,kk);
                                    count = count + 1;
                                end
                            end
                        end
                    end

                    if count > 0
                        T_soil_avg = T_soil_avg / count;
                        surface_area_ratio = 4 * 0.08 / (dx * dy * dz);
                        heat_exchange = h_exchange * surface_area_ratio * (T_soil_avg - T_old(i,j,k)) / ...
                                      (materials.fluid.rho * materials.fluid.cp);
                    end
                end

                % Time integration
                dT_dt = -convection + conduction + heat_exchange;
                T_new(i,j,k) = T_old(i,j,k) + dt * dT_dt;
            end
        end
    end

    % Apply boundary conditions
    T_new = apply_boundary_conditions(T_new, grid, materials);
end

function T = apply_boundary_conditions(T, grid, materials)
% Apply thermal boundary conditions

    % Surface boundary (constant temperature)
    T(:, :, 1) = materials.soil.T_initial;

    % Deep boundary (geothermal gradient)
    deep_temp = materials.soil.T_initial + 0.025 * max(grid.z);
    T(:, :, end) = deep_temp;

    % Side boundaries (no-flux)
    T(1, :, :) = T(2, :, :);
    T(end, :, :) = T(end-1, :, :);
    T(:, 1, :) = T(:, 2, :);
    T(:, end, :) = T(:, end-1, :);

    % Inlet boundary condition
    T(grid.inlet_mask) = materials.fluid.T_inlet;
end

function visualize_results(grid, fields, tube_mesh, params, materials)
% Comprehensive visualization of circular tube results

    fprintf('  Generating comprehensive visualization...\n');

    figure('Position', [100, 100, 1600, 1200], 'Name', 'Circular Tube Geothermal Pile Analysis');

    % Final time step results
    T_final = fields.T(:,:,:,end) - 273;  % Convert to Celsius
    time_hours = (1:params.nt) * params.dt / 3600;

    % 1. 3D Temperature distribution
    subplot(3,4,1);
    [X_tube, Y_tube, Z_tube] = meshgrid(grid.x, grid.y, grid.z);
    tube_indices = find(grid.tube_mask);
    scatter3(X_tube(tube_indices), Y_tube(tube_indices), Z_tube(tube_indices), ...
        20, T_final(tube_indices), 'filled');
    colorbar; title('3D Tube Temperature (°C)');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 2. Circular tube cross-section
    subplot(3,4,2);
    % Show circular mesh structure
    inlet_x_coords = tube_mesh.inlet.x(:,:,1);
    inlet_y_coords = tube_mesh.inlet.y(:,:,1);
    plot(inlet_x_coords(:), inlet_y_coords(:), 'ro', 'MarkerSize', 4);
    hold on;
    outlet_x_coords = tube_mesh.outlet.x(:,:,1);
    outlet_y_coords = tube_mesh.outlet.y(:,:,1);
    plot(outlet_x_coords(:), outlet_y_coords(:), 'bo', 'MarkerSize', 4);
    title('Circular Tube Cross-Section');
    xlabel('x [m]'); ylabel('y [m]');
    legend('Inlet', 'Outlet', 'Location', 'best');
    axis equal;

    % 3. Temperature at mid-depth
    subplot(3,4,3);
    mid_z = round(params.nz/2);
    contourf(grid.X(:,:,mid_z), grid.Y(:,:,mid_z), T_final(:,:,mid_z), 20, 'LineStyle', 'none');
    colorbar; title('Temperature at Mid-Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');

    % Mark tube positions
    hold on;
    inlet_x = grid.x(1) + (grid.x(end) - grid.x(1))/2 - 0.2;
    outlet_x = grid.x(1) + (grid.x(end) - grid.x(1))/2 + 0.2;
    tube_y = grid.y(1) + (grid.y(end) - grid.y(1))/2;

    theta = linspace(0, 2*pi, 50);
    inlet_circle_x = inlet_x + 0.08 * cos(theta);
    inlet_circle_y = tube_y + 0.08 * sin(theta);
    outlet_circle_x = outlet_x + 0.08 * cos(theta);
    outlet_circle_y = tube_y + 0.08 * sin(theta);

    plot(inlet_circle_x, inlet_circle_y, 'r-', 'LineWidth', 2);
    plot(outlet_circle_x, outlet_circle_y, 'b-', 'LineWidth', 2);

    % 4. Temperature evolution
    subplot(3,4,4);
    % Sample points for time evolution
    if any(grid.inlet_mask(:)) && any(grid.outlet_mask(:))
        inlet_sample = find(grid.inlet_mask, 1);
        outlet_sample = find(grid.outlet_mask, 1);
        [i_in, j_in, k_in] = ind2sub(size(grid.inlet_mask), inlet_sample);
        [i_out, j_out, k_out] = ind2sub(size(grid.outlet_mask), outlet_sample);

        T_inlet_time = squeeze(fields.T(i_in, j_in, k_in, :)) - 273;
        T_outlet_time = squeeze(fields.T(i_out, j_out, k_out, :)) - 273;

        plot(time_hours, T_inlet_time, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet');
        hold on;
        plot(time_hours, T_outlet_time, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet');
        xlabel('Time [hours]'); ylabel('Temperature [°C]');
        title('Temperature Evolution');
        legend('Location', 'best');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    else
        text(0.5, 0.5, 'No tube data available', 'HorizontalAlignment', 'center');
        title('Temperature Evolution');
    end

    % 5. Heat extraction efficiency
    subplot(3,4,5);
    if exist('T_inlet_time', 'var') && exist('T_outlet_time', 'var')
        heat_extraction = T_inlet_time - T_outlet_time;
        plot(time_hours, heat_extraction, 'g-', 'LineWidth', 2);
        xlabel('Time [hours]'); ylabel('ΔT [°C]');
        title('Heat Extraction (Inlet - Outlet)');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    else
        text(0.5, 0.5, 'No tube data available', 'HorizontalAlignment', 'center');
        title('Heat Extraction (Inlet - Outlet)');
    end

    % 6. Velocity field in tubes
    subplot(3,4,6);
    mid_z = round(params.nz/3);
    u_mag = sqrt(grid.u_vel(:,:,mid_z).^2 + grid.v_vel(:,:,mid_z).^2 + grid.w_vel(:,:,mid_z).^2);
    contourf(grid.X(:,:,mid_z), grid.Y(:,:,mid_z), u_mag, 10, 'LineStyle', 'none');
    colorbar; title('Velocity Magnitude [m/s]');
    xlabel('x [m]'); ylabel('y [m]');

    % 7. Temperature along tube depth
    subplot(3,4,7);
    if exist('i_in', 'var') && exist('i_out', 'var')
        T_inlet_depth = squeeze(fields.T(i_in, j_in, :, end)) - 273;
        T_outlet_depth = squeeze(fields.T(i_out, j_out, :, end)) - 273;

        plot(grid.z, T_inlet_depth, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet Tube');
        hold on;
        plot(grid.z, T_outlet_depth, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet Tube');
        xlabel('Depth [m]'); ylabel('Temperature [°C]');
        title('Temperature Along Tube Depth');
        legend('Location', 'best');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    else
        text(0.5, 0.5, 'No tube data available', 'HorizontalAlignment', 'center');
        title('Temperature Along Tube Depth');
    end

    % 8. Circular mesh visualization
    subplot(3,4,8);
    % Show 3D circular mesh structure
    inlet_mesh_x = tube_mesh.inlet.x(:,:,1:10:end);
    inlet_mesh_y = tube_mesh.inlet.y(:,:,1:10:end);
    inlet_mesh_z = tube_mesh.inlet.z(:,:,1:10:end);

    plot3(inlet_mesh_x(:), inlet_mesh_y(:), inlet_mesh_z(:), 'r.', 'MarkerSize', 3);
    hold on;

    outlet_mesh_x = tube_mesh.outlet.x(:,:,1:10:end);
    outlet_mesh_y = tube_mesh.outlet.y(:,:,1:10:end);
    outlet_mesh_z = tube_mesh.outlet.z(:,:,1:10:end);

    plot3(outlet_mesh_x(:), outlet_mesh_y(:), outlet_mesh_z(:), 'b.', 'MarkerSize', 3);

    title('Circular Tube Mesh Structure');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 9. U-bend visualization
    subplot(3,4,9);
    ubend_mesh_x = tube_mesh.ubend.x(:,:,:);
    ubend_mesh_y = tube_mesh.ubend.y(:,:,:);
    ubend_mesh_z = tube_mesh.ubend.z(:,:,:);

    plot3(ubend_mesh_x(:), ubend_mesh_y(:), ubend_mesh_z(:), 'g.', 'MarkerSize', 4);
    title('U-Bend Mesh Structure');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 10. Performance summary
    subplot(3,4,10);
    axis off;

    % Calculate performance metrics
    max_temp = max(T_final(:));
    min_temp = min(T_final(:));
    tube_volume = sum(grid.tube_mask(:)) * grid.dx * grid.dy * grid.dz;

    if exist('heat_extraction', 'var')
        avg_heat_extraction = mean(heat_extraction(max(1, end-50):end));
    else
        avg_heat_extraction = 0;
    end

    % Display summary
    text(0.1, 0.9, 'CIRCULAR TUBE PERFORMANCE', 'FontSize', 12, 'FontWeight', 'bold');
    text(0.1, 0.8, sprintf('Grid: %d × %d × %d', params.nx, params.ny, params.nz), 'FontSize', 10);
    text(0.1, 0.7, sprintf('Tube Volume: %.3f m³', tube_volume), 'FontSize', 10);
    text(0.1, 0.6, sprintf('Max Temperature: %.1f°C', max_temp), 'FontSize', 10);
    text(0.1, 0.5, sprintf('Min Temperature: %.1f°C', min_temp), 'FontSize', 10);
    text(0.1, 0.4, sprintf('Avg Heat Extraction: %.1f°C', avg_heat_extraction), 'FontSize', 10);
    text(0.1, 0.3, sprintf('Simulation Time: %.1f hours', time_hours(end)), 'FontSize', 10);
    text(0.1, 0.2, sprintf('Inlet Velocity: %.2f m/s', materials.fluid.v_inlet), 'FontSize', 10);
    text(0.1, 0.1, sprintf('Tube Radius: %.3f m', 0.08), 'FontSize', 10);

    % 11. Fluid vs wall regions
    subplot(3,4,11);
    fluid_points = sum(tube_mesh.fluid_mask(:));
    wall_points = sum(tube_mesh.wall_mask(:));
    pie([fluid_points, wall_points], {'Fluid Region', 'Wall Region'});
    title('Tube Cross-Section Distribution');

    % 12. Temperature contours
    subplot(3,4,12);
    deep_z = round(2*params.nz/3);
    contour(grid.X(:,:,deep_z), grid.Y(:,:,deep_z), T_final(:,:,deep_z), 15);
    colorbar; title('Temperature Contours at Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');

    sgtitle('Circular Tube Geothermal Pile Analysis Results', 'FontSize', 14, 'FontWeight', 'bold');

    % Save visualization
    saveas(gcf, 'circular_tube_geothermal_analysis.png');
    fprintf('  Visualization completed and saved!\n');
end
