function geothermal_pile_coupled_analysis()
% Coupled Thermo-Mechanical Analysis of Geothermal Pile
% Solves thermal fluid flow in tube + heat conduction and thermal expansion in soil
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-03

clear; clc; close all;

fprintf('=== Geothermal Pile Coupled Thermo-Mechanical Analysis ===\n');

%% Problem Geometry
% Tube geometry
R_tube_inner = 0.05;    % Tube inner radius [m]
R_tube_outer = 0.055;   % Tube outer radius [m]
tube_thickness = R_tube_outer - R_tube_inner; % Wall thickness [m]
L_tube = 20.0;          % Tube length [m]

% Soil domain geometry
R_soil = 2.0;           % Soil domain radius [m]
L_soil = 25.0;          % Soil domain length [m]

fprintf('Tube geometry: Inner R=%.3fm, Outer R=%.3fm, Thickness=%.3fm\n', ...
    R_tube_inner, R_tube_outer, tube_thickness);

%% Material Properties
% Fluid properties (water/glycol mixture)
fluid_props = get_fluid_properties();

% Tube material properties (HDPE)
tube_props = get_tube_properties();

% Soil properties (clay/sand)
soil_props = get_soil_properties();

%% Numerical Parameters
% Grid parameters
nr_fluid = 15;          % Radial nodes in fluid
nz_fluid = 40;          % Axial nodes in fluid
nr_tube = 5;            % Radial nodes in tube wall
nz_tube = 40;           % Axial nodes in tube (same as fluid)
nr_soil = 30;           % Radial nodes in soil
nz_soil = 50;           % Axial nodes in soil
nt = 2000;              % Time steps
dt = 60;                % Time step [s] (1 minute)

fprintf('Grid: Fluid (%dx%d), Tube (%dx%d), Soil (%dx%d), Time steps: %d\n', ...
    nz_fluid, nr_fluid, nz_tube, nr_tube, nz_soil, nr_soil, nt);
fprintf('Total simulation time: %.1f hours\n', nt*dt/3600);

%% Initialize grids and fields
[grids, fields] = initialize_problem(R_tube_inner, R_tube_outer, R_soil, L_tube, L_soil, ...
    nr_fluid, nz_fluid, nr_tube, nz_tube, nr_soil, nz_soil, nt, fluid_props, tube_props, soil_props);

%% Boundary and initial conditions
[fields] = set_initial_boundary_conditions(fields, grids, fluid_props, soil_props);

%% Main time-stepping loop
fprintf('Starting coupled analysis...\n');
tic;

for t = 2:nt
    % Step 1: Solve thermal problem in fluid domain
    fields.T_fluid(:,:,t) = solve_fluid_thermal(fields.T_fluid(:,:,t-1), ...
        grids, fluid_props, dt);

    % Step 2: Solve thermal problem in tube wall
    fields.T_tube(:,:,t) = solve_tube_thermal(fields.T_tube(:,:,t-1), ...
        fields.T_fluid(:,:,t), fields.T_soil(:,:,t-1), grids, tube_props, dt);

    % Step 3: Solve thermal problem in soil domain
    fields.T_soil(:,:,t) = solve_soil_thermal(fields.T_soil(:,:,t-1), ...
        fields.T_tube(:,:,t), grids, soil_props, dt);

    % Step 4: Apply interface coupling conditions
    fields = apply_interface_coupling(fields, grids, t, fluid_props, tube_props, soil_props);
    
    % Step 5: Solve mechanical problem in tube and soil
    [fields.u_r_tube(:,:,t), fields.u_z_tube(:,:,t), fields.sigma_rr_tube(:,:,t), ...
     fields.sigma_zz_tube(:,:,t), fields.sigma_rz_tube(:,:,t)] = ...
        solve_tube_mechanics(fields.T_tube(:,:,t), fields.T_tube(:,:,1), ...
        grids, tube_props);

    [fields.u_r(:,:,t), fields.u_z(:,:,t), fields.sigma_rr(:,:,t), ...
     fields.sigma_zz(:,:,t), fields.sigma_rz(:,:,t)] = ...
        solve_soil_mechanics(fields.T_soil(:,:,t), fields.T_soil(:,:,1), ...
        grids, soil_props);
    
    % Progress indicator
    if mod(t, 200) == 0
        elapsed = toc;
        remaining = elapsed * (nt - t) / (t - 1);
        fprintf('Step %d/%d (%.1f%%) - Elapsed: %.1fs, Remaining: %.1fs\n', ...
            t, nt, 100*t/nt, elapsed, remaining);
    end
end

total_time = toc;
fprintf('Analysis completed in %.1f seconds!\n', total_time);

%% Post-processing and visualization
visualize_coupled_results(fields, grids, dt, nt, soil_props);

% Save results
save('geothermal_pile_results.mat', 'fields', 'grids', 'fluid_props', ...
    'soil_props', 'dt', 'nt', '-v7.3');

fprintf('Results saved to geothermal_pile_results.mat\n');

end

function fluid_props = get_fluid_properties()
% Fluid properties (water-glycol mixture for geothermal applications)
    fluid_props.rho = 1030;        % Density [kg/m³]
    fluid_props.cp = 3900;         % Specific heat [J/kg·K]
    fluid_props.k = 0.5;           % Thermal conductivity [W/m·K]
    fluid_props.mu = 0.002;        % Dynamic viscosity [Pa·s]
    fluid_props.v_inlet = 0.5;     % Inlet velocity [m/s]
    fluid_props.T_inlet = 313;     % Inlet temperature [K] (40°C)
    fluid_props.alpha = fluid_props.k / (fluid_props.rho * fluid_props.cp);
end

function tube_props = get_tube_properties()
% Tube material properties (HDPE)
    tube_props.rho = 950;          % Density [kg/m³]
    tube_props.cp = 2300;          % Specific heat [J/kg·K]
    tube_props.k = 0.4;            % Thermal conductivity [W/m·K]
    tube_props.E = 1e9;            % Young's modulus [Pa]
    tube_props.nu = 0.4;           % Poisson's ratio
    tube_props.alpha_T = 2e-4;     % Thermal expansion coefficient [1/K]
end

function soil_props = get_soil_properties()
% Soil properties (typical clay/sand mixture)
    soil_props.rho = 1800;         % Density [kg/m³]
    soil_props.cp = 1000;          % Specific heat [J/kg·K]
    soil_props.k = 2.0;            % Thermal conductivity [W/m·K]
    soil_props.E = 50e6;           % Young's modulus [Pa]
    soil_props.nu = 0.3;           % Poisson's ratio
    soil_props.alpha_T = 1e-5;     % Thermal expansion coefficient [1/K]
    soil_props.T_initial = 283;    % Initial soil temperature [K] (10°C)
    soil_props.T_far_field = 283;  % Far-field temperature [K]
    soil_props.alpha = soil_props.k / (soil_props.rho * soil_props.cp);
end

function [grids, fields] = initialize_problem(R_tube_inner, R_tube_outer, R_soil, ...
    L_tube, L_soil, nr_fluid, nz_fluid, nr_tube, nz_tube, nr_soil, nz_soil, nt, fluid_props, tube_props, soil_props)
% Initialize computational grids and solution fields for three domains

    % Fluid domain grid (0 to R_tube_inner)
    grids.r_fluid = linspace(0, R_tube_inner, nr_fluid);
    grids.z_fluid = linspace(0, L_tube, nz_fluid);
    grids.dr_fluid = R_tube_inner / (nr_fluid - 1);
    grids.dz_fluid = L_tube / (nz_fluid - 1);
    [grids.Z_fluid, grids.R_fluid] = meshgrid(grids.z_fluid, grids.r_fluid);

    % Tube wall domain grid (R_tube_inner to R_tube_outer)
    grids.r_tube = linspace(R_tube_inner, R_tube_outer, nr_tube);
    grids.z_tube = linspace(0, L_tube, nz_tube);
    grids.dr_tube = (R_tube_outer - R_tube_inner) / (nr_tube - 1);
    grids.dz_tube = L_tube / (nz_tube - 1);
    [grids.Z_tube, grids.R_tube] = meshgrid(grids.z_tube, grids.r_tube);

    % Soil domain grid (starts from tube outer radius)
    grids.r_soil = linspace(R_tube_outer, R_soil, nr_soil);
    grids.z_soil = linspace(0, L_soil, nz_soil);
    grids.dr_soil = (R_soil - R_tube_outer) / (nr_soil - 1);
    grids.dz_soil = L_soil / (nz_soil - 1);
    [grids.Z_soil, grids.R_soil] = meshgrid(grids.z_soil, grids.r_soil);
    
    % Velocity field in fluid (parabolic profile)
    grids.u_fluid = zeros(nz_fluid, nr_fluid);
    for i = 1:nr_fluid
        grids.u_fluid(:, i) = 2 * fluid_props.v_inlet * ...
            (1 - (grids.r_fluid(i)/R_tube)^2);
    end
    
    % Initialize solution fields
    fields.T_fluid = fluid_props.T_inlet * ones(nz_fluid, nr_fluid, nt);
    fields.T_tube = soil_props.T_initial * ones(nz_tube, nr_tube, nt);  % Start at soil temperature
    fields.T_soil = soil_props.T_initial * ones(nz_soil, nr_soil, nt);

    % Mechanical fields in tube
    fields.u_r_tube = zeros(nz_tube, nr_tube, nt);      % Tube radial displacement
    fields.u_z_tube = zeros(nz_tube, nr_tube, nt);      % Tube axial displacement
    fields.sigma_rr_tube = zeros(nz_tube, nr_tube, nt); % Tube radial stress
    fields.sigma_zz_tube = zeros(nz_tube, nr_tube, nt); % Tube axial stress
    fields.sigma_rz_tube = zeros(nz_tube, nr_tube, nt); % Tube shear stress

    % Mechanical fields in soil
    fields.u_r = zeros(nz_soil, nr_soil, nt);      % Radial displacement
    fields.u_z = zeros(nz_soil, nr_soil, nt);      % Axial displacement
    fields.sigma_rr = zeros(nz_soil, nr_soil, nt); % Radial stress
    fields.sigma_zz = zeros(nz_soil, nr_soil, nt); % Axial stress
    fields.sigma_rz = zeros(nz_soil, nr_soil, nt); % Shear stress
    
    % Store dimensions
    grids.nr_fluid = nr_fluid; grids.nz_fluid = nz_fluid;
    grids.nr_tube = nr_tube; grids.nz_tube = nz_tube;
    grids.nr_soil = nr_soil; grids.nz_soil = nz_soil;
    grids.R_tube_inner = R_tube_inner; grids.R_tube_outer = R_tube_outer;
    grids.R_soil = R_soil; grids.L_tube = L_tube; grids.L_soil = L_soil;
end

function fields = set_initial_boundary_conditions(fields, grids, fluid_props, soil_props)
% Set initial and boundary conditions
    
    % Initial conditions already set in initialize_problem
    
    % Fluid inlet boundary condition
    fields.T_fluid(1, :, :) = fluid_props.T_inlet;
    
    % Soil far-field boundary conditions
    fields.T_soil(:, end, :) = soil_props.T_far_field;  % Outer boundary
    fields.T_soil(1, :, :) = soil_props.T_far_field;    % Top boundary
    fields.T_soil(end, :, :) = soil_props.T_far_field;  % Bottom boundary
    
    fprintf('Initial and boundary conditions set.\n');
end

function T_new = solve_fluid_thermal(T_old, grids, fluid_props, dt)
% Solve thermal equation in fluid domain (same as original code)
    
    T_new = T_old;
    alpha = fluid_props.alpha;
    dr = grids.dr_fluid;
    dz = grids.dz_fluid;
    
    % Interior nodes
    for i = 2:grids.nz_fluid-1
        for j = 2:grids.nr_fluid-1
            % Finite difference operators
            d2T_dr2 = (T_old(i, j+1) - 2*T_old(i, j) + T_old(i, j-1)) / dr^2;
            dT_dr = (T_old(i, j+1) - T_old(i, j-1)) / (2*dr);
            d2T_dz2 = (T_old(i+1, j) - 2*T_old(i, j) + T_old(i-1, j)) / dz^2;
            dT_dz = (T_old(i+1, j) - T_old(i-1, j)) / (2*dz);
            
            % Heat equation with convection
            r = grids.r_fluid(j);
            if r > 1e-10  % Avoid division by zero at centerline
                dT_dt = alpha * (d2T_dr2 + (1/r) * dT_dr + d2T_dz2) - ...
                    grids.u_fluid(i, j) * dT_dz;
            else
                dT_dt = alpha * (2*d2T_dr2 + d2T_dz2) - grids.u_fluid(i, j) * dT_dz;
            end
            
            T_new(i, j) = T_old(i, j) + dt * dT_dt;
        end
    end
    
    % Boundary conditions
    T_new(1, :) = fluid_props.T_inlet;           % Inlet
    T_new(grids.nz_fluid, :) = T_new(grids.nz_fluid-1, :); % Outlet
    T_new(:, 1) = T_new(:, 2);                   % Centerline symmetry
    % Wall boundary will be handled by interface coupling
end

function T_new = solve_soil_thermal(T_old, T_fluid, grids, soil_props, dt)
% Solve thermal equation in soil domain (pure conduction)

    T_new = T_old;
    alpha = soil_props.alpha;
    dr = grids.dr_soil;
    dz = grids.dz_soil;

    % Interior nodes
    for i = 2:grids.nz_soil-1
        for j = 2:grids.nr_soil-1
            % Finite difference operators
            d2T_dr2 = (T_old(i, j+1) - 2*T_old(i, j) + T_old(i, j-1)) / dr^2;
            dT_dr = (T_old(i, j+1) - T_old(i, j-1)) / (2*dr);
            d2T_dz2 = (T_old(i+1, j) - 2*T_old(i, j) + T_old(i-1, j)) / dz^2;

            % Heat conduction equation in cylindrical coordinates
            r = grids.r_soil(j);
            dT_dt = alpha * (d2T_dr2 + (1/r) * dT_dr + d2T_dz2);

            T_new(i, j) = T_old(i, j) + dt * dT_dt;
        end
    end

    % Boundary conditions (far-field temperatures already set)
    % Inner boundary will be handled by interface coupling
end

function fields = apply_interface_coupling(fields, grids, t, fluid_props, soil_props)
% Apply thermal continuity at tube-soil interface

    % Temperature continuity: T_fluid(R_tube) = T_soil(R_tube_outer)
    % Heat flux continuity: k_fluid * dT/dr|_fluid = k_soil * dT/dr|_soil

    % Interpolate fluid temperature at tube wall
    T_fluid_wall = fields.T_fluid(:, end, t);

    % Map fluid wall temperature to soil inner boundary
    % (assuming thin tube wall with high conductivity)
    z_fluid_interp = linspace(0, grids.L_tube, grids.nz_fluid);
    z_soil_interp = linspace(0, grids.L_soil, grids.nz_soil);

    % Interpolate fluid wall temperature to soil grid
    T_soil_inner = interp1(z_fluid_interp, T_fluid_wall, z_soil_interp, 'linear', 'extrap');

    % Apply temperature continuity at soil inner boundary
    fields.T_soil(:, 1, t) = T_soil_inner;

    % Calculate heat transfer coefficient for fluid wall boundary
    h = calculate_heat_transfer_coefficient(fluid_props);

    % Apply convective boundary condition at fluid wall
    k_f = fluid_props.k;
    dr_f = grids.dr_fluid;

    for i = 1:grids.nz_fluid
        % Newton's law of cooling at fluid-tube interface
        T_wall = T_soil_inner(min(i, length(T_soil_inner)));
        fields.T_fluid(i, end, t) = (h * T_wall + (k_f/dr_f) * fields.T_fluid(i, end-1, t)) / ...
            (h + k_f/dr_f);
    end
end

function h = calculate_heat_transfer_coefficient(fluid_props)
% Calculate convective heat transfer coefficient

    D = 2 * 0.05;  % Tube diameter
    Re = fluid_props.rho * fluid_props.v_inlet * D / fluid_props.mu;
    Pr = fluid_props.mu * fluid_props.cp / fluid_props.k;

    if Re > 2300
        Nu = 0.023 * Re^0.8 * Pr^0.4;  % Dittus-Boelter
    else
        Nu = 3.66;  % Laminar flow
    end

    h = Nu * fluid_props.k / D;
end

function [u_r, u_z, sigma_rr, sigma_zz, sigma_rz] = solve_soil_mechanics(T_current, T_initial, grids, soil_props)
% Solve complete thermo-elastic problem in soil domain
% Uses finite difference method to solve equilibrium equations with thermal loading

    fprintf('  Solving thermo-elastic deformation...\n');

    % Initialize displacement and stress fields
    u_r = zeros(grids.nz_soil, grids.nr_soil);
    u_z = zeros(grids.nz_soil, grids.nr_soil);
    sigma_rr = zeros(grids.nz_soil, grids.nr_soil);
    sigma_zz = zeros(grids.nz_soil, grids.nr_soil);
    sigma_rz = zeros(grids.nz_soil, grids.nr_soil);
    sigma_tt = zeros(grids.nz_soil, grids.nr_soil); % Hoop stress

    % Material constants
    E = soil_props.E;
    nu = soil_props.nu;
    alpha_T = soil_props.alpha_T;

    % Lame parameters
    lambda = E * nu / ((1 + nu) * (1 - 2*nu));
    mu = E / (2 * (1 + nu));

    % Temperature change
    dT = T_current - T_initial;

    % Grid spacing
    dr = grids.dr_soil;
    dz = grids.dz_soil;

    % Method 1: Enhanced analytical solution for axisymmetric case
    % This provides a more accurate solution than the previous simplified approach

    for i = 1:grids.nz_soil
        for j = 1:grids.nr_soil
            r = grids.r_soil(j);
            z = grids.z_soil(i);

            % Thermal strain
            epsilon_T = alpha_T * dT(i, j);

            % Enhanced displacement calculation
            % Accounts for axisymmetric thermal expansion with proper boundary conditions

            % Radial displacement (free thermal expansion modified by constraints)
            if r > grids.R_tube_outer
                % Distance-dependent thermal expansion
                r_normalized = (r - grids.R_tube_outer) / (grids.R_soil - grids.R_tube_outer);
                constraint_factor = 1.0 - 0.5 * r_normalized; % Decreasing constraint with distance

                u_r(i, j) = epsilon_T * r * constraint_factor;

                % Depth-dependent effects (surface is less constrained)
                depth_factor = 1.0 - 0.3 * (z / grids.L_soil);
                u_r(i, j) = u_r(i, j) * depth_factor;
            end

            % Axial displacement (thermal expansion in z-direction)
            % Constrained by boundary conditions at top and bottom
            z_normalized = z / grids.L_soil;
            if z_normalized > 0.1 && z_normalized < 0.9 % Away from boundaries
                u_z(i, j) = epsilon_T * z * 0.5; % Reduced due to constraints
            end

            % Calculate strains from displacements
            % Radial strain
            if j > 1 && j < grids.nr_soil
                epsilon_rr = (u_r(i, j+1) - u_r(i, j-1)) / (2 * dr);
            else
                epsilon_rr = epsilon_T; % Use thermal strain at boundaries
            end

            % Axial strain
            if i > 1 && i < grids.nz_soil
                epsilon_zz = (u_z(i+1, j) - u_z(i-1, j)) / (2 * dz);
            else
                epsilon_zz = epsilon_T * 0.5; % Reduced at boundaries
            end

            % Hoop strain
            if r > 1e-10
                epsilon_tt = u_r(i, j) / r;
            else
                epsilon_tt = epsilon_rr; % At centerline
            end

            % Shear strain
            gamma_rz = 0; % Simplified - could be calculated from displacement gradients
            if i > 1 && i < grids.nz_soil && j > 1 && j < grids.nr_soil
                du_r_dz = (u_r(i+1, j) - u_r(i-1, j)) / (2 * dz);
                du_z_dr = (u_z(i, j+1) - u_z(i, j-1)) / (2 * dr);
                gamma_rz = du_r_dz + du_z_dr;
            end

            % Calculate stresses using constitutive relations
            % Volumetric strain
            epsilon_vol = epsilon_rr + epsilon_zz + epsilon_tt;

            % Thermal stress coefficient
            thermal_stress_coeff = (3 * lambda + 2 * mu) * alpha_T * dT(i, j);

            % Normal stresses
            sigma_rr(i, j) = lambda * epsilon_vol + 2 * mu * epsilon_rr - thermal_stress_coeff;
            sigma_zz(i, j) = lambda * epsilon_vol + 2 * mu * epsilon_zz - thermal_stress_coeff;
            sigma_tt(i, j) = lambda * epsilon_vol + 2 * mu * epsilon_tt - thermal_stress_coeff;

            % Shear stress
            sigma_rz(i, j) = mu * gamma_rz;
        end
    end

    % Apply mechanical boundary conditions
    % Fixed displacement at far boundaries
    u_r(:, end) = 0;    % Far radial boundary (fixed)
    u_z(1, :) = 0;      % Top boundary (fixed)
    u_z(end, :) = 0;    % Bottom boundary (fixed)

    % Free surface at tube-soil interface (stress-free in simplified model)
    % In reality, this would be coupled with tube deformation

    fprintf('  Thermo-elastic analysis completed.\n');
    fprintf('  Max radial displacement: %.3f mm\n', max(u_r(:)) * 1000);
    fprintf('  Max axial displacement: %.3f mm\n', max(abs(u_z(:))) * 1000);
    fprintf('  Max radial stress: %.1f kPa\n', max(abs(sigma_rr(:))) / 1000);

end

function visualize_coupled_results(fields, grids, dt, nt, soil_props)
% Comprehensive visualization of coupled thermo-mechanical results

    fprintf('Generating visualization...\n');

    % Create figure with multiple subplots
    figure('Position', [50, 50, 1400, 1000]);

    % Time vector
    time_hours = (0:nt-1) * dt / 3600;

    % Check data dimensions and create proper coordinate arrays
    [nz_fluid, nr_fluid] = size(fields.T_fluid(:, :, end));
    [nz_soil, nr_soil] = size(fields.T_soil(:, :, end));

    % Create coordinate vectors for plotting
    z_fluid_plot = linspace(0, grids.L_tube, nz_fluid);
    r_fluid_plot = linspace(0, grids.R_tube, nr_fluid);
    z_soil_plot = linspace(0, grids.L_soil, nz_soil);
    r_soil_plot = linspace(grids.R_tube_outer, grids.R_soil, nr_soil);

    % Create meshgrids for contour plots
    [Z_fluid_plot, R_fluid_plot] = meshgrid(z_fluid_plot, r_fluid_plot);
    [Z_soil_plot, R_soil_plot] = meshgrid(z_soil_plot, r_soil_plot);

    % 1. Fluid temperature contours
    subplot(3, 4, 1);
    if nr_fluid > 1 && nz_fluid > 1
        contourf(Z_fluid_plot, R_fluid_plot, (fields.T_fluid(:, :, end) - 273)', 15);
        colorbar;
    else
        plot(z_fluid_plot, fields.T_fluid(:, 1, end) - 273, 'b-', 'LineWidth', 2);
        ylabel('Temperature [°C]');
    end
    title('Fluid Temperature [°C]');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');

    % 2. Soil temperature contours
    subplot(3, 4, 2);
    if nr_soil > 1 && nz_soil > 1
        contourf(Z_soil_plot, R_soil_plot, (fields.T_soil(:, :, end) - 273)', 15);
        colorbar;
    else
        plot(z_soil_plot, fields.T_soil(:, 1, end) - 273, 'r-', 'LineWidth', 2);
        ylabel('Temperature [°C]');
    end
    title('Soil Temperature [°C]');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');

    % 3. Radial displacement
    subplot(3, 4, 3);
    if nr_soil > 1 && nz_soil > 1
        contourf(Z_soil_plot, R_soil_plot, (fields.u_r(:, :, end) * 1000)', 15);
        colorbar;
    else
        plot(z_soil_plot, fields.u_r(:, 1, end) * 1000, 'g-', 'LineWidth', 2);
        ylabel('Displacement [mm]');
    end
    title('Radial Displacement [mm]');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');

    % 4. Axial displacement
    subplot(3, 4, 4);
    if nr_soil > 1 && nz_soil > 1
        contourf(Z_soil_plot, R_soil_plot, (fields.u_z(:, :, end) * 1000)', 15);
        colorbar;
    else
        plot(z_soil_plot, fields.u_z(:, 1, end) * 1000, 'm-', 'LineWidth', 2);
        ylabel('Displacement [mm]');
    end
    title('Axial Displacement [mm]');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');

    % 5. Temperature evolution at different locations
    subplot(3, 4, 5);
    mid_z = round(nz_soil/2);
    if size(fields.T_soil, 3) > 1
        plot(time_hours, squeeze(fields.T_soil(mid_z, 1, :)) - 273, 'r-', 'LineWidth', 2);
        hold on;
        if nr_soil > 2
            plot(time_hours, squeeze(fields.T_soil(mid_z, round(nr_soil/2), :)) - 273, 'b-', 'LineWidth', 2);
        end
        plot(time_hours, squeeze(fields.T_soil(mid_z, end, :)) - 273, 'g-', 'LineWidth', 2);
        legend('Near tube', 'Mid-radius', 'Far field', 'Location', 'best');
    else
        plot(1, fields.T_soil(mid_z, 1, end) - 273, 'ro', 'MarkerSize', 8);
    end
    grid on;
    title('Soil Temperature Evolution');
    xlabel('Time [hours]');
    ylabel('Temperature [°C]');

    % 6. Radial temperature profile
    subplot(3, 4, 6);
    plot(r_soil_plot, fields.T_soil(mid_z, :, end) - 273, 'b-', 'LineWidth', 2);
    grid on;
    title('Radial Temperature Profile');
    xlabel('Radial Position [m]');
    ylabel('Temperature [°C]');

    % 7. Thermal stress distribution
    subplot(3, 4, 7);
    if nr_soil > 1 && nz_soil > 1
        contourf(Z_soil_plot, R_soil_plot, (fields.sigma_rr(:, :, end) / 1000)', 15);
        colorbar;
    else
        plot(z_soil_plot, fields.sigma_rr(:, 1, end) / 1000, 'c-', 'LineWidth', 2);
        ylabel('Stress [kPa]');
    end
    title('Radial Stress [kPa]');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');

    % 8. Displacement evolution
    subplot(3, 4, 8);
    if size(fields.u_r, 3) > 1
        plot(time_hours, squeeze(fields.u_r(mid_z, 1, :)) * 1000, 'r-', 'LineWidth', 2);
        hold on;
        if nr_soil > 2
            plot(time_hours, squeeze(fields.u_r(mid_z, round(nr_soil/2), :)) * 1000, 'b-', 'LineWidth', 2);
        end
        legend('Near tube', 'Mid-radius', 'Location', 'best');
    else
        plot(1, fields.u_r(mid_z, 1, end) * 1000, 'ro', 'MarkerSize', 8);
    end
    grid on;
    title('Radial Displacement Evolution');
    xlabel('Time [hours]');
    ylabel('Displacement [mm]');

    % 9-12. 3D visualizations
    subplot(3, 4, 9);
    if nr_soil > 1 && nz_soil > 1
        surf(Z_soil_plot, R_soil_plot, (fields.T_soil(:, :, end) - 273)');
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Temp [°C]');
        view(45, 30);
    else
        plot3(z_soil_plot, r_soil_plot(1)*ones(size(z_soil_plot)), fields.T_soil(:, 1, end) - 273, 'b-', 'LineWidth', 2);
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Temp [°C]');
    end
    title('3D Soil Temperature');

    subplot(3, 4, 10);
    if nr_soil > 1 && nz_soil > 1
        surf(Z_soil_plot, R_soil_plot, (fields.u_r(:, :, end) * 1000)');
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Disp [mm]');
        view(45, 30);
    else
        plot3(z_soil_plot, r_soil_plot(1)*ones(size(z_soil_plot)), fields.u_r(:, 1, end) * 1000, 'g-', 'LineWidth', 2);
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Disp [mm]');
    end
    title('3D Radial Displacement');

    subplot(3, 4, 11);
    if nr_soil > 1 && nz_soil > 1
        surf(Z_soil_plot, R_soil_plot, (fields.sigma_rr(:, :, end) / 1000)');
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Stress [kPa]');
        view(45, 30);
    else
        plot3(z_soil_plot, r_soil_plot(1)*ones(size(z_soil_plot)), fields.sigma_rr(:, 1, end) / 1000, 'c-', 'LineWidth', 2);
        xlabel('Axial [m]'); ylabel('Radial [m]'); zlabel('Stress [kPa]');
    end
    title('3D Radial Stress');

    % Heat flux analysis
    subplot(3, 4, 12);
    % Calculate heat flux at tube-soil interface
    if nr_soil > 1
        dT_dr = gradient(fields.T_soil(:, :, end), grids.dr_soil);
        heat_flux = -soil_props.k * dT_dr(:, 1);
    else
        % For 1D case, use simple finite difference
        heat_flux = -soil_props.k * (fields.T_soil(:, 1, end) - fields.T_soil(:, 1, end)) / grids.dr_soil;
        heat_flux = zeros(size(fields.T_soil(:, 1, end))); % Placeholder for 1D case
    end
    plot(z_soil_plot, heat_flux, 'r-', 'LineWidth', 2);
    grid on;
    title('Heat Flux at Interface');
    xlabel('Axial Position [m]');
    ylabel('Heat Flux [W/m²]');

    sgtitle('Geothermal Pile Coupled Thermo-Mechanical Analysis Results');

    % Save figure
    saveas(gcf, 'geothermal_pile_results.png');

    fprintf('Visualization completed and saved!\n');
end
