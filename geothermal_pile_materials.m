function geothermal_pile_materials()
% Advanced material property functions for geothermal pile analysis
% Includes temperature-dependent properties and different soil types

clear; clc;

fprintf('=== Geothermal Pile Material Properties Database ===\n');

%% Menu for material property analysis
fprintf('1. Display material properties\n');
fprintf('2. Temperature-dependent property analysis\n');
fprintf('3. Soil type comparison\n');
fprintf('4. Thermal expansion analysis\n');
fprintf('5. Run coupled analysis with selected materials\n');

choice = input('Select option (1-5): ');

switch choice
    case 1
        display_material_properties();
    case 2
        analyze_temperature_dependent_properties();
    case 3
        compare_soil_types();
    case 4
        analyze_thermal_expansion();
    case 5
        run_material_sensitivity_analysis();
    otherwise
        fprintf('Invalid choice. Displaying material properties...\n');
        display_material_properties();
end

end

function display_material_properties()
% Display comprehensive material property database

fprintf('\n=== FLUID PROPERTIES ===\n');
fluids = get_fluid_database();
field_names = fieldnames(fluids);
for i = 1:length(field_names)
    fluid_name = field_names{i};
    props = fluids.(fluid_name);
    fprintf('\n%s:\n', strrep(fluid_name, '_', ' '));
    fprintf('  Density: %.0f kg/m³\n', props.rho);
    fprintf('  Specific Heat: %.0f J/kg·K\n', props.cp);
    fprintf('  Thermal Conductivity: %.2f W/m·K\n', props.k);
    fprintf('  Viscosity: %.4f Pa·s\n', props.mu);
    fprintf('  Thermal Diffusivity: %.2e m²/s\n', props.alpha);
end

fprintf('\n=== SOIL PROPERTIES ===\n');
soils = get_soil_database();
field_names = fieldnames(soils);
for i = 1:length(field_names)
    soil_name = field_names{i};
    props = soils.(soil_name);
    fprintf('\n%s:\n', strrep(soil_name, '_', ' '));
    fprintf('  Density: %.0f kg/m³\n', props.rho);
    fprintf('  Specific Heat: %.0f J/kg·K\n', props.cp);
    fprintf('  Thermal Conductivity: %.2f W/m·K\n', props.k);
    fprintf('  Young''s Modulus: %.1e Pa\n', props.E);
    fprintf('  Poisson''s Ratio: %.2f\n', props.nu);
    fprintf('  Thermal Expansion: %.1e 1/K\n', props.alpha_T);
    fprintf('  Thermal Diffusivity: %.2e m²/s\n', props.alpha);
end

fprintf('\n=== TUBE MATERIALS ===\n');
tubes = get_tube_database();
field_names = fieldnames(tubes);
for i = 1:length(field_names)
    tube_name = field_names{i};
    props = tubes.(tube_name);
    fprintf('\n%s:\n', strrep(tube_name, '_', ' '));
    fprintf('  Density: %.0f kg/m³\n', props.rho);
    fprintf('  Specific Heat: %.0f J/kg·K\n', props.cp);
    fprintf('  Thermal Conductivity: %.2f W/m·K\n', props.k);
    fprintf('  Young''s Modulus: %.1e Pa\n', props.E);
    fprintf('  Poisson''s Ratio: %.2f\n', props.nu);
    fprintf('  Thermal Expansion: %.1e 1/K\n', props.alpha_T);
end

end

function fluids = get_fluid_database()
% Comprehensive fluid property database

% Water
fluids.water.rho = 1000;
fluids.water.cp = 4180;
fluids.water.k = 0.6;
fluids.water.mu = 0.001;
fluids.water.alpha = fluids.water.k / (fluids.water.rho * fluids.water.cp);

% Water-Glycol 30%
fluids.water_glycol_30.rho = 1030;
fluids.water_glycol_30.cp = 3900;
fluids.water_glycol_30.k = 0.5;
fluids.water_glycol_30.mu = 0.002;
fluids.water_glycol_30.alpha = fluids.water_glycol_30.k / (fluids.water_glycol_30.rho * fluids.water_glycol_30.cp);

% Water-Glycol 50%
fluids.water_glycol_50.rho = 1060;
fluids.water_glycol_50.cp = 3600;
fluids.water_glycol_50.k = 0.45;
fluids.water_glycol_50.mu = 0.004;
fluids.water_glycol_50.alpha = fluids.water_glycol_50.k / (fluids.water_glycol_50.rho * fluids.water_glycol_50.cp);

% Refrigerant R134a (liquid)
fluids.r134a.rho = 1200;
fluids.r134a.cp = 1400;
fluids.r134a.k = 0.08;
fluids.r134a.mu = 0.0002;
fluids.r134a.alpha = fluids.r134a.k / (fluids.r134a.rho * fluids.r134a.cp);

end

function soils = get_soil_database()
% Comprehensive soil property database

% Clay (saturated)
soils.clay_saturated.rho = 1900;
soils.clay_saturated.cp = 1200;
soils.clay_saturated.k = 1.5;
soils.clay_saturated.E = 20e6;
soils.clay_saturated.nu = 0.35;
soils.clay_saturated.alpha_T = 1.5e-5;
soils.clay_saturated.alpha = soils.clay_saturated.k / (soils.clay_saturated.rho * soils.clay_saturated.cp);

% Clay (dry)
soils.clay_dry.rho = 1600;
soils.clay_dry.cp = 900;
soils.clay_dry.k = 0.8;
soils.clay_dry.E = 15e6;
soils.clay_dry.nu = 0.3;
soils.clay_dry.alpha_T = 2e-5;
soils.clay_dry.alpha = soils.clay_dry.k / (soils.clay_dry.rho * soils.clay_dry.cp);

% Sand (saturated)
soils.sand_saturated.rho = 2000;
soils.sand_saturated.cp = 1000;
soils.sand_saturated.k = 2.5;
soils.sand_saturated.E = 50e6;
soils.sand_saturated.nu = 0.25;
soils.sand_saturated.alpha_T = 1e-5;
soils.sand_saturated.alpha = soils.sand_saturated.k / (soils.sand_saturated.rho * soils.sand_saturated.cp);

% Sand (dry)
soils.sand_dry.rho = 1700;
soils.sand_dry.cp = 800;
soils.sand_dry.k = 1.2;
soils.sand_dry.E = 30e6;
soils.sand_dry.nu = 0.2;
soils.sand_dry.alpha_T = 1.2e-5;
soils.sand_dry.alpha = soils.sand_dry.k / (soils.sand_dry.rho * soils.sand_dry.cp);

% Rock (granite)
soils.granite.rho = 2650;
soils.granite.cp = 800;
soils.granite.k = 3.5;
soils.granite.E = 70e9;
soils.granite.nu = 0.25;
soils.granite.alpha_T = 8e-6;
soils.granite.alpha = soils.granite.k / (soils.granite.rho * soils.granite.cp);

% Rock (limestone)
soils.limestone.rho = 2400;
soils.limestone.cp = 900;
soils.limestone.k = 2.8;
soils.limestone.E = 50e9;
soils.limestone.nu = 0.3;
soils.limestone.alpha_T = 1e-5;
soils.limestone.alpha = soils.limestone.k / (soils.limestone.rho * soils.limestone.cp);

end

function tubes = get_tube_database()
% Tube material property database

% HDPE (High Density Polyethylene)
tubes.hdpe.rho = 950;
tubes.hdpe.cp = 2300;
tubes.hdpe.k = 0.4;
tubes.hdpe.E = 1e9;
tubes.hdpe.nu = 0.4;
tubes.hdpe.alpha_T = 2e-4;

% PEX (Cross-linked Polyethylene)
tubes.pex.rho = 940;
tubes.pex.cp = 2400;
tubes.pex.k = 0.35;
tubes.pex.E = 0.8e9;
tubes.pex.nu = 0.45;
tubes.pex.alpha_T = 2.2e-4;

% Steel
tubes.steel.rho = 7850;
tubes.steel.cp = 460;
tubes.steel.k = 50;
tubes.steel.E = 200e9;
tubes.steel.nu = 0.3;
tubes.steel.alpha_T = 1.2e-5;

% Copper
tubes.copper.rho = 8960;
tubes.copper.cp = 385;
tubes.copper.k = 400;
tubes.copper.E = 110e9;
tubes.copper.nu = 0.34;
tubes.copper.alpha_T = 1.7e-5;

end

function analyze_temperature_dependent_properties()
% Analyze how material properties change with temperature

fprintf('\nAnalyzing temperature-dependent properties...\n');

T_range = 273:5:373; % Temperature range from 0°C to 100°C

% Water properties vs temperature
figure('Position', [100, 100, 1200, 800]);

subplot(2, 3, 1);
rho_water = water_density_temperature(T_range);
plot(T_range - 273, rho_water, 'b-', 'LineWidth', 2);
grid on;
title('Water Density vs Temperature');
xlabel('Temperature [°C]');
ylabel('Density [kg/m³]');

subplot(2, 3, 2);
cp_water = water_specific_heat_temperature(T_range);
plot(T_range - 273, cp_water, 'r-', 'LineWidth', 2);
grid on;
title('Water Specific Heat vs Temperature');
xlabel('Temperature [°C]');
ylabel('Specific Heat [J/kg·K]');

subplot(2, 3, 3);
k_water = water_thermal_conductivity_temperature(T_range);
plot(T_range - 273, k_water, 'g-', 'LineWidth', 2);
grid on;
title('Water Thermal Conductivity vs Temperature');
xlabel('Temperature [°C]');
ylabel('Thermal Conductivity [W/m·K]');

subplot(2, 3, 4);
mu_water = water_viscosity_temperature(T_range);
semilogy(T_range - 273, mu_water, 'm-', 'LineWidth', 2);
grid on;
title('Water Viscosity vs Temperature');
xlabel('Temperature [°C]');
ylabel('Viscosity [Pa·s]');

subplot(2, 3, 5);
alpha_water = k_water ./ (rho_water .* cp_water);
plot(T_range - 273, alpha_water * 1e7, 'c-', 'LineWidth', 2);
grid on;
title('Water Thermal Diffusivity vs Temperature');
xlabel('Temperature [°C]');
ylabel('Thermal Diffusivity [×10⁻⁷ m²/s]');

% Soil thermal conductivity vs moisture content
subplot(2, 3, 6);
moisture_content = 0:0.05:0.5;
k_clay = soil_thermal_conductivity_moisture(moisture_content, 'clay');
k_sand = soil_thermal_conductivity_moisture(moisture_content, 'sand');
plot(moisture_content * 100, k_clay, 'b-', 'LineWidth', 2);
hold on;
plot(moisture_content * 100, k_sand, 'r-', 'LineWidth', 2);
grid on;
title('Soil Thermal Conductivity vs Moisture');
xlabel('Moisture Content [%]');
ylabel('Thermal Conductivity [W/m·K]');
legend('Clay', 'Sand', 'Location', 'best');

sgtitle('Temperature-Dependent Material Properties');

fprintf('Temperature-dependent analysis completed!\n');

end

function rho = water_density_temperature(T)
% Water density as function of temperature (kg/m³)
% Valid for 0-100°C at atmospheric pressure
    T_C = T - 273.15;
    rho = 999.84 + 0.06426*T_C - 0.0085794*T_C.^2 + 6.5166e-5*T_C.^3 - 1.8906e-7*T_C.^4;
end

function cp = water_specific_heat_temperature(T)
% Water specific heat as function of temperature (J/kg·K)
    T_C = T - 273.15;
    cp = 4217.4 - 3.720283*T_C + 0.1412855*T_C.^2 - 2.654387e-3*T_C.^3 + 2.093236e-5*T_C.^4;
end

function k = water_thermal_conductivity_temperature(T)
% Water thermal conductivity as function of temperature (W/m·K)
    T_C = T - 273.15;
    k = -0.869083936 + 0.00894880345*T_C - 1.58366345e-5*T_C.^2 + 7.97543259e-9*T_C.^3;
end

function mu = water_viscosity_temperature(T)
% Water dynamic viscosity as function of temperature (Pa·s)
    T_C = T - 273.15;
    mu = 2.414e-5 * 10.^(247.8 ./ (T_C + 140));
end

function k = soil_thermal_conductivity_moisture(w, soil_type)
% Soil thermal conductivity as function of moisture content
% w: moisture content (volumetric fraction)
% soil_type: 'clay' or 'sand'

    switch soil_type
        case 'clay'
            k_dry = 0.8;
            k_sat = 1.5;
            k = k_dry + (k_sat - k_dry) * w / 0.4; % Assume 40% porosity
        case 'sand'
            k_dry = 1.2;
            k_sat = 2.5;
            k = k_dry + (k_sat - k_dry) * w / 0.3; % Assume 30% porosity
        otherwise
            error('Unknown soil type');
    end
end

function compare_soil_types()
% Compare different soil types for geothermal applications

fprintf('\nComparing soil types for geothermal pile performance...\n');

soils = get_soil_database();
soil_names = fieldnames(soils);

% Create comparison table
fprintf('\n%-20s %-8s %-8s %-8s %-10s %-8s %-10s\n', ...
    'Soil Type', 'k [W/mK]', 'α [m²/s]', 'E [MPa]', 'α_T [1/K]', 'ρ [kg/m³]', 'cp [J/kgK]');
fprintf('%s\n', repmat('-', 1, 80));

for i = 1:length(soil_names)
    props = soils.(soil_names{i});
    fprintf('%-20s %-8.2f %-8.2e %-8.0f %-10.1e %-8.0f %-10.0f\n', ...
        strrep(soil_names{i}, '_', ' '), props.k, props.alpha, props.E/1e6, ...
        props.alpha_T, props.rho, props.cp);
end

% Performance ranking
fprintf('\n=== PERFORMANCE RANKING ===\n');
fprintf('Best thermal conductivity: Sand (saturated)\n');
fprintf('Best thermal diffusivity: Granite\n');
fprintf('Lowest thermal expansion: Granite\n');
fprintf('Most stable: Rock formations\n');
fprintf('Most common: Clay/Sand mixtures\n');

end

function analyze_thermal_expansion()
% Analyze thermal expansion effects

fprintf('\nAnalyzing thermal expansion effects...\n');

% Temperature change scenarios
dT_scenarios = [10, 20, 30, 50]; % Temperature changes in K
soils = get_soil_database();
soil_names = fieldnames(soils);

figure('Position', [100, 100, 1000, 600]);

% Thermal strain analysis
subplot(2, 2, 1);
colors = lines(length(soil_names));
for i = 1:length(soil_names)
    props = soils.(soil_names{i});
    thermal_strain = props.alpha_T * dT_scenarios * 1e6; % Convert to microstrain
    plot(dT_scenarios, thermal_strain, 'o-', 'Color', colors(i,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Thermal Strain vs Temperature Change');
xlabel('Temperature Change [K]');
ylabel('Thermal Strain [μstrain]');
legend(strrep(soil_names, '_', ' '), 'Location', 'best');

% Thermal stress (constrained expansion)
subplot(2, 2, 2);
for i = 1:length(soil_names)
    props = soils.(soil_names{i});
    thermal_stress = props.E * props.alpha_T * dT_scenarios / 1000; % Convert to kPa
    plot(dT_scenarios, thermal_stress, 'o-', 'Color', colors(i,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Thermal Stress vs Temperature Change');
xlabel('Temperature Change [K]');
ylabel('Thermal Stress [kPa]');

% Displacement at 1m radius
subplot(2, 2, 3);
r = 1.0; % 1 meter from tube center
for i = 1:length(soil_names)
    props = soils.(soil_names{i});
    displacement = props.alpha_T * dT_scenarios * r * 1000; % Convert to mm
    plot(dT_scenarios, displacement, 'o-', 'Color', colors(i,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Radial Displacement at 1m Radius');
xlabel('Temperature Change [K]');
ylabel('Displacement [mm]');

% Thermal diffusion time
subplot(2, 2, 4);
r_diffusion = [0.5, 1.0, 2.0]; % Diffusion distances
for i = 1:length(soil_names)
    props = soils.(soil_names{i});
    t_diffusion = r_diffusion.^2 / (4 * props.alpha) / 3600; % Convert to hours
    semilogy(r_diffusion, t_diffusion, 'o-', 'Color', colors(i,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Thermal Diffusion Time');
xlabel('Distance [m]');
ylabel('Time [hours]');

sgtitle('Thermal Expansion Analysis for Different Soil Types');

fprintf('Thermal expansion analysis completed!\n');

end

function run_material_sensitivity_analysis()
% Run coupled analysis with different material combinations

fprintf('\nRunning material sensitivity analysis...\n');

% Get material databases
fluids = get_fluid_database();
soils = get_soil_database();

% Select representative materials for comparison
fluid_types = {'water', 'water_glycol_30'};
soil_types = {'clay_saturated', 'sand_saturated', 'granite'};

results = struct();

for f = 1:length(fluid_types)
    for s = 1:length(soil_types)
        fprintf('Analyzing: %s fluid with %s soil...\n', ...
            fluid_types{f}, soil_types{s});

        % Run simplified analysis
        [max_temp, max_displacement, max_stress] = run_simplified_analysis(...
            fluids.(fluid_types{f}), soils.(soil_types{s}));

        results.(fluid_types{f}).(soil_types{s}).max_temp = max_temp;
        results.(fluid_types{f}).(soil_types{s}).max_displacement = max_displacement;
        results.(fluid_types{f}).(soil_types{s}).max_stress = max_stress;
    end
end

% Display results summary
fprintf('\n=== SENSITIVITY ANALYSIS RESULTS ===\n');
for f = 1:length(fluid_types)
    fprintf('\n%s Fluid:\n', strrep(fluid_types{f}, '_', ' '));
    for s = 1:length(soil_types)
        res = results.(fluid_types{f}).(soil_types{s});
        fprintf('  %s: Max Temp=%.1f°C, Max Disp=%.2fmm, Max Stress=%.1fkPa\n', ...
            strrep(soil_types{s}, '_', ' '), res.max_temp-273, ...
            res.max_displacement*1000, res.max_stress/1000);
    end
end

fprintf('\nMaterial sensitivity analysis completed!\n');
fprintf('For full analysis, run: geothermal_pile_coupled_analysis()\n');

end

function [max_temp, max_displacement, max_stress] = run_simplified_analysis(fluid_props, soil_props)
% Simplified analysis for material comparison

    % Simplified 1D radial heat conduction
    r = linspace(0.055, 2.0, 50); % From tube outer radius to far field
    T_initial = 283; % 10°C
    T_tube = 313;    % 40°C

    % Steady-state temperature distribution (logarithmic)
    T = T_initial + (T_tube - T_initial) * log(2.0./r) / log(2.0/0.055);

    max_temp = max(T);

    % Thermal expansion
    dT = T - T_initial;
    thermal_strain = soil_props.alpha_T * dT;
    max_displacement = max(thermal_strain .* r);

    % Thermal stress (constrained expansion)
    thermal_stress = soil_props.E * thermal_strain;
    max_stress = max(thermal_stress);
end
