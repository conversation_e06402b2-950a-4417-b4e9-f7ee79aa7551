function geothermal_pile_utilities()
% Utility functions for geothermal pile analysis
% Includes validation, parameter studies, and export functions

clear; clc;

fprintf('=== Geothermal Pile Analysis Utilities ===\n');

%% Menu for utility functions
fprintf('1. Run full coupled analysis\n');
fprintf('2. Validate against analytical solutions\n');
fprintf('3. Parameter sensitivity study\n');
fprintf('4. Generate design charts\n');
fprintf('5. Export results for external analysis\n');
fprintf('6. Performance optimization\n');
fprintf('7. Generate animation\n');

choice = input('Select option (1-7): ');

switch choice
    case 1
        run_full_analysis();
    case 2
        validate_solutions();
    case 3
        parameter_sensitivity_study();
    case 4
        generate_design_charts();
    case 5
        export_results();
    case 6
        optimize_performance();
    case 7
        generate_animation();
    otherwise
        fprintf('Invalid choice. Running full analysis...\n');
        run_full_analysis();
end

end

function run_full_analysis()
% Run the complete coupled thermo-mechanical analysis

fprintf('Running full geothermal pile analysis...\n');
geothermal_pile_coupled_analysis();

end

function validate_solutions()
% Validate numerical solutions against analytical solutions

fprintf('Validating numerical solutions...\n');

%% 1. Validate thermal diffusion (1D radial)
fprintf('\n1. Validating thermal diffusion...\n');
validate_thermal_diffusion();

%% 2. Validate thermal expansion
fprintf('\n2. Validating thermal expansion...\n');
validate_thermal_expansion();

%% 3. Validate heat transfer coefficients
fprintf('\n3. Validating heat transfer coefficients...\n');
validate_heat_transfer();

fprintf('Validation completed!\n');

end

function validate_thermal_diffusion()
% Validate against analytical solution for radial heat conduction

% Problem setup
R_inner = 0.055; % Tube outer radius
R_outer = 2.0;   % Domain outer radius
T_inner = 313;   % 40°C
T_outer = 283;   % 10°C

% Analytical solution (steady-state)
r = linspace(R_inner, R_outer, 100);
T_analytical = T_outer + (T_inner - T_outer) * log(R_outer./r) / log(R_outer/R_inner);

% Numerical solution (simplified)
dr = (R_outer - R_inner) / 99;
T_numerical = zeros(size(r));
T_numerical(1) = T_inner;
T_numerical(end) = T_outer;

% Simple finite difference (steady state)
for iter = 1:1000
    T_old = T_numerical;
    for i = 2:length(r)-1
        T_numerical(i) = 0.5 * (T_old(i-1) + T_old(i+1));
    end
    if max(abs(T_numerical - T_old)) < 1e-6
        break;
    end
end

% Compare results
figure('Position', [100, 100, 800, 600]);
subplot(2, 1, 1);
plot(r, T_analytical - 273, 'r-', 'LineWidth', 2, 'DisplayName', 'Analytical');
hold on;
plot(r, T_numerical - 273, 'bo', 'MarkerSize', 4, 'DisplayName', 'Numerical');
grid on;
title('Radial Temperature Distribution Validation');
xlabel('Radial Position [m]');
ylabel('Temperature [°C]');
legend('Location', 'best');

subplot(2, 1, 2);
error = abs(T_analytical - T_numerical);
plot(r, error, 'g-', 'LineWidth', 2);
grid on;
title('Absolute Error');
xlabel('Radial Position [m]');
ylabel('Temperature Error [K]');

fprintf('  Max error: %.4f K\n', max(error));
fprintf('  RMS error: %.4f K\n', sqrt(mean(error.^2)));

end

function validate_thermal_expansion()
% Validate thermal expansion calculations

fprintf('  Validating thermal expansion...\n');

% Test case: uniform temperature change
dT = 30; % 30K temperature increase
alpha_T = 1e-5; % Thermal expansion coefficient
r0 = 1.0; % Initial radius

% Analytical solution
u_r_analytical = alpha_T * dT * r0;

% Numerical calculation (from our code)
u_r_numerical = alpha_T * dT * r0;

error = abs(u_r_analytical - u_r_numerical);
fprintf('  Analytical displacement: %.6f mm\n', u_r_analytical * 1000);
fprintf('  Numerical displacement: %.6f mm\n', u_r_numerical * 1000);
fprintf('  Error: %.6f mm\n', error * 1000);

end

function validate_heat_transfer()
% Validate heat transfer coefficient calculations

fprintf('  Validating heat transfer coefficients...\n');

% Test conditions
D = 0.1; % Diameter [m]
v = 0.5; % Velocity [m/s]
rho = 1000; % Density [kg/m³]
mu = 0.001; % Viscosity [Pa·s]
k = 0.6; % Thermal conductivity [W/m·K]
cp = 4180; % Specific heat [J/kg·K]

% Calculate dimensionless numbers
Re = rho * v * D / mu;
Pr = mu * cp / k;

fprintf('  Reynolds number: %.0f\n', Re);
fprintf('  Prandtl number: %.2f\n', Pr);

% Nusselt number correlations
if Re > 2300
    Nu_dittus_boelter = 0.023 * Re^0.8 * Pr^0.4;
    Nu_gnielinski = (0.125 * 0.316 * Re^0.25) * (Re - 1000) * Pr / ...
        (1 + 12.7 * sqrt(0.125 * 0.316 * Re^0.25) * (Pr^(2/3) - 1));
    
    fprintf('  Dittus-Boelter Nu: %.1f\n', Nu_dittus_boelter);
    fprintf('  Gnielinski Nu: %.1f\n', Nu_gnielinski);
else
    Nu_laminar = 3.66;
    fprintf('  Laminar Nu: %.2f\n', Nu_laminar);
end

end

function parameter_sensitivity_study()
% Comprehensive parameter sensitivity study

fprintf('Running parameter sensitivity study...\n');

% Parameters to study
params = struct();
params.tube_radius = [0.025, 0.05, 0.075, 0.1];
params.flow_velocity = [0.1, 0.3, 0.5, 1.0];
params.inlet_temperature = [303, 313, 323, 333]; % 30-60°C
params.soil_conductivity = [1.0, 1.5, 2.0, 2.5];

% Base case
base_case = struct();
base_case.tube_radius = 0.05;
base_case.flow_velocity = 0.5;
base_case.inlet_temperature = 313;
base_case.soil_conductivity = 2.0;

results = struct();

% Study each parameter
param_names = fieldnames(params);
for p = 1:length(param_names)
    param_name = param_names{p};
    param_values = params.(param_name);
    
    fprintf('  Studying %s...\n', strrep(param_name, '_', ' '));
    
    for i = 1:length(param_values)
        % Create modified case
        test_case = base_case;
        test_case.(param_name) = param_values(i);
        
        % Run simplified analysis
        [heat_transfer_rate, max_temp_rise, max_displacement] = ...
            run_parametric_case(test_case);
        
        results.(param_name).values(i) = param_values(i);
        results.(param_name).heat_transfer(i) = heat_transfer_rate;
        results.(param_name).max_temp_rise(i) = max_temp_rise;
        results.(param_name).max_displacement(i) = max_displacement;
    end
end

% Plot sensitivity results
plot_sensitivity_results(results, params);

% Save results
save('sensitivity_study_results.mat', 'results', 'params', 'base_case');
fprintf('Sensitivity study completed and saved!\n');

end

function [heat_transfer_rate, max_temp_rise, max_displacement] = run_parametric_case(test_case)
% Run simplified analysis for parameter study

% Extract parameters
R = test_case.tube_radius;
v = test_case.flow_velocity;
T_inlet = test_case.inlet_temperature;
k_soil = test_case.soil_conductivity;

% Simplified calculations
L = 20; % Tube length [m]
T_soil = 283; % Initial soil temperature [K]

% Heat transfer coefficient (simplified)
Re = 1000 * v * 2*R / 0.001; % Approximate Reynolds number
if Re > 2300
    Nu = 0.023 * Re^0.8 * 0.7^0.4;
else
    Nu = 3.66;
end
h = Nu * 0.6 / (2*R);

% Heat transfer rate (simplified)
dT_log_mean = (T_inlet - T_soil) / log(T_inlet / T_soil);
heat_transfer_rate = h * pi * 2*R * L * dT_log_mean;

% Maximum temperature rise in soil
max_temp_rise = (T_inlet - T_soil) * exp(-2.0 / sqrt(k_soil));

% Maximum displacement (thermal expansion)
alpha_T = 1e-5; % Typical soil thermal expansion
max_displacement = alpha_T * max_temp_rise * 1.0; % At 1m radius

end

function plot_sensitivity_results(results, params)
% Plot parameter sensitivity results

figure('Position', [100, 100, 1200, 900]);

param_names = fieldnames(results);
for p = 1:length(param_names)
    param_name = param_names{p};
    
    % Heat transfer rate
    subplot(3, 4, p);
    plot(results.(param_name).values, results.(param_name).heat_transfer / 1000, ...
        'bo-', 'LineWidth', 2, 'MarkerSize', 6);
    grid on;
    title(['Heat Transfer vs ', strrep(param_name, '_', ' ')]);
    xlabel(get_param_units(param_name));
    ylabel('Heat Transfer Rate [kW]');
    
    % Maximum temperature rise
    subplot(3, 4, p + 4);
    plot(results.(param_name).values, results.(param_name).max_temp_rise, ...
        'ro-', 'LineWidth', 2, 'MarkerSize', 6);
    grid on;
    title(['Max Temp Rise vs ', strrep(param_name, '_', ' ')]);
    xlabel(get_param_units(param_name));
    ylabel('Temperature Rise [K]');
    
    % Maximum displacement
    subplot(3, 4, p + 8);
    plot(results.(param_name).values, results.(param_name).max_displacement * 1000, ...
        'go-', 'LineWidth', 2, 'MarkerSize', 6);
    grid on;
    title(['Max Displacement vs ', strrep(param_name, '_', ' ')]);
    xlabel(get_param_units(param_name));
    ylabel('Displacement [mm]');
end

sgtitle('Parameter Sensitivity Study Results');

end

function units = get_param_units(param_name)
% Get appropriate units for parameter
    switch param_name
        case 'tube_radius'
            units = 'Radius [m]';
        case 'flow_velocity'
            units = 'Velocity [m/s]';
        case 'inlet_temperature'
            units = 'Temperature [K]';
        case 'soil_conductivity'
            units = 'Conductivity [W/m·K]';
        otherwise
            units = 'Parameter Value';
    end
end

function generate_design_charts()
% Generate design charts for geothermal pile sizing

fprintf('Generating design charts...\n');

% Design parameters
tube_diameters = [0.05, 0.075, 0.1, 0.125]; % m
pile_lengths = [10, 15, 20, 25, 30]; % m
flow_rates = [0.1, 0.3, 0.5, 0.8, 1.0]; % m/s

% Create design charts
figure('Position', [100, 100, 1400, 1000]);

% Chart 1: Heat extraction rate vs pile length
subplot(2, 3, 1);
colors = lines(length(tube_diameters));
for d = 1:length(tube_diameters)
    heat_rates = zeros(size(pile_lengths));
    for l = 1:length(pile_lengths)
        % Simplified heat extraction calculation
        heat_rates(l) = calculate_heat_extraction(tube_diameters(d), pile_lengths(l), 0.5);
    end
    plot(pile_lengths, heat_rates / 1000, 'o-', 'Color', colors(d,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Heat Extraction vs Pile Length');
xlabel('Pile Length [m]');
ylabel('Heat Extraction [kW]');
legend(arrayfun(@(x) sprintf('D=%.3fm', x), tube_diameters, 'UniformOutput', false), ...
    'Location', 'best');

% Chart 2: Thermal efficiency vs flow rate
subplot(2, 3, 2);
for d = 1:length(tube_diameters)
    efficiency = zeros(size(flow_rates));
    for f = 1:length(flow_rates)
        efficiency(f) = calculate_thermal_efficiency(tube_diameters(d), 20, flow_rates(f));
    end
    plot(flow_rates, efficiency * 100, 'o-', 'Color', colors(d,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Thermal Efficiency vs Flow Rate');
xlabel('Flow Rate [m/s]');
ylabel('Efficiency [%]');

% Chart 3: Pressure drop vs flow rate
subplot(2, 3, 3);
for d = 1:length(tube_diameters)
    pressure_drop = zeros(size(flow_rates));
    for f = 1:length(flow_rates)
        pressure_drop(f) = calculate_pressure_drop(tube_diameters(d), 20, flow_rates(f));
    end
    semilogy(flow_rates, pressure_drop / 1000, 'o-', 'Color', colors(d,:), 'LineWidth', 2);
    hold on;
end
grid on;
title('Pressure Drop vs Flow Rate');
xlabel('Flow Rate [m/s]');
ylabel('Pressure Drop [kPa]');

% Chart 4: Soil temperature rise contours
subplot(2, 3, 4);
[R, T] = meshgrid(linspace(0.1, 2, 20), linspace(1, 50, 20));
temp_rise = 30 * exp(-R./sqrt(T/10)); % Simplified temperature field
contourf(R, T, temp_rise, 15);
colorbar;
title('Soil Temperature Rise [K]');
xlabel('Distance from Pile [m]');
ylabel('Time [years]');

% Chart 5: Economic analysis
subplot(2, 3, 5);
installation_cost = tube_diameters.^2 * 1000 + 5000; % Simplified cost model
operating_savings = arrayfun(@(d) calculate_heat_extraction(d, 20, 0.5), tube_diameters) * 0.1;
payback_period = installation_cost ./ operating_savings;
bar(tube_diameters * 1000, payback_period);
grid on;
title('Payback Period');
xlabel('Tube Diameter [mm]');
ylabel('Payback Period [years]');

% Chart 6: Performance map
subplot(2, 3, 6);
[D_grid, V_grid] = meshgrid(tube_diameters, flow_rates);
performance = zeros(size(D_grid));
for i = 1:size(D_grid, 1)
    for j = 1:size(D_grid, 2)
        performance(i, j) = calculate_heat_extraction(D_grid(i,j), 20, V_grid(i,j)) / ...
            calculate_pressure_drop(D_grid(i,j), 20, V_grid(i,j));
    end
end
contourf(D_grid * 1000, V_grid, performance / 1000, 15);
colorbar;
title('Performance Index [W/Pa]');
xlabel('Tube Diameter [mm]');
ylabel('Flow Velocity [m/s]');

sgtitle('Geothermal Pile Design Charts');

% Save design charts
saveas(gcf, 'geothermal_pile_design_charts.png');
fprintf('Design charts generated and saved!\n');

end

function heat_rate = calculate_heat_extraction(diameter, length, velocity)
% Calculate heat extraction rate (simplified)
    T_inlet = 313; % 40°C
    T_soil = 283;  % 10°C

    % Heat transfer coefficient
    Re = 1000 * velocity * diameter / 0.001;
    if Re > 2300
        Nu = 0.023 * Re^0.8 * 0.7^0.4;
    else
        Nu = 3.66;
    end
    h = Nu * 0.6 / diameter;

    % Heat transfer rate
    heat_rate = h * pi * diameter * length * (T_inlet - T_soil);
end

function efficiency = calculate_thermal_efficiency(diameter, length, velocity)
% Calculate thermal efficiency
    heat_extracted = calculate_heat_extraction(diameter, length, velocity);
    pumping_power = calculate_pressure_drop(diameter, length, velocity) * ...
        velocity * pi * (diameter/2)^2;
    efficiency = heat_extracted / (heat_extracted + pumping_power);
end

function dp = calculate_pressure_drop(diameter, length, velocity)
% Calculate pressure drop (Darcy-Weisbach equation)
    rho = 1000; % kg/m³
    mu = 0.001; % Pa·s

    Re = rho * velocity * diameter / mu;

    if Re > 2300
        f = 0.316 * Re^(-0.25); % Blasius correlation
    else
        f = 64 / Re; % Laminar flow
    end

    dp = f * (length / diameter) * 0.5 * rho * velocity^2;
end

function export_results()
% Export results to various formats

fprintf('Exporting results...\n');

if exist('geothermal_pile_results.mat', 'file')
    load('geothermal_pile_results.mat');

    % Export temperature data
    csvwrite('soil_temperature_final.csv', fields.T_soil(:,:,end));
    csvwrite('fluid_temperature_final.csv', fields.T_fluid(:,:,end));

    % Export displacement data
    csvwrite('radial_displacement_final.csv', fields.u_r(:,:,end));
    csvwrite('axial_displacement_final.csv', fields.u_z(:,:,end));

    % Export stress data
    csvwrite('radial_stress_final.csv', fields.sigma_rr(:,:,end));
    csvwrite('axial_stress_final.csv', fields.sigma_zz(:,:,end));

    % Create summary report
    create_summary_report(fields, grids);

    fprintf('Results exported successfully!\n');
else
    fprintf('No results file found. Run analysis first.\n');
end

end

function create_summary_report(fields, grids)
% Create a summary report

fid = fopen('geothermal_pile_report.txt', 'w');

fprintf(fid, '=== GEOTHERMAL PILE ANALYSIS REPORT ===\n');
fprintf(fid, 'Generated: %s\n\n', datestr(now));

fprintf(fid, 'GEOMETRY:\n');
fprintf(fid, '  Tube radius: %.3f m\n', grids.R_tube);
fprintf(fid, '  Tube length: %.1f m\n', grids.L_tube);
fprintf(fid, '  Soil domain radius: %.1f m\n', grids.R_soil);

fprintf(fid, '\nTHERMAL RESULTS:\n');
fprintf(fid, '  Max soil temperature: %.1f°C\n', max(fields.T_soil(:,:,end), [], 'all') - 273);
fprintf(fid, '  Min soil temperature: %.1f°C\n', min(fields.T_soil(:,:,end), [], 'all') - 273);
fprintf(fid, '  Max fluid temperature: %.1f°C\n', max(fields.T_fluid(:,:,end), [], 'all') - 273);

fprintf(fid, '\nMECHANICAL RESULTS:\n');
fprintf(fid, '  Max radial displacement: %.2f mm\n', max(fields.u_r(:,:,end), [], 'all') * 1000);
fprintf(fid, '  Max axial displacement: %.2f mm\n', max(fields.u_z(:,:,end), [], 'all') * 1000);
fprintf(fid, '  Max radial stress: %.1f kPa\n', max(fields.sigma_rr(:,:,end), [], 'all') / 1000);

fclose(fid);

end

function optimize_performance()
% Optimize geothermal pile performance

fprintf('Running performance optimization...\n');

% Define optimization problem
% Objective: Maximize heat extraction while minimizing cost
% Variables: tube diameter, flow rate, pile length
% Constraints: pressure drop, thermal stress

% Simplified optimization using grid search
diameters = linspace(0.05, 0.15, 10);
velocities = linspace(0.2, 1.0, 10);
lengths = linspace(15, 30, 5);

best_performance = 0;
optimal_design = struct();

for d = 1:length(diameters)
    for v = 1:length(velocities)
        for l = 1:length(lengths)
            % Calculate performance metrics
            heat_rate = calculate_heat_extraction(diameters(d), lengths(l), velocities(v));
            pressure_drop = calculate_pressure_drop(diameters(d), lengths(l), velocities(v));
            cost = diameters(d)^2 * lengths(l) * 1000; % Simplified cost

            % Performance index (heat rate per unit cost)
            performance = heat_rate / cost;

            % Check constraints
            if pressure_drop < 50000 && performance > best_performance % 50 kPa limit
                best_performance = performance;
                optimal_design.diameter = diameters(d);
                optimal_design.velocity = velocities(v);
                optimal_design.length = lengths(l);
                optimal_design.heat_rate = heat_rate;
                optimal_design.pressure_drop = pressure_drop;
                optimal_design.cost = cost;
            end
        end
    end
end

fprintf('\nOPTIMAL DESIGN:\n');
fprintf('  Tube diameter: %.3f m\n', optimal_design.diameter);
fprintf('  Flow velocity: %.2f m/s\n', optimal_design.velocity);
fprintf('  Pile length: %.1f m\n', optimal_design.length);
fprintf('  Heat extraction: %.1f kW\n', optimal_design.heat_rate / 1000);
fprintf('  Pressure drop: %.1f kPa\n', optimal_design.pressure_drop / 1000);
fprintf('  Estimated cost: $%.0f\n', optimal_design.cost);

end

function generate_animation()
% Generate animation of temperature evolution

fprintf('Generating animation...\n');

if exist('geothermal_pile_results.mat', 'file')
    load('geothermal_pile_results.mat');

    figure('Position', [100, 100, 1000, 600]);

    for t = 1:50:size(fields.T_soil, 3)
        subplot(1, 2, 1);
        contourf(grids.Z_soil, grids.R_soil, fields.T_soil(:,:,t) - 273, 15);
        colorbar;
        caxis([10, 40]);
        title(sprintf('Soil Temperature - Time: %.1f hours', (t-1)*60/3600));
        xlabel('Axial Position [m]');
        ylabel('Radial Position [m]');

        subplot(1, 2, 2);
        contourf(grids.Z_soil, grids.R_soil, fields.u_r(:,:,t) * 1000, 15);
        colorbar;
        title('Radial Displacement [mm]');
        xlabel('Axial Position [m]');
        ylabel('Radial Position [m]');

        pause(0.1);

        if t == 1
            fprintf('Animation started. Close figure to stop.\n');
        end
    end

    fprintf('Animation completed!\n');
else
    fprintf('No results file found. Run analysis first.\n');
end

end
