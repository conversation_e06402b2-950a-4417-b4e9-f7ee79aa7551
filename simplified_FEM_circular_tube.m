function simplified_FEM_circular_tube()
% Simplified FEM for Geothermal Pile with Circular Tube Mesh
% Focus: Robust implementation with proper circular tube representation
% Author: Generated for Geothermal Pile FEM Analysis
% Date: 2025-07-16

clear; clc; close all;

fprintf('=== Simplified FEM Geothermal Pile with Circular Tubes ===\n');

%% Problem Setup
domain = struct();
domain.Lx = 4.0; domain.Ly = 4.0; domain.Lz = 20.0;  % Domain size [m]
domain.tube_radius = 0.08;                            % Tube radius [m]
domain.tube_separation = 0.4;                         % Tube separation [m]

% Material properties
materials = get_materials();

% FEM parameters (simplified)
fem = struct();
fem.nx = 20; fem.ny = 20; fem.nz = 40;               % Grid resolution
fem.nt = 300; fem.dt = 300;                          % Time parameters

fprintf('Configuration:\n');
fprintf('  Domain: %.1f × %.1f × %.1f m\n', domain.Lx, domain.Ly, domain.Lz);
fprintf('  Grid: %d × %d × %d nodes\n', fem.nx, fem.ny, fem.nz);
fprintf('  Time: %d steps × %.0f s = %.1f hours\n', fem.nt, fem.dt, fem.nt*fem.dt/3600);

%% Generate Simplified FEM Mesh
fprintf('Generating simplified FEM mesh...\n');
[mesh] = generate_simplified_mesh(domain, fem);

%% Initialize FEM System
fprintf('Initializing FEM system...\n');
[K_thermal, M_thermal, K_mechanical] = assemble_simplified_matrices(mesh, materials);

%% Set Boundary Conditions
fprintf('Setting boundary conditions...\n');
[bc] = set_simplified_boundary_conditions(mesh, materials, domain);

%% Initialize Solution
fprintf('Initializing solution...\n');
[T, u] = initialize_solution(mesh, materials, bc);

%% Main FEM Time Loop
fprintf('Starting FEM time integration...\n');
results = solve_simplified_FEM(mesh, K_thermal, M_thermal, K_mechanical, bc, T, u, materials, fem);

%% Visualization
fprintf('Generating results...\n');
visualize_simplified_results(mesh, results, materials, fem);

% Save results
save('simplified_FEM_results.mat', 'mesh', 'results', 'materials', 'fem', '-v7.3');
fprintf('Analysis completed successfully!\n');

end

function materials = get_materials()
% Material properties
    materials.fluid = struct();
    materials.fluid.rho = 1030; materials.fluid.cp = 3900; materials.fluid.k = 0.5;
    materials.fluid.v = 0.8; materials.fluid.T_inlet = 318; % 45°C
    materials.fluid.alpha = materials.fluid.k / (materials.fluid.rho * materials.fluid.cp);
    
    materials.soil = struct();
    materials.soil.rho = 1800; materials.soil.cp = 1000; materials.soil.k = 2.2;
    materials.soil.E = 50e6; materials.soil.nu = 0.3; materials.soil.alpha_T = 1e-5;
    materials.soil.T_initial = 283; % 10°C
    materials.soil.alpha = materials.soil.k / (materials.soil.rho * materials.soil.cp);
    
    fprintf('Materials initialized.\n');
end

function [mesh] = generate_simplified_mesh(domain, fem)
% Generate simplified structured mesh with circular tube identification
    
    % Create structured grid
    mesh.x = linspace(0, domain.Lx, fem.nx);
    mesh.y = linspace(0, domain.Ly, fem.ny);
    mesh.z = linspace(0, domain.Lz, fem.nz);
    mesh.dx = domain.Lx / (fem.nx - 1);
    mesh.dy = domain.Ly / (fem.ny - 1);
    mesh.dz = domain.Lz / (fem.nz - 1);
    
    % Total nodes and elements
    mesh.n_nodes = fem.nx * fem.ny * fem.nz;
    mesh.n_elements = (fem.nx-1) * (fem.ny-1) * (fem.nz-1);
    
    % Create node coordinates
    mesh.nodes = zeros(mesh.n_nodes, 3);
    node_id = 1;
    for k = 1:fem.nz
        for j = 1:fem.ny
            for i = 1:fem.nx
                mesh.nodes(node_id, :) = [mesh.x(i), mesh.y(j), mesh.z(k)];
                node_id = node_id + 1;
            end
        end
    end
    
    % Create hexahedral elements
    mesh.elements = zeros(mesh.n_elements, 8);
    elem_id = 1;
    for k = 1:fem.nz-1
        for j = 1:fem.ny-1
            for i = 1:fem.nx-1
                % Node indices for hexahedral element
                n1 = (k-1)*fem.nx*fem.ny + (j-1)*fem.nx + i;
                n2 = n1 + 1;
                n3 = n1 + fem.nx + 1;
                n4 = n1 + fem.nx;
                n5 = n1 + fem.nx*fem.ny;
                n6 = n5 + 1;
                n7 = n5 + fem.nx + 1;
                n8 = n5 + fem.nx;
                
                mesh.elements(elem_id, :) = [n1, n2, n3, n4, n5, n6, n7, n8];
                elem_id = elem_id + 1;
            end
        end
    end
    
    % Identify circular tube regions
    mesh = identify_circular_tubes(mesh, domain, fem);
    
    fprintf('  Mesh: %d nodes, %d elements\n', mesh.n_nodes, mesh.n_elements);
    fprintf('  Tube nodes: %d (%.1f%%)\n', sum(mesh.tube_mask), 100*sum(mesh.tube_mask)/mesh.n_nodes);
end

function mesh = identify_circular_tubes(mesh, domain, fem)
% Identify nodes and elements in circular tubes

    % Initialize masks
    mesh.tube_mask = false(mesh.n_nodes, 1);
    mesh.inlet_mask = false(mesh.n_nodes, 1);
    mesh.outlet_mask = false(mesh.n_nodes, 1);

    % Tube centers (corrected positioning)
    inlet_x = domain.Lx/2 - domain.tube_separation/2;
    outlet_x = domain.Lx/2 + domain.tube_separation/2;
    tube_y = domain.Ly/2;

    fprintf('  Tube centers: Inlet (%.2f, %.2f), Outlet (%.2f, %.2f)\n', ...
        inlet_x, tube_y, outlet_x, tube_y);
    fprintf('  Tube radius: %.3f m\n', domain.tube_radius);

    % Check each node
    inlet_count = 0;
    outlet_count = 0;

    for i = 1:mesh.n_nodes
        x = mesh.nodes(i, 1);
        y = mesh.nodes(i, 2);
        z = mesh.nodes(i, 3);

        % Distance to tube centers
        dist_inlet = sqrt((x - inlet_x)^2 + (y - tube_y)^2);
        dist_outlet = sqrt((x - outlet_x)^2 + (y - tube_y)^2);

        % Check if inside tubes (with slightly larger radius for better capture)
        tube_radius_effective = domain.tube_radius * 1.2;  % 20% larger for better mesh capture

        if dist_inlet <= tube_radius_effective
            mesh.tube_mask(i) = true;
            mesh.inlet_mask(i) = true;
            inlet_count = inlet_count + 1;
        elseif dist_outlet <= tube_radius_effective
            mesh.tube_mask(i) = true;
            mesh.outlet_mask(i) = true;
            outlet_count = outlet_count + 1;
        end
    end

    fprintf('  Nodes identified: %d inlet, %d outlet\n', inlet_count, outlet_count);

    % Identify tube elements
    mesh.tube_elements = [];
    for e = 1:size(mesh.elements, 1)
        element_nodes = mesh.elements(e, :);
        if any(mesh.tube_mask(element_nodes))
            mesh.tube_elements = [mesh.tube_elements; e];
        end
    end

    fprintf('  Circular tubes identified: %d tube elements\n', length(mesh.tube_elements));

    % If no tubes found, create some manually for demonstration
    if sum(mesh.tube_mask) == 0
        fprintf('  Warning: No tubes found automatically, creating manual tubes...\n');
        mesh = create_manual_tubes(mesh, domain, fem);
    end
end

function mesh = create_manual_tubes(mesh, domain, fem)
% Create manual tube regions if automatic detection fails

    % Find nodes near tube centers
    inlet_x = domain.Lx/2 - domain.tube_separation/2;
    outlet_x = domain.Lx/2 + domain.tube_separation/2;
    tube_y = domain.Ly/2;

    % Use grid-based approach
    inlet_i = round(inlet_x / mesh.dx) + 1;
    outlet_i = round(outlet_x / mesh.dx) + 1;
    tube_j = round(tube_y / mesh.dy) + 1;

    % Create circular regions in grid indices
    radius_cells = ceil(domain.tube_radius / mesh.dx);

    for di = -radius_cells:radius_cells
        for dj = -radius_cells:radius_cells
            if di^2 + dj^2 <= radius_cells^2
                % Inlet tube
                i_inlet = inlet_i + di;
                j_inlet = tube_j + dj;
                if i_inlet >= 1 && i_inlet <= fem.nx && j_inlet >= 1 && j_inlet <= fem.ny
                    for k = 1:fem.nz
                        node_id = (k-1)*fem.nx*fem.ny + (j_inlet-1)*fem.nx + i_inlet;
                        if node_id <= mesh.n_nodes
                            mesh.tube_mask(node_id) = true;
                            mesh.inlet_mask(node_id) = true;
                        end
                    end
                end

                % Outlet tube
                i_outlet = outlet_i + di;
                j_outlet = tube_j + dj;
                if i_outlet >= 1 && i_outlet <= fem.nx && j_outlet >= 1 && j_outlet <= fem.ny
                    for k = 1:fem.nz
                        node_id = (k-1)*fem.nx*fem.ny + (j_outlet-1)*fem.nx + i_outlet;
                        if node_id <= mesh.n_nodes
                            mesh.tube_mask(node_id) = true;
                            mesh.outlet_mask(node_id) = true;
                        end
                    end
                end
            end
        end
    end

    % Update tube elements
    mesh.tube_elements = [];
    for e = 1:size(mesh.elements, 1)
        element_nodes = mesh.elements(e, :);
        if any(mesh.tube_mask(element_nodes))
            mesh.tube_elements = [mesh.tube_elements; e];
        end
    end

    fprintf('  Manual tubes created: %d inlet nodes, %d outlet nodes, %d elements\n', ...
        sum(mesh.inlet_mask), sum(mesh.outlet_mask), length(mesh.tube_elements));
end

function [K_thermal, M_thermal, K_mechanical] = assemble_simplified_matrices(mesh, materials)
% Assemble simplified FEM matrices
    
    n = mesh.n_nodes;
    K_thermal = sparse(n, n);
    M_thermal = sparse(n, n);
    K_mechanical = sparse(3*n, 3*n);
    
    % Simple element-by-element assembly
    for e = 1:size(mesh.elements, 1)
        element_nodes = mesh.elements(e, :);
        
        % Determine material properties
        if any(mesh.tube_mask(element_nodes))
            % Tube element - use fluid properties
            k = materials.fluid.k;
            rho = materials.fluid.rho;
            cp = materials.fluid.cp;
        else
            % Soil element
            k = materials.soil.k;
            rho = materials.soil.rho;
            cp = materials.soil.cp;
        end
        
        % Element volume (simplified)
        Ve = mesh.dx * mesh.dy * mesh.dz;
        
        % Simple element matrices (lumped)
        Ke_thermal = k * eye(8) / Ve;  % Simplified thermal stiffness
        Me_thermal = rho * cp * Ve * eye(8) / 8;  % Lumped mass matrix
        
        % Assemble thermal matrices
        K_thermal(element_nodes, element_nodes) = K_thermal(element_nodes, element_nodes) + Ke_thermal;
        M_thermal(element_nodes, element_nodes) = M_thermal(element_nodes, element_nodes) + Me_thermal;
    end
    
    % Simple mechanical matrix (identity-based)
    E = materials.soil.E;
    K_mechanical = E * speye(3*n) * 1e-6;  % Simplified mechanical stiffness
    
    fprintf('  Matrices assembled: %d thermal DOF, %d mechanical DOF\n', n, 3*n);
end

function [bc] = set_simplified_boundary_conditions(mesh, materials, domain)
% Set simplified boundary conditions
    
    bc = struct();
    
    % Find boundary nodes
    tol = 1e-6;
    bc.surface_nodes = find(abs(mesh.nodes(:,3)) < tol);
    bc.bottom_nodes = find(abs(mesh.nodes(:,3) - domain.Lz) < tol);
    bc.inlet_nodes = find(mesh.inlet_mask);
    
    % Boundary values
    bc.surface_temp = materials.soil.T_initial;
    bc.bottom_temp = materials.soil.T_initial + 0.025 * domain.Lz;
    bc.inlet_temp = materials.fluid.T_inlet;
    
    fprintf('  Boundary conditions: %d surface, %d bottom, %d inlet nodes\n', ...
        length(bc.surface_nodes), length(bc.bottom_nodes), length(bc.inlet_nodes));
end

function [T, u] = initialize_solution(mesh, materials, bc)
% Initialize solution vectors
    
    n = mesh.n_nodes;
    
    % Temperature field
    T = materials.soil.T_initial * ones(n, 1);
    
    % Apply geothermal gradient
    for i = 1:n
        depth = mesh.nodes(i, 3);
        T(i) = materials.soil.T_initial + 0.025 * depth;
    end
    
    % Set inlet temperature
    T(bc.inlet_nodes) = bc.inlet_temp;
    
    % Displacement field
    u = zeros(3*n, 1);
    
    fprintf('  Solution initialized: T range %.1f-%.1f°C\n', min(T)-273, max(T)-273);
end

function results = solve_simplified_FEM(mesh, K_thermal, M_thermal, K_mechanical, bc, T, u, materials, fem)
% Simplified FEM time integration with stability

    n = mesh.n_nodes;
    dt = fem.dt;

    % Storage for results
    results = struct();
    results.T = zeros(n, fem.nt);
    results.u = zeros(3*n, fem.nt);
    results.time = (1:fem.nt) * dt;

    % Time integration parameters
    theta = 0.5;  % Crank-Nicolson

    % Thermal system matrix (with regularization)
    A_thermal = M_thermal/dt + theta*K_thermal + 1e-6*speye(n);

    % Add convection effects for tube nodes
    A_thermal = add_convection_effects(A_thermal, mesh, materials);

    % Mechanical system (simplified)
    A_mechanical = K_mechanical + 1e-6*speye(3*n);

    fprintf('  Time integration started...\n');
    tic;

    for t = 1:fem.nt
        % Thermal step
        T_old = T;

        % Right-hand side
        rhs_thermal = (M_thermal/dt - (1-theta)*K_thermal) * T_old;

        % Apply boundary conditions
        T = apply_thermal_BC(A_thermal, rhs_thermal, T, bc, materials);

        % Mechanical step (simplified)
        thermal_load = compute_thermal_load(T, T_old, materials, mesh);
        u = A_mechanical \ thermal_load;

        % Store results
        results.T(:, t) = T;
        results.u(:, t) = u;

        % Progress
        if mod(t, 50) == 0 || t == fem.nt
            elapsed = toc;
            remaining = elapsed * (fem.nt - t) / t;
            fprintf('    Step %d/%d (%.1f%%) - Elapsed: %.1fs, Remaining: %.1fs\n', ...
                t, fem.nt, 100*t/fem.nt, elapsed, remaining);
            fprintf('      Temperature range: %.1f-%.1f°C\n', min(T)-273, max(T)-273);
        end
    end

    fprintf('  Time integration completed in %.1f seconds\n', toc);
end

function A_thermal = add_convection_effects(A_thermal, mesh, materials)
% Add simplified convection effects to thermal matrix

    % Convection parameter
    Pe = materials.fluid.v * mesh.dx / materials.fluid.alpha;  % Peclet number
    conv_factor = min(Pe * 0.1, 1.0);  % Limit convection effect

    % Add convection to tube nodes
    tube_nodes = find(mesh.tube_mask);
    for i = 1:length(tube_nodes)
        node = tube_nodes(i);
        A_thermal(node, node) = A_thermal(node, node) * (1 + conv_factor);
    end
end

function T = apply_thermal_BC(A, rhs, T, bc, materials)
% Apply thermal boundary conditions

    % Surface temperature
    for i = 1:length(bc.surface_nodes)
        node = bc.surface_nodes(i);
        A(node, :) = 0;
        A(node, node) = 1;
        rhs(node) = bc.surface_temp;
    end

    % Bottom temperature
    for i = 1:length(bc.bottom_nodes)
        node = bc.bottom_nodes(i);
        A(node, :) = 0;
        A(node, node) = 1;
        rhs(node) = bc.bottom_temp;
    end

    % Inlet temperature
    for i = 1:length(bc.inlet_nodes)
        node = bc.inlet_nodes(i);
        A(node, :) = 0;
        A(node, node) = 1;
        rhs(node) = bc.inlet_temp;
    end

    % Solve system
    T = A \ rhs;
end

function thermal_load = compute_thermal_load(T, T_old, materials, mesh)
% Compute thermal loading for mechanical analysis

    n = mesh.n_nodes;
    thermal_load = zeros(3*n, 1);

    % Thermal expansion coefficient
    alpha_T = materials.soil.alpha_T;
    E = materials.soil.E;

    % Simple thermal loading
    dT = T - T_old;
    for i = 1:n
        thermal_load(3*i-2:3*i) = -alpha_T * E * dT(i) * [1; 1; 1];
    end
end

function visualize_simplified_results(mesh, results, materials, fem)
% Comprehensive visualization of simplified FEM results

    fprintf('  Generating visualization...\n');

    figure('Position', [100, 100, 1400, 1000], 'Name', 'Simplified FEM Circular Tube Analysis');

    % Final temperature field
    T_final = results.T(:, end) - 273;  % Convert to Celsius
    time_hours = results.time / 3600;

    % Reshape for visualization
    T_grid = reshape(T_final, [fem.nx, fem.ny, fem.nz]);

    % 1. Temperature distribution at mid-depth
    subplot(2,3,1);
    mid_z = round(fem.nz/2);
    [X, Y] = meshgrid(mesh.x, mesh.y);
    contourf(X, Y, T_grid(:,:,mid_z)', 20, 'LineStyle', 'none');
    colorbar; title('Temperature at Mid-Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');

    % Mark tube positions
    hold on;
    inlet_x = 2.0 - 0.2; outlet_x = 2.0 + 0.2; tube_y = 2.0;
    theta = linspace(0, 2*pi, 50);
    inlet_circle_x = inlet_x + 0.08 * cos(theta);
    inlet_circle_y = tube_y + 0.08 * sin(theta);
    outlet_circle_x = outlet_x + 0.08 * cos(theta);
    outlet_circle_y = tube_y + 0.08 * sin(theta);
    plot(inlet_circle_x, inlet_circle_y, 'r-', 'LineWidth', 2);
    plot(outlet_circle_x, outlet_circle_y, 'b-', 'LineWidth', 2);

    % 2. Temperature evolution
    subplot(2,3,2);
    inlet_nodes = find(mesh.inlet_mask);
    outlet_nodes = find(mesh.outlet_mask);

    if ~isempty(inlet_nodes) && ~isempty(outlet_nodes)
        T_inlet_avg = mean(results.T(inlet_nodes, :), 1) - 273;
        T_outlet_avg = mean(results.T(outlet_nodes, :), 1) - 273;

        plot(time_hours, T_inlet_avg, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet');
        hold on;
        plot(time_hours, T_outlet_avg, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet');
        xlabel('Time [hours]'); ylabel('Temperature [°C]');
        title('Average Tube Temperature Evolution');
        legend('Location', 'best');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    end

    % 3. Heat extraction efficiency
    subplot(2,3,3);
    if exist('T_inlet_avg', 'var') && exist('T_outlet_avg', 'var')
        heat_extraction = T_inlet_avg - T_outlet_avg;
        plot(time_hours, heat_extraction, 'g-', 'LineWidth', 2);
        xlabel('Time [hours]'); ylabel('ΔT [°C]');
        title('Heat Extraction (Inlet - Outlet)');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    end

    % 4. 3D temperature distribution
    subplot(2,3,4);
    tube_nodes = find(mesh.tube_mask);
    scatter3(mesh.nodes(tube_nodes,1), mesh.nodes(tube_nodes,2), mesh.nodes(tube_nodes,3), ...
        20, T_final(tube_nodes), 'filled');
    colorbar; title('3D Tube Temperature (°C)');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 5. Temperature along depth
    subplot(2,3,5);
    if ~isempty(inlet_nodes) && ~isempty(outlet_nodes)
        % Average temperature at each depth
        T_depth_inlet = zeros(fem.nz, 1);
        T_depth_outlet = zeros(fem.nz, 1);

        for k = 1:fem.nz
            depth_nodes_inlet = inlet_nodes(abs(mesh.nodes(inlet_nodes,3) - mesh.z(k)) < mesh.dz/2);
            depth_nodes_outlet = outlet_nodes(abs(mesh.nodes(outlet_nodes,3) - mesh.z(k)) < mesh.dz/2);

            if ~isempty(depth_nodes_inlet)
                T_depth_inlet(k) = mean(T_final(depth_nodes_inlet));
            end
            if ~isempty(depth_nodes_outlet)
                T_depth_outlet(k) = mean(T_final(depth_nodes_outlet));
            end
        end

        plot(mesh.z, T_depth_inlet, 'r-', 'LineWidth', 2, 'DisplayName', 'Inlet');
        hold on;
        plot(mesh.z, T_depth_outlet, 'b-', 'LineWidth', 2, 'DisplayName', 'Outlet');
        xlabel('Depth [m]'); ylabel('Temperature [°C]');
        title('Temperature Along Depth');
        legend('Location', 'best');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    end

    % 6. Performance summary
    subplot(2,3,6);
    axis off;

    % Calculate metrics
    max_temp = max(T_final);
    min_temp = min(T_final);
    n_tube_nodes = sum(mesh.tube_mask);
    tube_volume = n_tube_nodes * mesh.dx * mesh.dy * mesh.dz;

    if exist('heat_extraction', 'var')
        avg_heat_extraction = mean(heat_extraction(end-10:end));
    else
        avg_heat_extraction = 0;
    end

    % Display summary
    text(0.1, 0.9, 'SIMPLIFIED FEM PERFORMANCE', 'FontSize', 12, 'FontWeight', 'bold');
    text(0.1, 0.8, sprintf('Grid: %d × %d × %d', fem.nx, fem.ny, fem.nz), 'FontSize', 10);
    text(0.1, 0.7, sprintf('Tube Nodes: %d', n_tube_nodes), 'FontSize', 10);
    text(0.1, 0.6, sprintf('Tube Volume: %.3f m³', tube_volume), 'FontSize', 10);
    text(0.1, 0.5, sprintf('Max Temperature: %.1f°C', max_temp), 'FontSize', 10);
    text(0.1, 0.4, sprintf('Min Temperature: %.1f°C', min_temp), 'FontSize', 10);
    text(0.1, 0.3, sprintf('Avg Heat Extraction: %.1f°C', avg_heat_extraction), 'FontSize', 10);
    text(0.1, 0.2, sprintf('Simulation Time: %.1f hours', time_hours(end)), 'FontSize', 10);
    text(0.1, 0.1, sprintf('Elements: %d', mesh.n_elements), 'FontSize', 10);

    sgtitle('Simplified FEM Geothermal Pile Analysis Results', 'FontSize', 14, 'FontWeight', 'bold');

    % Save visualization
    saveas(gcf, 'simplified_FEM_analysis.png');
    fprintf('  Visualization completed and saved!\n');
end
