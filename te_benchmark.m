% =========================================================================
% 2D AXIAL-SYMMETRIC THERMO-ELASTIC PROBLEM: CONSTANT CIRCULAR HEAT SOURCE
% =========================================================================
% This script computes and plots the analytical solution for the
% temperature, displacement, and stress fields in an infinite elastic
% medium subjected to a constant circular heat source on the z=0 plane.
%
% The solution is based on Hankel transform methods.
%
% OPTIMIZATION: This version uses a fully vectorized integration method
% (trapezoidal rule) to replace slow, looped calls to 'integral',
% resulting in a massive speed improvement.
% -------------------------------------------------------------------------

clear; clc; close all;

%% 1. DEFINE PHYSICAL AND MATERIAL PARAMETERS
% -------------------------------------------------------------------------
a = 1.0;          % Radius of the circular heat source [m]
q0 = 10000;       % Heat flux density [W/m^2]
k = 50.0;         % Thermal conductivity [W/(m*K)]

E = 200e9;        % Young's modulus [Pa]
nu = 0.3;         % Poisson's ratio
alpha = 12e-6;    % Coefficient of thermal expansion [1/K]

% --- Derived Parameters ---
G = E / (2 * (1 + nu)); % Shear modulus
m = alpha * (1 + nu) / (1 - nu);

%% 2. CREATE COMPUTATIONAL GRID
% -------------------------------------------------------------------------
rmax = 4 * a;
zmax = 4 * a;
% NOTE: Grid size can be increased (e.g., to 100x100) due to the new, faster method.
nr = 50;
nz = 50;

r_vec = linspace(0.01, rmax, nr); % Start r from a small value to avoid singularity at r=0
z_vec = linspace(0.01, zmax, nz); % Start z from a small value for stability
[R, Z] = meshgrid(r_vec, z_vec);

%% 3. COMPUTE THE FIELDS (VECTORIZED NUMERICAL INTEGRATION)
% -------------------------------------------------------------------------
fprintf('Computation started (using vectorized approach)... This may take a moment.\n');
tic;

% --- Optimization: Vectorize the integration to avoid looping over grid points ---
% We will manually integrate using the trapezoidal rule over a defined xi vector.
% This is much faster than calling 'integral' for every point.
n_xi = 4000;      % Number of points for numerical integration. More points = higher accuracy.
xi_max = 40/a;    % Integrate up to where the integrands decay sufficiently.
xi_vec = linspace(1e-6, xi_max, n_xi); % Integration variable vector (start near zero)

% Initialize 3D arrays to hold integrand values at each xi
integrand_T_vals  = zeros(nz, nr, n_xi);
integrand_ur_vals = zeros(nz, nr, n_xi);
integrand_uz_vals = zeros(nz, nr, n_xi);
integrand_I1_vals = zeros(nz, nr, n_xi); % For dur_dr
integrand_I2_vals = zeros(nz, nr, n_xi); % For dur_dr and duz_dz
integrand_rz_vals = zeros(nz, nr, n_xi); % For sigma_rz

% --- UX Improvement: Add a waitbar for progress feedback ---
h_wait = waitbar(0, 'Vectorized computation... Please wait.');

% Loop over the integration variable xi
for idx = 1:n_xi
    xi = xi_vec(idx);
    
    % Update waitbar periodically to reduce overhead
    if mod(idx, 50) == 0
        waitbar(idx / n_xi, h_wait, sprintf('Integrating... %.0f%%', (idx/n_xi)*100));
    end
    
    % Pre-calculate common terms for this xi value (vectorized over R and Z)
    exp_term = exp(-xi .* Z);
    bessel0_r = besselj(0, xi .* R);
    bessel1_r = besselj(1, xi .* R);
    bessel1_a = besselj(1, xi * a); % This is a scalar
    
    % Calculate the value of each integrand across the entire R-Z grid
    integrand_T_vals(:,:,idx)  = (1/xi)      .* exp_term .* bessel0_r .* bessel1_a;
    integrand_ur_vals(:,:,idx) = (1/xi^2)    .* exp_term .* bessel1_r .* bessel1_a;
    integrand_uz_vals(:,:,idx) = (1/xi^2)    .* exp_term .* bessel0_r .* bessel1_a;
    integrand_I1_vals(:,:,idx) = (1/xi)      .* exp_term .* bessel1_r .* bessel1_a ./ R;
    integrand_I2_vals(:,:,idx) =               exp_term .* bessel0_r .* bessel1_a;
    integrand_rz_vals(:,:,idx) = (1/xi)      .* exp_term .* bessel1_r .* bessel1_a;
end

close(h_wait); % Close the waitbar

% --- Perform the integration using the trapezoidal rule along the 3rd dimension ---
T_integral   = trapz(xi_vec, integrand_T_vals, 3);
ur_integral  = trapz(xi_vec, integrand_ur_vals, 3);
uz_integral  = trapz(xi_vec, integrand_uz_vals, 3);
I1_integral  = trapz(xi_vec, integrand_I1_vals, 3);
I2_integral  = trapz(xi_vec, integrand_I2_vals, 3);
rz_integral  = trapz(xi_vec, integrand_rz_vals, 3);

% --- Calculate final physical quantities ---
T   = (q0 * a / k) * T_integral;
u_r = (m * q0 * a / k) * ur_integral;
u_z = (m * q0 * a / k) * uz_integral;

% Calculate strain components from the integrated values
I1 = (m * q0 * a / k) * I1_integral;
I2 = (m * q0 * a / k) * I2_integral;
dur_dr = I2 - I1;
duz_dz = -I2;
ur_r   = u_r ./ R; % Element-wise division for the grid

% Calculate shear strain term (dur_dz and duz_dr are identical)
dudz_term = -(m * q0 * a / k) * rz_integral;

% Calculate stress components
stress_thermal_term = 2 * G * (1+nu) * alpha .* T;
sigma_rr = 2 * G .* dur_dr - stress_thermal_term;
sigma_zz = 2 * G .* duz_dz - stress_thermal_term;
sigma_tt = 2 * G .* ur_r   - stress_thermal_term;
sigma_rz = G .* (dudz_term + dudz_term);

fprintf('Computation finished in %.2f seconds.\n', toc);


%% 4. PLOT THE RESULTS
% -------------------------------------------------------------------------

% --- Plot Temperature Distribution ---
figure('Name','Temperature Distribution','NumberTitle','off', 'Position', [100, 100, 600, 500]);
pcolor(R, Z, T);
shading interp;
axis equal tight;
title('Temperature Field T(r,z)');
xlabel('Radial Coordinate, r [m]');
ylabel('Axial Coordinate, z [m]');
c = colorbar;
ylabel(c, 'Temperature Rise [K]');
set(gca, 'FontSize', 12);

% --- Plot Displacement Field ---
figure('Name','Displacement Field','NumberTitle','off', 'Position', [750, 100, 600, 500]);
% Plot a subset of vectors to keep the plot clean
skip = 5;
R_s = R(1:skip:end, 1:skip:end);
Z_s = Z(1:skip:end, 1:skip:end);
u_r_s = u_r(1:skip:end, 1:skip:end);
u_z_s = u_z(1:skip:end, 1:skip:end);
quiver(R_s, Z_s, u_r_s, u_z_s, 1.5, 'b'); % The '1.5' scales the arrows
axis equal tight;
xlim([0 rmax]);
ylim([0 zmax]);
title('Displacement Vector Field (u_r, u_z)');
xlabel('Radial Coordinate, r [m]');
ylabel('Axial Coordinate, z [m]');
set(gca, 'FontSize', 12);

% --- Plot Stress Components ---
stress_plots = {sigma_rr, sigma_zz, sigma_tt, sigma_rz};
stress_titles = {'Radial Stress \sigma_{rr}', 'Axial Stress \sigma_{zz}',...
                 'Hoop Stress \sigma_{\theta\theta}', 'Shear Stress \sigma_{rz}'};

for k = 1:length(stress_plots)
    figure('Name', stress_titles{k}, 'NumberTitle', 'off', 'Position', [100 + 50*k, 100 + 50*k, 600, 500]);
    pcolor(R, Z, stress_plots{k} / 1e6); % Plot in MPa
    shading interp;
    axis equal tight;
    title(stress_titles{k});
    xlabel('Radial Coordinate, r [m]');
    ylabel('Axial Coordinate, z [m]');
    c = colorbar;
    ylabel(c, 'Stress [MPa]');
    set(gca, 'FontSize', 12);
end
