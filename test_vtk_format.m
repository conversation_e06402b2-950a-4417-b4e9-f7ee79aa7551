function test_vtk_format()
% Test VTK format generation with simple data

clear; clc;

fprintf('Testing VTK format generation...\n');

% Create simple test data
n_nodes = 8;
n_elements = 1;

% Simple cube nodes
nodes = [
    0, 0, 0;
    1, 0, 0;
    1, 1, 0;
    0, 1, 0;
    0, 0, 1;
    1, 0, 1;
    1, 1, 1;
    0, 1, 1
];

% Single hexahedral element
elements = [1, 2, 3, 4, 5, 6, 7, 8];

% Test data
temperature = [10, 15, 20, 25, 30, 35, 40, 45];  % Celsius
displacement_x = [0, 0.1, 0.2, 0.1, 0, 0.1, 0.2, 0.1] * 0.001;  % m
displacement_y = [0, 0, 0.1, 0.1, 0, 0, 0.1, 0.1] * 0.001;  % m
displacement_z = [0, 0, 0, 0, 0.05, 0.05, 0.05, 0.05] * 0.001;  % m

% Write test VTK file
write_test_vtk('test_simple.vtk', nodes, elements, temperature, displacement_x, displacement_y, displacement_z);

% Write test VTU file
write_test_vtu('test_simple.vtu', nodes, elements, temperature, displacement_x, displacement_y, displacement_z);

fprintf('Test files created:\n');
fprintf('  test_simple.vtk (legacy format)\n');
fprintf('  test_simple.vtu (XML format)\n');
fprintf('Try opening these in ParaView to verify format.\n');

end

function write_test_vtk(filename, nodes, elements, temperature, u_x, u_y, u_z)
% Write simple test VTK file

    fid = fopen(filename, 'w');
    if fid == -1
        error('Could not open file %s', filename);
    end
    
    n_nodes = size(nodes, 1);
    n_elements = size(elements, 1);
    
    try
        % VTK header
        fprintf(fid, '# vtk DataFile Version 3.0\n');
        fprintf(fid, 'Test VTK file\n');
        fprintf(fid, 'ASCII\n');
        fprintf(fid, 'DATASET UNSTRUCTURED_GRID\n');
        fprintf(fid, '\n');
        
        % Points
        fprintf(fid, 'POINTS %d float\n', n_nodes);
        for i = 1:n_nodes
            fprintf(fid, '%.6f %.6f %.6f\n', nodes(i,1), nodes(i,2), nodes(i,3));
        end
        fprintf(fid, '\n');
        
        % Cells
        fprintf(fid, 'CELLS %d %d\n', n_elements, n_elements * 9);
        for i = 1:n_elements
            element_nodes = elements(i, :) - 1;  % 0-based indexing
            fprintf(fid, '8 %d %d %d %d %d %d %d %d\n', element_nodes);
        end
        fprintf(fid, '\n');
        
        % Cell types
        fprintf(fid, 'CELL_TYPES %d\n', n_elements);
        for i = 1:n_elements
            fprintf(fid, '12\n');  % Hexahedral
        end
        fprintf(fid, '\n');
        
        % Point data
        fprintf(fid, 'POINT_DATA %d\n', n_nodes);
        
        % Temperature
        fprintf(fid, 'SCALARS Temperature float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for i = 1:n_nodes
            fprintf(fid, '%.6f\n', temperature(i));
        end
        fprintf(fid, '\n');
        
        % Displacement
        fprintf(fid, 'VECTORS Displacement float\n');
        for i = 1:n_nodes
            fprintf(fid, '%.6f %.6f %.6f\n', u_x(i), u_y(i), u_z(i));
        end
        
    catch ME
        fclose(fid);
        rethrow(ME);
    end
    
    fclose(fid);
end

function write_test_vtu(filename, nodes, elements, temperature, u_x, u_y, u_z)
% Write simple test VTU file

    fid = fopen(filename, 'w');
    if fid == -1
        error('Could not open file %s', filename);
    end
    
    n_nodes = size(nodes, 1);
    n_elements = size(elements, 1);
    
    try
        % VTU header
        fprintf(fid, '<?xml version="1.0"?>\n');
        fprintf(fid, '<VTKFile type="UnstructuredGrid" version="0.1" byte_order="LittleEndian">\n');
        fprintf(fid, '  <UnstructuredGrid>\n');
        fprintf(fid, '    <Piece NumberOfPoints="%d" NumberOfCells="%d">\n', n_nodes, n_elements);
        
        % Points
        fprintf(fid, '      <Points>\n');
        fprintf(fid, '        <DataArray type="Float32" NumberOfComponents="3" format="ascii">\n');
        for i = 1:n_nodes
            fprintf(fid, '          %.6f %.6f %.6f\n', nodes(i,1), nodes(i,2), nodes(i,3));
        end
        fprintf(fid, '        </DataArray>\n');
        fprintf(fid, '      </Points>\n');
        
        % Cells
        fprintf(fid, '      <Cells>\n');
        fprintf(fid, '        <DataArray type="Int32" Name="connectivity" format="ascii">\n');
        for i = 1:n_elements
            element_nodes = elements(i, :) - 1;  % 0-based indexing
            fprintf(fid, '          %d %d %d %d %d %d %d %d\n', element_nodes);
        end
        fprintf(fid, '        </DataArray>\n');
        
        fprintf(fid, '        <DataArray type="Int32" Name="offsets" format="ascii">\n');
        for i = 1:n_elements
            fprintf(fid, '          %d\n', i * 8);
        end
        fprintf(fid, '        </DataArray>\n');
        
        fprintf(fid, '        <DataArray type="UInt8" Name="types" format="ascii">\n');
        for i = 1:n_elements
            fprintf(fid, '          12\n');  % Hexahedral
        end
        fprintf(fid, '        </DataArray>\n');
        fprintf(fid, '      </Cells>\n');
        
        % Point data
        fprintf(fid, '      <PointData Scalars="Temperature" Vectors="Displacement">\n');
        
        % Temperature
        fprintf(fid, '        <DataArray type="Float32" Name="Temperature" format="ascii">\n');
        for i = 1:n_nodes
            fprintf(fid, '          %.6f\n', temperature(i));
        end
        fprintf(fid, '        </DataArray>\n');
        
        % Displacement
        fprintf(fid, '        <DataArray type="Float32" Name="Displacement" NumberOfComponents="3" format="ascii">\n');
        for i = 1:n_nodes
            fprintf(fid, '          %.6f %.6f %.6f\n', u_x(i), u_y(i), u_z(i));
        end
        fprintf(fid, '        </DataArray>\n');
        
        fprintf(fid, '      </PointData>\n');
        fprintf(fid, '    </Piece>\n');
        fprintf(fid, '  </UnstructuredGrid>\n');
        fprintf(fid, '</VTKFile>\n');
        
    catch ME
        fclose(fid);
        rethrow(ME);
    end
    
    fclose(fid);
end
