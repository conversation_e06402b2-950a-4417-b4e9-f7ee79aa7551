function thermoelastic_FEM_circular_hole()
% FEM Thermoelastic Analysis of Medium with Circular Hole
% ======================================================
% 
% Features:
% - 3D FEM thermoelastic analysis with circular hole
% - Proper finite element formulation
% - Constant temperature flux from hole boundary
% - Constant temperature at outer boundary
% - Coupled thermal-mechanical analysis with FEM
% - Professional mesh generation and assembly
%
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-16

clear; clc; close all;

fprintf('=== FEM Thermoelastic Analysis: Medium with Circular Hole ===\n');

%% Problem Parameters
domain = struct();
domain.Lx = 8.0;           % Domain width [m]
domain.Ly = 8.0;           % Domain length [m] 
domain.Lz = 10.0;          % Domain depth [m]
domain.hole_radius = 0.5; % Hole radius [m]
domain.hole_x = 2.0;       % Hole center x [m]
domain.hole_y = 2.0;       % Hole center y [m]

% Material properties (thermoelastic medium)
material = struct();
material.rho = 1800;       % Density [kg/m³]
material.cp = 1000;        % Specific heat [J/kg·K]
material.k = 2.2;          % Thermal conductivity [W/m·K]
material.E = 50e6;         % Young's modulus [Pa]
material.nu = 0.3;         % Poisson's ratio
material.alpha_T = 1e-5;   % Thermal expansion coefficient [1/K]
material.T_ref = 283;      % Reference temperature [K] (10°C)

% Boundary conditions
bc = struct();
bc.T_outer = 283;          % Outer boundary temperature [K] (10°C)
bc.q_hole = 5000;          % Heat flux from hole [W/m²] (positive = into medium)
bc.T_initial = 283;        % Initial temperature [K]

% FEM parameters (reduced for efficiency)
fem = struct();
fem.nx = 10; fem.ny = 10; fem.nz = 15;  % Element resolution (reduced)
fem.nt = 50; fem.dt = 1200;             % Time parameters (50 steps × 1200s = 16.7 hours)

fprintf('Configuration:\n');
fprintf('  Domain: %.1f × %.1f × %.1f m\n', domain.Lx, domain.Ly, domain.Lz);
fprintf('  Hole: radius %.3f m at (%.1f, %.1f)\n', domain.hole_radius, domain.hole_x, domain.hole_y);
fprintf('  Material: E = %.0f MPa, ν = %.2f, α_T = %.0e /K\n', material.E/1e6, material.nu, material.alpha_T);
fprintf('  BC: T_outer = %.0f K, q_hole = %.0f W/m²\n', bc.T_outer, bc.q_hole);
fprintf('  FEM: %d × %d × %d elements, Time: %d steps (%.1f hours)\n', fem.nx, fem.ny, fem.nz, fem.nt, fem.nt*fem.dt/3600);

%% Generate FEM Mesh
fprintf('Generating FEM mesh...\n');
[mesh] = generate_FEM_mesh(domain, fem);

%% Assemble FEM Matrices
fprintf('Assembling FEM matrices...\n');
[K_thermal, M_thermal, K_mechanical, f_thermal] = assemble_FEM_matrices(mesh, material, bc, domain);

%% Apply Boundary Conditions
fprintf('Applying boundary conditions...\n');
[K_thermal, M_thermal, K_mechanical, f_thermal, bc_info] = apply_FEM_boundary_conditions(...
    K_thermal, M_thermal, K_mechanical, f_thermal, mesh, bc, domain);

%% Initialize Solution
fprintf('Initializing FEM solution...\n');
[T, u] = initialize_FEM_solution(mesh, bc);

%% Main FEM Time-Stepping Loop
fprintf('Starting FEM coupled thermoelastic analysis...\n');
[results] = solve_FEM_thermoelastic(mesh, K_thermal, M_thermal, K_mechanical, f_thermal, ...
    T, u, material, bc, bc_info, fem);

%% Post-Processing and Visualization
fprintf('Post-processing and visualization...\n');
visualize_FEM_results(mesh, results, material, bc, fem);

%% Generate VTK Output Files
fprintf('Generating VTK output files...\n');
generate_FEM_vtk_output(mesh, results, material, bc, fem);

% Save results
save('thermoelastic_FEM_results.mat', 'mesh', 'results', 'material', 'bc', 'fem', '-v7.3');
fprintf('FEM analysis completed successfully!\n');

end

function [mesh] = generate_FEM_mesh(domain, fem)
% Generate structured FEM mesh with circular hole identification

    % Create structured grid of nodes
    mesh.x = linspace(0, domain.Lx, fem.nx + 1);
    mesh.y = linspace(0, domain.Ly, fem.ny + 1);
    mesh.z = linspace(0, domain.Lz, fem.nz + 1);
    mesh.dx = domain.Lx / fem.nx;
    mesh.dy = domain.Ly / fem.ny;
    mesh.dz = domain.Lz / fem.nz;
    
    % Total nodes and elements
    mesh.n_nodes = (fem.nx + 1) * (fem.ny + 1) * (fem.nz + 1);
    mesh.n_elements = fem.nx * fem.ny * fem.nz;
    
    % Create node coordinates
    mesh.nodes = zeros(mesh.n_nodes, 3);
    node_id = 1;
    for k = 1:fem.nz+1
        for j = 1:fem.ny+1
            for i = 1:fem.nx+1
                mesh.nodes(node_id, :) = [mesh.x(i), mesh.y(j), mesh.z(k)];
                node_id = node_id + 1;
            end
        end
    end
    
    % Create hexahedral elements (8-node brick elements)
    mesh.elements = zeros(mesh.n_elements, 8);
    elem_id = 1;
    for k = 1:fem.nz
        for j = 1:fem.ny
            for i = 1:fem.nx
                % Node indices for hexahedral element (standard ordering)
                n1 = (k-1)*(fem.nx+1)*(fem.ny+1) + (j-1)*(fem.nx+1) + i;
                n2 = n1 + 1;
                n3 = n1 + (fem.nx+1) + 1;
                n4 = n1 + (fem.nx+1);
                n5 = n1 + (fem.nx+1)*(fem.ny+1);
                n6 = n5 + 1;
                n7 = n5 + (fem.nx+1) + 1;
                n8 = n5 + (fem.nx+1);
                
                mesh.elements(elem_id, :) = [n1, n2, n3, n4, n5, n6, n7, n8];
                elem_id = elem_id + 1;
            end
        end
    end
    
    % Identify hole region and boundaries
    mesh = identify_hole_region_FEM(mesh, domain, fem);
    
    fprintf('  FEM mesh: %d nodes, %d elements\n', mesh.n_nodes, mesh.n_elements);
    fprintf('  Hole elements: %d (%.1f%%)\n', sum(mesh.hole_elements), 100*sum(mesh.hole_elements)/mesh.n_elements);
    fprintf('  Boundary nodes: %d outer, %d hole\n', sum(mesh.outer_boundary_nodes), sum(mesh.hole_boundary_nodes));
end

function mesh = identify_hole_region_FEM(mesh, domain, fem)
% Identify hole region and boundary nodes for FEM

    % Initialize masks
    mesh.hole_elements = false(mesh.n_elements, 1);
    mesh.outer_boundary_nodes = false(mesh.n_nodes, 1);
    mesh.hole_boundary_nodes = false(mesh.n_nodes, 1);
    
    % Check each element for hole intersection
    for elem_id = 1:mesh.n_elements
        element_nodes = mesh.elements(elem_id, :);
        element_coords = mesh.nodes(element_nodes, :);
        
        % Check if element center is inside hole
        elem_center = mean(element_coords, 1);
        dist_to_hole = sqrt((elem_center(1) - domain.hole_x)^2 + (elem_center(2) - domain.hole_y)^2);
        
        if dist_to_hole <= domain.hole_radius
            mesh.hole_elements(elem_id) = true;
        end
    end
    
    % Identify boundary nodes
    tol = 1e-6;
    for node_id = 1:mesh.n_nodes
        x = mesh.nodes(node_id, 1);
        y = mesh.nodes(node_id, 2);
        z = mesh.nodes(node_id, 3);
        
        % Outer boundary nodes
        if abs(x) < tol || abs(x - domain.Lx) < tol || ...
           abs(y) < tol || abs(y - domain.Ly) < tol || ...
           abs(z) < tol || abs(z - domain.Lz) < tol
            mesh.outer_boundary_nodes(node_id) = true;
        end
        
        % Hole boundary nodes (nodes near hole surface)
        dist_to_hole = sqrt((x - domain.hole_x)^2 + (y - domain.hole_y)^2);
        if abs(dist_to_hole - domain.hole_radius) < mesh.dx/2
            mesh.hole_boundary_nodes(node_id) = true;
        end
    end
    
    % Remove hole elements from analysis (they represent void)
    mesh.active_elements = ~mesh.hole_elements;
    
    fprintf('  Hole identification: %d hole elements removed\n', sum(mesh.hole_elements));
end

function [K_thermal, M_thermal, K_mechanical, f_thermal] = assemble_FEM_matrices(mesh, material, bc, domain)
% Assemble global FEM matrices for thermal and mechanical problems

    n_nodes = mesh.n_nodes;
    n_dof_thermal = n_nodes;
    n_dof_mechanical = 3 * n_nodes;  % 3 DOF per node (u, v, w)
    
    % Initialize global matrices
    K_thermal = sparse(n_dof_thermal, n_dof_thermal);
    M_thermal = sparse(n_dof_thermal, n_dof_thermal);
    K_mechanical = sparse(n_dof_mechanical, n_dof_mechanical);
    f_thermal = zeros(n_dof_thermal, 1);
    
    % Gauss quadrature points and weights for hexahedral elements
    [gp, weights] = get_gauss_points_hex();
    
    fprintf('  Assembling element matrices...\n');
    
    % Loop over active elements
    for elem_id = 1:mesh.n_elements
        if ~mesh.active_elements(elem_id)
            continue;  % Skip hole elements
        end
        
        element_nodes = mesh.elements(elem_id, :);
        element_coords = mesh.nodes(element_nodes, :);
        
        % Element matrices
        [Ke_thermal, Me_thermal, Ke_mechanical] = compute_element_matrices(...
            element_coords, material, gp, weights);
        
        % Assemble thermal matrices
        K_thermal(element_nodes, element_nodes) = K_thermal(element_nodes, element_nodes) + Ke_thermal;
        M_thermal(element_nodes, element_nodes) = M_thermal(element_nodes, element_nodes) + Me_thermal;
        
        % Assemble mechanical matrices
        mech_dofs = [];
        for i = 1:8
            node = element_nodes(i);
            mech_dofs = [mech_dofs, 3*node-2, 3*node-1, 3*node];
        end
        K_mechanical(mech_dofs, mech_dofs) = K_mechanical(mech_dofs, mech_dofs) + Ke_mechanical;
    end
    
    fprintf('  Matrix assembly completed\n');
end

function [gp, weights] = get_gauss_points_hex()
% Gauss quadrature points and weights for hexahedral elements (2x2x2)

    g = 1/sqrt(3);  % Gauss point coordinate

    % 8 Gauss points for hexahedral element
    gp = [-g, -g, -g;
           g, -g, -g;
           g,  g, -g;
          -g,  g, -g;
          -g, -g,  g;
           g, -g,  g;
           g,  g,  g;
          -g,  g,  g];

    weights = ones(8, 1);  % All weights are 1 for 2x2x2 quadrature
end

function [Ke_thermal, Me_thermal, Ke_mechanical] = compute_element_matrices(element_coords, material, gp, weights)
% Compute element matrices for thermal and mechanical problems

    n_nodes = 8;  % Hexahedral element

    % Initialize element matrices
    Ke_thermal = zeros(n_nodes, n_nodes);
    Me_thermal = zeros(n_nodes, n_nodes);
    Ke_mechanical = zeros(3*n_nodes, 3*n_nodes);

    % Material properties
    k = material.k;
    rho = material.rho;
    cp = material.cp;
    E = material.E;
    nu = material.nu;

    % Mechanical material matrix (3D)
    D = get_material_matrix_3D(E, nu);

    % Numerical integration
    for gp_id = 1:length(weights)
        xi = gp(gp_id, 1);
        eta = gp(gp_id, 2);
        zeta = gp(gp_id, 3);

        % Shape functions and derivatives
        [N, dN_dxi] = shape_functions_hex8(xi, eta, zeta);

        % Jacobian matrix
        J = dN_dxi' * element_coords;
        det_J = det(J);

        if det_J <= 0
            error('Negative Jacobian determinant in element');
        end

        % Global derivatives
        dN_dx = (J \ dN_dxi')';

        % Thermal matrices
        Ke_thermal = Ke_thermal + (dN_dx * dN_dx') * k * det_J * weights(gp_id);
        Me_thermal = Me_thermal + (N' * N) * rho * cp * det_J * weights(gp_id);

        % Mechanical B-matrix (strain-displacement)
        B = zeros(6, 3*n_nodes);
        for i = 1:n_nodes
            B(1, 3*i-2) = dN_dx(i, 1);
            B(2, 3*i-1) = dN_dx(i, 2);
            B(3, 3*i)   = dN_dx(i, 3);
            B(4, 3*i-2) = dN_dx(i, 2);
            B(4, 3*i-1) = dN_dx(i, 1);
            B(5, 3*i-1) = dN_dx(i, 3);
            B(5, 3*i)   = dN_dx(i, 2);
            B(6, 3*i-2) = dN_dx(i, 3);
            B(6, 3*i)   = dN_dx(i, 1);
        end

        % Mechanical stiffness matrix
        Ke_mechanical = Ke_mechanical + B' * D * B * det_J * weights(gp_id);
    end
end

function D = get_material_matrix_3D(E, nu)
% 3D material matrix for isotropic material

    factor = E / ((1 + nu) * (1 - 2*nu));

    D = factor * [
        1-nu,   nu,   nu,    0,    0,    0;
          nu, 1-nu,   nu,    0,    0,    0;
          nu,   nu, 1-nu,    0,    0,    0;
           0,    0,    0, (1-2*nu)/2,    0,    0;
           0,    0,    0,    0, (1-2*nu)/2,    0;
           0,    0,    0,    0,    0, (1-2*nu)/2];
end

function [N, dN_dxi] = shape_functions_hex8(xi, eta, zeta)
% Shape functions and derivatives for 8-node hexahedral element

    % Shape functions
    N = zeros(8, 1);
    N(1) = (1-xi)*(1-eta)*(1-zeta)/8;
    N(2) = (1+xi)*(1-eta)*(1-zeta)/8;
    N(3) = (1+xi)*(1+eta)*(1-zeta)/8;
    N(4) = (1-xi)*(1+eta)*(1-zeta)/8;
    N(5) = (1-xi)*(1-eta)*(1+zeta)/8;
    N(6) = (1+xi)*(1-eta)*(1+zeta)/8;
    N(7) = (1+xi)*(1+eta)*(1+zeta)/8;
    N(8) = (1-xi)*(1+eta)*(1+zeta)/8;

    % Derivatives with respect to natural coordinates
    dN_dxi = zeros(8, 3);

    % dN/dxi
    dN_dxi(1,1) = -(1-eta)*(1-zeta)/8;
    dN_dxi(2,1) =  (1-eta)*(1-zeta)/8;
    dN_dxi(3,1) =  (1+eta)*(1-zeta)/8;
    dN_dxi(4,1) = -(1+eta)*(1-zeta)/8;
    dN_dxi(5,1) = -(1-eta)*(1+zeta)/8;
    dN_dxi(6,1) =  (1-eta)*(1+zeta)/8;
    dN_dxi(7,1) =  (1+eta)*(1+zeta)/8;
    dN_dxi(8,1) = -(1+eta)*(1+zeta)/8;

    % dN/deta
    dN_dxi(1,2) = -(1-xi)*(1-zeta)/8;
    dN_dxi(2,2) = -(1+xi)*(1-zeta)/8;
    dN_dxi(3,2) =  (1+xi)*(1-zeta)/8;
    dN_dxi(4,2) =  (1-xi)*(1-zeta)/8;
    dN_dxi(5,2) = -(1-xi)*(1+zeta)/8;
    dN_dxi(6,2) = -(1+xi)*(1+zeta)/8;
    dN_dxi(7,2) =  (1+xi)*(1+zeta)/8;
    dN_dxi(8,2) =  (1-xi)*(1+zeta)/8;

    % dN/dzeta
    dN_dxi(1,3) = -(1-xi)*(1-eta)/8;
    dN_dxi(2,3) = -(1+xi)*(1-eta)/8;
    dN_dxi(3,3) = -(1+xi)*(1+eta)/8;
    dN_dxi(4,3) = -(1-xi)*(1+eta)/8;
    dN_dxi(5,3) =  (1-xi)*(1-eta)/8;
    dN_dxi(6,3) =  (1+xi)*(1-eta)/8;
    dN_dxi(7,3) =  (1+xi)*(1+eta)/8;
    dN_dxi(8,3) =  (1-xi)*(1+eta)/8;
end

function [K_thermal, M_thermal, K_mechanical, f_thermal, bc_info] = apply_FEM_boundary_conditions(...
    K_thermal, M_thermal, K_mechanical, f_thermal, mesh, bc, domain)
% Apply boundary conditions to FEM matrices

    bc_info = struct();

    % Thermal boundary conditions
    % 1. Outer boundary: fixed temperature
    outer_nodes = find(mesh.outer_boundary_nodes);
    bc_info.thermal_dirichlet_nodes = outer_nodes;
    bc_info.thermal_dirichlet_values = bc.T_outer * ones(length(outer_nodes), 1);

    % 2. Hole boundary: heat flux (Neumann BC)
    hole_nodes = find(mesh.hole_boundary_nodes);
    bc_info.thermal_neumann_nodes = hole_nodes;

    % Apply heat flux to thermal load vector
    for i = 1:length(hole_nodes)
        node_id = hole_nodes(i);
        % Approximate nodal area for flux application
        nodal_area = 2 * pi * domain.hole_radius * domain.Lz / length(hole_nodes);
        f_thermal(node_id) = f_thermal(node_id) + bc.q_hole * nodal_area;
    end

    % Mechanical boundary conditions
    % Fixed outer boundary (all DOF = 0)
    mech_fixed_dofs = [];
    for i = 1:length(outer_nodes)
        node = outer_nodes(i);
        mech_fixed_dofs = [mech_fixed_dofs, 3*node-2, 3*node-1, 3*node];
    end
    bc_info.mechanical_dirichlet_dofs = mech_fixed_dofs;
    bc_info.mechanical_dirichlet_values = zeros(length(mech_fixed_dofs), 1);

    fprintf('  Boundary conditions: %d thermal Dirichlet, %d thermal Neumann, %d mechanical fixed\n', ...
        length(outer_nodes), length(hole_nodes), length(mech_fixed_dofs));
end

function [T, u] = initialize_FEM_solution(mesh, bc)
% Initialize FEM solution vectors

    n_nodes = mesh.n_nodes;

    % Temperature field (initial uniform temperature)
    T = bc.T_initial * ones(n_nodes, 1);

    % Displacement field (initially zero)
    u = zeros(3 * n_nodes, 1);

    fprintf('  FEM solution initialized: T = %.0f K, displacements = 0\n', bc.T_initial);
end

function [results] = solve_FEM_thermoelastic(mesh, K_thermal, M_thermal, K_mechanical, f_thermal, ...
    T, u, material, bc, bc_info, fem)
% Main FEM thermoelastic solver with time stepping

    n_nodes = mesh.n_nodes;
    dt = fem.dt;

    % Storage for results
    results = struct();
    results.T = zeros(n_nodes, fem.nt);
    results.u = zeros(3*n_nodes, fem.nt);
    results.time = (1:fem.nt) * dt;
    results.T_avg = zeros(fem.nt, 1);
    results.max_displacement = zeros(fem.nt, 1);

    % Time integration parameters (backward Euler)
    A_thermal = M_thermal/dt + K_thermal;

    fprintf('  Starting FEM time integration...\n');
    tic;

    for n = 1:fem.nt
        % Store old temperature
        T_old = T;

        % Thermal step: solve (M/dt + K)T = M*T_old/dt + f
        rhs_thermal = M_thermal * T_old / dt + f_thermal;

        % Apply thermal boundary conditions
        T = solve_with_dirichlet_BC(A_thermal, rhs_thermal, T, ...
            bc_info.thermal_dirichlet_nodes, bc_info.thermal_dirichlet_values);

        % Mechanical step: solve for displacements due to thermal loading
        u = solve_mechanical_FEM(K_mechanical, T, T_old, mesh, material, bc_info);

        % Store results
        results.T(:, n) = T;
        results.u(:, n) = u;

        % Compute performance metrics
        active_nodes = ~mesh.hole_boundary_nodes & ~mesh.outer_boundary_nodes;
        if any(active_nodes)
            results.T_avg(n) = mean(T(active_nodes)) - 273.15;  % Convert to Celsius
        else
            results.T_avg(n) = bc.T_initial - 273.15;
        end

        % Maximum displacement
        u_reshaped = reshape(u, 3, n_nodes);
        displacement_magnitude = sqrt(sum(u_reshaped.^2, 1));
        results.max_displacement(n) = max(displacement_magnitude) * 1000;  % Convert to mm

        % Progress output
        if mod(n, 20) == 0 || n == fem.nt
            elapsed = toc;
            remaining = elapsed * (fem.nt - n) / n;
            fprintf('    Step %d/%d (%.1f%%) - T_avg: %.1f°C, max_disp: %.2f mm\n', ...
                n, fem.nt, 100*n/fem.nt, results.T_avg(n), results.max_displacement(n));
            fprintf('      Elapsed: %.1fs, Remaining: %.1fs\n', elapsed, remaining);
        end
    end

    fprintf('  FEM time integration completed in %.1f seconds\n', toc);
end

function T = solve_with_dirichlet_BC(A, rhs, T, dirichlet_nodes, dirichlet_values)
% Solve linear system with Dirichlet boundary conditions

    % Modify system for Dirichlet BCs
    A_mod = A;
    rhs_mod = rhs;

    for i = 1:length(dirichlet_nodes)
        node = dirichlet_nodes(i);
        value = dirichlet_values(i);

        % Set row to identity
        A_mod(node, :) = 0;
        A_mod(node, node) = 1;
        rhs_mod(node) = value;
    end

    % Solve modified system
    T = A_mod \ rhs_mod;
end

function u = solve_mechanical_FEM(K_mechanical, T, T_old, mesh, material, bc_info)
% Solve mechanical equilibrium with thermal loading

    n_nodes = mesh.n_nodes;

    % Thermal loading vector
    dT = T - T_old;
    f_thermal_mech = compute_thermal_loading_FEM(dT, mesh, material);

    % Apply mechanical boundary conditions and solve
    u = solve_with_dirichlet_BC(K_mechanical, f_thermal_mech, zeros(3*n_nodes, 1), ...
        bc_info.mechanical_dirichlet_dofs, bc_info.mechanical_dirichlet_values);
end

function f_thermal = compute_thermal_loading_FEM(dT, mesh, material)
% Compute thermal loading vector for mechanical analysis

    n_nodes = mesh.n_nodes;
    f_thermal = zeros(3*n_nodes, 1);

    % Material properties
    E = material.E;
    nu = material.nu;
    alpha_T = material.alpha_T;

    % Thermal stress coefficient
    beta = E * alpha_T / (1 - 2*nu);

    % Simplified thermal loading (nodal forces)
    for node = 1:n_nodes
        if ~mesh.hole_boundary_nodes(node)  % Only for solid nodes
            thermal_force = beta * dT(node);

            % Apply to all three DOF (simplified)
            f_thermal(3*node-2) = thermal_force;
            f_thermal(3*node-1) = thermal_force;
            f_thermal(3*node) = thermal_force;
        end
    end
end

function visualize_FEM_results(mesh, results, material, bc, fem)
% Visualization of FEM thermoelastic results

    fprintf('  Generating FEM visualization...\n');

    figure('Position', [100, 100, 1600, 1200], 'Name', 'FEM Thermoelastic Analysis: Circular Hole');

    % Final results
    T_final = results.T(:, end) - 273.15;  % Convert to Celsius
    u_final = results.u(:, end);
    time_hours = results.time / 3600;

    % Reshape displacement for visualization
    u_reshaped = reshape(u_final, 3, mesh.n_nodes);
    u_x = u_reshaped(1, :)' * 1000;  % mm
    u_y = u_reshaped(2, :)' * 1000;  % mm
    u_z = u_reshaped(3, :)' * 1000;  % mm
    u_mag = sqrt(u_x.^2 + u_y.^2 + u_z.^2);

    % 1. Temperature evolution
    subplot(2,3,1);
    plot(time_hours, results.T_avg, 'b-', 'LineWidth', 2);
    xlabel('Time [hours]'); ylabel('Average Temperature [°C]');
    title('FEM Temperature Evolution');
    grid on;

    % 2. Displacement evolution
    subplot(2,3,2);
    plot(time_hours, results.max_displacement, 'r-', 'LineWidth', 2);
    xlabel('Time [hours]'); ylabel('Max Displacement [mm]');
    title('FEM Displacement Evolution');
    grid on;

    % 3. Temperature distribution (scatter plot)
    subplot(2,3,3);
    active_nodes = ~mesh.hole_boundary_nodes;
    scatter3(mesh.nodes(active_nodes,1), mesh.nodes(active_nodes,2), mesh.nodes(active_nodes,3), ...
        20, T_final(active_nodes), 'filled');
    colorbar; title('Temperature Distribution (°C)');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 4. Displacement magnitude distribution
    subplot(2,3,4);
    scatter3(mesh.nodes(active_nodes,1), mesh.nodes(active_nodes,2), mesh.nodes(active_nodes,3), ...
        20, u_mag(active_nodes), 'filled');
    colorbar; title('Displacement Magnitude (mm)');
    xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
    view(45, 30);

    % 5. Cross-section temperature
    subplot(2,3,5);
    mid_z = 10.0;  % Mid-depth
    tol = 0.5;
    mid_nodes = abs(mesh.nodes(:,3) - mid_z) < tol & active_nodes;

    if any(mid_nodes)
        scatter(mesh.nodes(mid_nodes,1), mesh.nodes(mid_nodes,2), 50, T_final(mid_nodes), 'filled');
        colorbar; title('Temperature at Mid-Depth (°C)');
        xlabel('x [m]'); ylabel('y [m]');
        axis equal;

        % Mark hole boundary
        hold on;
        theta = linspace(0, 2*pi, 100);
        hole_x = 2.0 + 0.08 * cos(theta);
        hole_y = 2.0 + 0.08 * sin(theta);
        plot(hole_x, hole_y, 'k-', 'LineWidth', 2);
    end

    % 6. Performance summary
    subplot(2,3,6);
    axis off;

    final_T_avg = results.T_avg(end);
    final_max_disp = results.max_displacement(end);

    text(0.1, 0.9, 'FEM THERMOELASTIC RESULTS', 'FontSize', 12, 'FontWeight', 'bold');
    text(0.1, 0.8, sprintf('Nodes: %d', mesh.n_nodes), 'FontSize', 10);
    text(0.1, 0.7, sprintf('Elements: %d', mesh.n_elements), 'FontSize', 10);
    text(0.1, 0.6, sprintf('Active Elements: %d', sum(mesh.active_elements)), 'FontSize', 10);
    text(0.1, 0.5, sprintf('Simulation Time: %.1f hours', time_hours(end)), 'FontSize', 10);
    text(0.1, 0.4, sprintf('Final Avg Temperature: %.1f°C', final_T_avg), 'FontSize', 10);
    text(0.1, 0.3, sprintf('Max Displacement: %.2f mm', final_max_disp), 'FontSize', 10);
    text(0.1, 0.2, sprintf('Heat Flux: %.0f W/m²', bc.q_hole), 'FontSize', 10);
    text(0.1, 0.1, sprintf('Material: E = %.0f GPa', material.E/1e9), 'FontSize', 10);

    sgtitle('FEM Thermoelastic Analysis: Medium with Circular Hole', 'FontSize', 14, 'FontWeight', 'bold');

    % Save visualization
    saveas(gcf, 'thermoelastic_FEM_results.png');
    fprintf('  FEM visualization completed and saved!\n');
end

function generate_FEM_vtk_output(mesh, results, material, bc, fem)
% Generate comprehensive VTK/VTU/PVD files for FEM results

    fprintf('  Creating FEM output directories...\n');

    % Create output directories
    vtk_dir = 'vtk_FEM_output';
    vtu_dir = 'vtu_FEM_output';
    if ~exist(vtk_dir, 'dir'), mkdir(vtk_dir); end
    if ~exist(vtu_dir, 'dir'), mkdir(vtu_dir); end

    % Time steps to output (every 5th step for more detail)
    output_steps = 1:5:fem.nt;
    if output_steps(end) ~= fem.nt
        output_steps = [output_steps, fem.nt];
    end

    fprintf('  Generating FEM output files for %d time steps...\n', length(output_steps));

    for step_idx = 1:length(output_steps)
        n = output_steps(step_idx);
        time = results.time(n);

        % Extract fields at current time step
        T = results.T(:, n);
        u_vec = results.u(:, n);

        % Reshape displacement
        u_reshaped = reshape(u_vec, 3, mesh.n_nodes);
        u_x = u_reshaped(1, :)';
        u_y = u_reshaped(2, :)';
        u_z = u_reshaped(3, :)';

        % Create filenames
        vtk_filename = sprintf('%s/thermoelastic_FEM_step_%04d.vtk', vtk_dir, n);
        vtu_filename = sprintf('%s/thermoelastic_FEM_step_%04d.vtu', vtu_dir, n);

        % Write both VTK and VTU files
        write_FEM_vtk_unstructured(vtk_filename, mesh, T, u_x, u_y, u_z, time, material, bc);
        write_FEM_vtu_unstructured(vtu_filename, mesh, T, u_x, u_y, u_z, time, material, bc);

        if mod(step_idx, 3) == 0 || step_idx == length(output_steps)
            fprintf('    Written %d/%d FEM files (VTK + VTU)\n', step_idx, length(output_steps));
        end
    end

    % Create PVD files for both formats
    create_FEM_pvd_file(vtk_dir, 'vtk', output_steps, results.time);
    create_FEM_pvd_file(vtu_dir, 'vtu', output_steps, results.time);

    fprintf('  FEM output completed:\n');
    fprintf('    VTK: %d files in %s/ (legacy format)\n', length(output_steps), vtk_dir);
    fprintf('    VTU: %d files in %s/ (XML format)\n', length(output_steps), vtu_dir);
    fprintf('  Open PVD files in ParaView:\n');
    fprintf('    %s/thermoelastic_FEM_time_series.pvd\n', vtk_dir);
    fprintf('    %s/thermoelastic_FEM_time_series.pvd\n', vtu_dir);
end

function write_FEM_vtk_unstructured(filename, mesh, T, u_x, u_y, u_z, time, material, bc)
% Write VTK unstructured grid file for FEM results (fixed format)

    fid = fopen(filename, 'w');
    if fid == -1
        error('Could not open file %s for writing', filename);
    end

    try
        % Count active elements and nodes
        active_elements = find(mesh.active_elements);
        n_active_elements = length(active_elements);
        n_nodes = mesh.n_nodes;

        % VTK header (fixed format)
        fprintf(fid, '# vtk DataFile Version 3.0\n');
        fprintf(fid, 'FEM Thermoelastic Analysis Circular Hole t=%.1fh\n', time/3600);
        fprintf(fid, 'ASCII\n');
        fprintf(fid, 'DATASET UNSTRUCTURED_GRID\n');
        fprintf(fid, '\n');

        % Points
        fprintf(fid, 'POINTS %d float\n', n_nodes);
        for i = 1:n_nodes
            fprintf(fid, '%.6f %.6f %.6f\n', mesh.nodes(i,1), mesh.nodes(i,2), mesh.nodes(i,3));
        end
        fprintf(fid, '\n');

        % Cells (only active elements)
        fprintf(fid, 'CELLS %d %d\n', n_active_elements, n_active_elements * 9);
        for i = 1:length(active_elements)
            elem_id = active_elements(i);
            element_nodes = mesh.elements(elem_id, :) - 1;  % VTK uses 0-based indexing
            fprintf(fid, '8 %d %d %d %d %d %d %d %d\n', element_nodes);
        end
        fprintf(fid, '\n');

        % Cell types (hexahedral = 12)
        fprintf(fid, 'CELL_TYPES %d\n', n_active_elements);
        for i = 1:n_active_elements
            fprintf(fid, '12\n');
        end
        fprintf(fid, '\n');

        % Point data
        fprintf(fid, 'POINT_DATA %d\n', n_nodes);

        % Temperature field (primary scalar)
        fprintf(fid, 'SCALARS Temperature float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for i = 1:n_nodes
            temp_celsius = T(i) - 273.15;
            if isnan(temp_celsius) || isinf(temp_celsius)
                temp_celsius = 0.0;  % Handle invalid values
            end
            fprintf(fid, '%.6f\n', temp_celsius);
        end
        fprintf(fid, '\n');

        % Displacement vector field (primary vector)
        fprintf(fid, 'VECTORS Displacement float\n');
        for i = 1:n_nodes
            % Handle invalid values
            ux = u_x(i); if isnan(ux) || isinf(ux), ux = 0.0; end
            uy = u_y(i); if isnan(uy) || isinf(uy), uy = 0.0; end
            uz = u_z(i); if isnan(uz) || isinf(uz), uz = 0.0; end
            fprintf(fid, '%.6f %.6f %.6f\n', ux, uy, uz);
        end
        fprintf(fid, '\n');

        % Displacement magnitude
        fprintf(fid, 'SCALARS Displacement_Magnitude float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for i = 1:n_nodes
            ux = u_x(i); if isnan(ux) || isinf(ux), ux = 0.0; end
            uy = u_y(i); if isnan(uy) || isinf(uy), uy = 0.0; end
            uz = u_z(i); if isnan(uz) || isinf(uz), uz = 0.0; end
            disp_mag = sqrt(ux^2 + uy^2 + uz^2) * 1000;  % mm
            fprintf(fid, '%.6f\n', disp_mag);
        end
        fprintf(fid, '\n');

        % Thermal stress
        fprintf(fid, 'SCALARS Thermal_Stress float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for i = 1:n_nodes
            dT = T(i) - material.T_ref;
            if isnan(dT) || isinf(dT), dT = 0.0; end
            thermal_stress = material.E * material.alpha_T * dT / (1 - material.nu) / 1e6;  % MPa
            if isnan(thermal_stress) || isinf(thermal_stress), thermal_stress = 0.0; end
            fprintf(fid, '%.6f\n', thermal_stress);
        end
        fprintf(fid, '\n');

        % Node type indicator
        fprintf(fid, 'SCALARS Node_Type float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for i = 1:n_nodes
            if mesh.hole_boundary_nodes(i)
                node_type = 2.0;  % Hole boundary
            elseif mesh.outer_boundary_nodes(i)
                node_type = 1.0;  % Outer boundary
            else
                node_type = 0.0;  % Interior
            end
            fprintf(fid, '%.1f\n', node_type);
        end

    catch ME
        fclose(fid);
        rethrow(ME);
    end

    fclose(fid);
end

function write_FEM_vtu_unstructured(filename, mesh, T, u_x, u_y, u_z, time, material, bc)
% Write VTU (XML) unstructured grid file for FEM results

    fid = fopen(filename, 'w');
    if fid == -1
        error('Could not open file %s for writing', filename);
    end

    try
        % Count active elements and nodes
        active_elements = find(mesh.active_elements);
        n_active_elements = length(active_elements);
        n_nodes = mesh.n_nodes;

        % VTU header (XML format)
        fprintf(fid, '<?xml version="1.0"?>\n');
        fprintf(fid, '<VTKFile type="UnstructuredGrid" version="0.1" byte_order="LittleEndian">\n');
        fprintf(fid, '  <UnstructuredGrid>\n');
        fprintf(fid, '    <Piece NumberOfPoints="%d" NumberOfCells="%d">\n', n_nodes, n_active_elements);

        % Points
        fprintf(fid, '      <Points>\n');
        fprintf(fid, '        <DataArray type="Float32" NumberOfComponents="3" format="ascii">\n');
        for i = 1:n_nodes
            fprintf(fid, '          %.6f %.6f %.6f\n', mesh.nodes(i,1), mesh.nodes(i,2), mesh.nodes(i,3));
        end
        fprintf(fid, '        </DataArray>\n');
        fprintf(fid, '      </Points>\n');

        % Cells
        fprintf(fid, '      <Cells>\n');

        % Connectivity
        fprintf(fid, '        <DataArray type="Int32" Name="connectivity" format="ascii">\n');
        for i = 1:length(active_elements)
            elem_id = active_elements(i);
            element_nodes = mesh.elements(elem_id, :) - 1;  % VTK uses 0-based indexing
            fprintf(fid, '          %d %d %d %d %d %d %d %d\n', element_nodes);
        end
        fprintf(fid, '        </DataArray>\n');

        % Offsets
        fprintf(fid, '        <DataArray type="Int32" Name="offsets" format="ascii">\n');
        for i = 1:n_active_elements
            fprintf(fid, '          %d\n', i * 8);
        end
        fprintf(fid, '        </DataArray>\n');

        % Cell types (hexahedral = 12)
        fprintf(fid, '        <DataArray type="UInt8" Name="types" format="ascii">\n');
        for i = 1:n_active_elements
            fprintf(fid, '          12\n');
        end
        fprintf(fid, '        </DataArray>\n');
        fprintf(fid, '      </Cells>\n');

        % Point data
        fprintf(fid, '      <PointData Scalars="Temperature" Vectors="Displacement">\n');

        % Temperature field
        fprintf(fid, '        <DataArray type="Float32" Name="Temperature" format="ascii">\n');
        for i = 1:n_nodes
            temp_celsius = T(i) - 273.15;
            if isnan(temp_celsius) || isinf(temp_celsius), temp_celsius = 0.0; end
            fprintf(fid, '          %.6f\n', temp_celsius);
        end
        fprintf(fid, '        </DataArray>\n');

        % Displacement vector field
        fprintf(fid, '        <DataArray type="Float32" Name="Displacement" NumberOfComponents="3" format="ascii">\n');
        for i = 1:n_nodes
            ux = u_x(i); if isnan(ux) || isinf(ux), ux = 0.0; end
            uy = u_y(i); if isnan(uy) || isinf(uy), uy = 0.0; end
            uz = u_z(i); if isnan(uz) || isinf(uz), uz = 0.0; end
            fprintf(fid, '          %.6f %.6f %.6f\n', ux, uy, uz);
        end
        fprintf(fid, '        </DataArray>\n');

        % Displacement magnitude
        fprintf(fid, '        <DataArray type="Float32" Name="Displacement_Magnitude" format="ascii">\n');
        for i = 1:n_nodes
            ux = u_x(i); if isnan(ux) || isinf(ux), ux = 0.0; end
            uy = u_y(i); if isnan(uy) || isinf(uy), uy = 0.0; end
            uz = u_z(i); if isnan(uz) || isinf(uz), uz = 0.0; end
            disp_mag = sqrt(ux^2 + uy^2 + uz^2) * 1000;  % mm
            fprintf(fid, '          %.6f\n', disp_mag);
        end
        fprintf(fid, '        </DataArray>\n');

        % Thermal stress
        fprintf(fid, '        <DataArray type="Float32" Name="Thermal_Stress" format="ascii">\n');
        for i = 1:n_nodes
            dT = T(i) - material.T_ref;
            if isnan(dT) || isinf(dT), dT = 0.0; end
            thermal_stress = material.E * material.alpha_T * dT / (1 - material.nu) / 1e6;  % MPa
            if isnan(thermal_stress) || isinf(thermal_stress), thermal_stress = 0.0; end
            fprintf(fid, '          %.6f\n', thermal_stress);
        end
        fprintf(fid, '        </DataArray>\n');

        % Node type indicator
        fprintf(fid, '        <DataArray type="Float32" Name="Node_Type" format="ascii">\n');
        for i = 1:n_nodes
            if mesh.hole_boundary_nodes(i)
                node_type = 2.0;  % Hole boundary
            elseif mesh.outer_boundary_nodes(i)
                node_type = 1.0;  % Outer boundary
            else
                node_type = 0.0;  % Interior
            end
            fprintf(fid, '          %.1f\n', node_type);
        end
        fprintf(fid, '        </DataArray>\n');

        fprintf(fid, '      </PointData>\n');

        % Cell data (optional - element-based data)
        fprintf(fid, '      <CellData>\n');
        fprintf(fid, '        <DataArray type="Float32" Name="Element_ID" format="ascii">\n');
        for i = 1:n_active_elements
            fprintf(fid, '          %d\n', active_elements(i));
        end
        fprintf(fid, '        </DataArray>\n');
        fprintf(fid, '      </CellData>\n');

        % VTU footer
        fprintf(fid, '    </Piece>\n');
        fprintf(fid, '  </UnstructuredGrid>\n');
        fprintf(fid, '</VTKFile>\n');

    catch ME
        fclose(fid);
        rethrow(ME);
    end

    fclose(fid);
end

function create_FEM_pvd_file(output_dir, file_format, output_steps, time_array)
% Create PVD file for FEM time series (supports both VTK and VTU formats)

    pvd_filename = sprintf('%s/thermoelastic_FEM_time_series.pvd', output_dir);

    fid = fopen(pvd_filename, 'w');
    if fid == -1
        error('Could not create PVD file %s', pvd_filename);
    end

    try
        % PVD header
        fprintf(fid, '<?xml version="1.0"?>\n');
        fprintf(fid, '<VTKFile type="Collection" version="0.1" byte_order="LittleEndian">\n');
        fprintf(fid, '  <Collection>\n');

        % Add each time step
        for i = 1:length(output_steps)
            n = output_steps(i);
            time_hours = time_array(n) / 3600;

            % Create filename based on format
            if strcmp(file_format, 'vtu')
                data_file = sprintf('thermoelastic_FEM_step_%04d.vtu', n);
            else
                data_file = sprintf('thermoelastic_FEM_step_%04d.vtk', n);
            end

            fprintf(fid, '    <DataSet timestep="%.6f" group="" part="0" file="%s"/>\n', ...
                time_hours, data_file);
        end

        % PVD footer
        fprintf(fid, '  </Collection>\n');
        fprintf(fid, '</VTKFile>\n');

    catch ME
        fclose(fid);
        rethrow(ME);
    end

    fclose(fid);

    fprintf('  Created %s PVD time series file: %s\n', upper(file_format), pvd_filename);
end
