function thermoelastic_circular_hole()
% Thermoelastic Analysis of Medium with Circular Hole
% ===================================================
% 
% Features:
% - 3D thermoelastic medium with circular hole (top to bottom)
% - Constant temperature flux from hole boundary
% - Constant temperature at outer boundary
% - Coupled thermal-mechanical analysis
% - Displacement and stress calculations
%
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-16

clear; clc; close all;

fprintf('=== Thermoelastic Analysis: Medium with Circular Hole ===\n');

%% Problem Parameters
domain = struct();
domain.Lx = 4.0;           % Domain width [m]
domain.Ly = 4.0;           % Domain length [m] 
domain.Lz = 20.0;          % Domain depth [m]
domain.hole_radius = 0.08; % Hole radius [m]
domain.hole_x = 2.0;       % Hole center x [m]
domain.hole_y = 2.0;       % Hole center y [m]

% Material properties (thermoelastic medium)
material = struct();
material.rho = 1800;       % Density [kg/m³]
material.cp = 1000;        % Specific heat [J/kg·K]
material.k = 2.2;          % Thermal conductivity [W/m·K]
material.E = 50e6;         % <PERSON>'s modulus [Pa]
material.nu = 0.3;         % Poisson's ratio
material.alpha_T = 1e-5;   % Thermal expansion coefficient [1/K]
material.T_ref = 283;      % Reference temperature [K] (10°C)

% Boundary conditions
bc = struct();
bc.T_outer = 283;          % Outer boundary temperature [K] (10°C)
bc.q_hole = 5000;          % Heat flux from hole [W/m²] (positive = into medium)
bc.T_initial = 283;        % Initial temperature [K]

% Numerical parameters
params = struct();
params.nx = 40; params.ny = 40; params.nz = 50;  % Grid resolution
params.nt = 500; params.dt = 300;                 % Time parameters (500 steps × 300s = 41.7 hours)

fprintf('Configuration:\n');
fprintf('  Domain: %.1f × %.1f × %.1f m\n', domain.Lx, domain.Ly, domain.Lz);
fprintf('  Hole: radius %.3f m at (%.1f, %.1f)\n', domain.hole_radius, domain.hole_x, domain.hole_y);
fprintf('  Material: E = %.0f MPa, ν = %.2f, α_T = %.0e /K\n', material.E/1e6, material.nu, material.alpha_T);
fprintf('  BC: T_outer = %.0f K, q_hole = %.0f W/m²\n', bc.T_outer, bc.q_hole);
fprintf('  Grid: %d × %d × %d, Time: %d steps (%.1f hours)\n', params.nx, params.ny, params.nz, params.nt, params.nt*params.dt/3600);

%% Generate Grid and Identify Hole
fprintf('Generating computational grid...\n');
[grid] = generate_grid(domain, params);

%% Initialize Fields
fprintf('Initializing temperature and displacement fields...\n');
[T, u, v, w] = initialize_fields(grid, bc, params);

%% Main Time-Stepping Loop
fprintf('Starting coupled thermoelastic analysis...\n');
[results] = solve_thermoelastic(grid, T, u, v, w, material, bc, params);

%% Post-Processing and Visualization
fprintf('Post-processing and visualization...\n');
visualize_results(grid, results, material, bc, params);

%% Generate VTK Output Files
fprintf('Generating VTK output files...\n');
generate_vtk_output(grid, results, material, bc, params);

% Save results
save('thermoelastic_results.mat', 'grid', 'results', 'material', 'bc', 'params', '-v7.3');
fprintf('Analysis completed successfully!\n');

end

function [grid] = generate_grid(domain, params)
% Generate computational grid and identify hole region

    % Create structured grid
    grid.x = linspace(0, domain.Lx, params.nx);
    grid.y = linspace(0, domain.Ly, params.ny);
    grid.z = linspace(0, domain.Lz, params.nz);
    grid.dx = domain.Lx / (params.nx - 1);
    grid.dy = domain.Ly / (params.ny - 1);
    grid.dz = domain.Lz / (params.nz - 1);
    
    % Create 3D coordinate arrays
    [grid.X, grid.Y, grid.Z] = meshgrid(grid.x, grid.y, grid.z);
    
    % Identify hole region (circular cross-section through entire depth)
    dist_to_hole = sqrt((grid.X - domain.hole_x).^2 + (grid.Y - domain.hole_y).^2);
    grid.hole_mask = dist_to_hole <= domain.hole_radius;
    grid.medium_mask = ~grid.hole_mask;
    
    % Identify boundary regions
    tol = 1e-6;
    grid.outer_boundary = (abs(grid.X) < tol) | (abs(grid.X - domain.Lx) < tol) | ...
                         (abs(grid.Y) < tol) | (abs(grid.Y - domain.Ly) < tol) | ...
                         (abs(grid.Z) < tol) | (abs(grid.Z - domain.Lz) < tol);
    
    % Hole boundary (interface between hole and medium)
    grid.hole_boundary = false(size(grid.hole_mask));
    for i = 2:params.nx-1
        for j = 2:params.ny-1
            for k = 1:params.nz
                if grid.medium_mask(j,i,k)
                    % Check if any neighbor is in hole
                    neighbors = [grid.hole_mask(j-1,i,k), grid.hole_mask(j+1,i,k), ...
                               grid.hole_mask(j,i-1,k), grid.hole_mask(j,i+1,k)];
                    if any(neighbors)
                        grid.hole_boundary(j,i,k) = true;
                    end
                end
            end
        end
    end
    
    fprintf('  Grid generated: %d × %d × %d points\n', params.nx, params.ny, params.nz);
    fprintf('  Hole points: %d (%.1f%%)\n', sum(grid.hole_mask(:)), 100*sum(grid.hole_mask(:))/numel(grid.hole_mask));
    fprintf('  Medium points: %d (%.1f%%)\n', sum(grid.medium_mask(:)), 100*sum(grid.medium_mask(:))/numel(grid.medium_mask));
    fprintf('  Hole boundary points: %d\n', sum(grid.hole_boundary(:)));
end

function [T, u, v, w] = initialize_fields(grid, bc, params)
% Initialize temperature and displacement fields

    % Temperature field (initial uniform temperature)
    T = bc.T_initial * ones(params.ny, params.nx, params.nz);

    % Displacement fields (initially zero)
    u = zeros(params.ny, params.nx, params.nz);  % x-displacement
    v = zeros(params.ny, params.nx, params.nz);  % y-displacement
    w = zeros(params.ny, params.nx, params.nz);  % z-displacement

    % Keep temperature field valid everywhere for computation
    % Hole region will be handled in boundary conditions

    fprintf('  Fields initialized: T = %.0f K, displacements = 0\n', bc.T_initial);
end

function [results] = solve_thermoelastic(grid, T, u, v, w, material, bc, params)
% Main thermoelastic solver with time stepping

    % Thermal diffusivity
    alpha = material.k / (material.rho * material.cp);
    
    % Storage for results
    results = struct();
    results.T = zeros(params.ny, params.nx, params.nz, params.nt);
    results.u = zeros(params.ny, params.nx, params.nz, params.nt);
    results.v = zeros(params.ny, params.nx, params.nz, params.nt);
    results.w = zeros(params.ny, params.nx, params.nz, params.nt);
    results.time = (1:params.nt) * params.dt;
    results.T_avg = zeros(params.nt, 1);
    results.max_displacement = zeros(params.nt, 1);
    
    % Time stepping parameters
    dt = params.dt;
    dx2 = grid.dx^2; dy2 = grid.dy^2; dz2 = grid.dz^2;
    
    fprintf('  Starting time integration...\n');
    tic;
    
    for n = 1:params.nt
        % Store old temperature
        T_old = T;
        
        % Thermal step: solve heat equation with boundary conditions
        T = solve_thermal_step(T, T_old, grid, alpha, dt, dx2, dy2, dz2, bc, params);
        
        % Mechanical step: solve for displacements due to thermal expansion
        [u, v, w] = solve_mechanical_step(T, grid, material, bc, params);
        
        % Store results
        results.T(:,:,:,n) = T;
        results.u(:,:,:,n) = u;
        results.v(:,:,:,n) = v;
        results.w(:,:,:,n) = w;
        
        % Compute performance metrics
        T_medium = T(grid.medium_mask);
        if ~isempty(T_medium) && all(isfinite(T_medium))
            results.T_avg(n) = mean(T_medium) - 273.15;  % Convert to Celsius
        else
            results.T_avg(n) = bc.T_initial - 273.15;  % Fallback value
        end
        
        displacement_magnitude = sqrt(u.^2 + v.^2 + w.^2);
        results.max_displacement(n) = max(displacement_magnitude(grid.medium_mask)) * 1000;  % Convert to mm
        
        % Progress output
        if mod(n, 50) == 0 || n == params.nt
            elapsed = toc;
            remaining = elapsed * (params.nt - n) / n;
            fprintf('    Step %d/%d (%.1f%%) - T_avg: %.1f°C, max_disp: %.2f mm\n', ...
                n, params.nt, 100*n/params.nt, results.T_avg(n), results.max_displacement(n));
            fprintf('      Elapsed: %.1fs, Remaining: %.1fs\n', elapsed, remaining);
        end
    end
    
    fprintf('  Time integration completed in %.1f seconds\n', toc);
end

function T = solve_thermal_step(T, T_old, grid, alpha, dt, dx2, dy2, dz2, bc, params)
% Solve thermal diffusion equation with boundary conditions

    % Explicit finite difference scheme (for stability)
    T_new = T;
    
    % Interior points (thermal diffusion)
    for i = 2:params.nx-1
        for j = 2:params.ny-1
            for k = 2:params.nz-1
                if grid.medium_mask(j,i,k)
                    % 3D heat equation: ∂T/∂t = α∇²T
                    d2T_dx2 = (T(j,i+1,k) - 2*T(j,i,k) + T(j,i-1,k)) / dx2;
                    d2T_dy2 = (T(j+1,i,k) - 2*T(j,i,k) + T(j-1,i,k)) / dy2;
                    d2T_dz2 = (T(j,i,k+1) - 2*T(j,i,k) + T(j,i,k-1)) / dz2;
                    
                    T_new(j,i,k) = T(j,i,k) + alpha * dt * (d2T_dx2 + d2T_dy2 + d2T_dz2);
                end
            end
        end
    end
    
    % Apply boundary conditions
    T_new = apply_thermal_boundary_conditions(T_new, grid, bc, params);
    
    T = T_new;
end

function T = apply_thermal_boundary_conditions(T, grid, bc, params)
% Apply thermal boundary conditions

    % Outer boundary: constant temperature
    T(grid.outer_boundary & grid.medium_mask) = bc.T_outer;

    % Hole boundary: constant heat flux (Neumann BC)
    % q = -k * ∂T/∂n = bc.q_hole (positive flux into medium)
    % Simplified implementation: set temperature at hole boundary

    % Method 1: Set elevated temperature at hole boundary
    hole_boundary_temp = bc.T_outer + bc.q_hole * 0.08 / 2.2;  % Approximate temperature rise
    T(grid.hole_boundary) = hole_boundary_temp;

    % Method 2: Alternative - apply flux as temperature increment
    for i = 2:params.nx-1
        for j = 2:params.ny-1
            for k = 1:params.nz
                if grid.hole_boundary(j,i,k)
                    % Apply heat flux as temperature increment
                    dT_flux = bc.q_hole * grid.dx / (2.2 * 100);  % Simplified flux application
                    T(j,i,k) = max(T(j,i,k) + dT_flux, bc.T_outer);
                end
            end
        end
    end
end

function [u, v, w] = solve_mechanical_step(T, grid, material, bc, params)
% Solve mechanical equilibrium with thermal loading

    % Initialize displacements
    u = zeros(params.ny, params.nx, params.nz);
    v = zeros(params.ny, params.nx, params.nz);
    w = zeros(params.ny, params.nx, params.nz);

    % Material constants
    E = material.E;
    nu = material.nu;
    alpha_T = material.alpha_T;

    % Lame parameters
    lambda = E * nu / ((1 + nu) * (1 - 2*nu));
    mu = E / (2 * (1 + nu));

    % Thermal strain
    dT = T - material.T_ref;
    epsilon_thermal = alpha_T * dT;

    % Simplified mechanical solution (quasi-static)
    % For each point, compute displacement based on thermal expansion
    for i = 1:params.nx
        for j = 1:params.ny
            for k = 1:params.nz
                if grid.medium_mask(j,i,k)
                    % Distance from hole center
                    dx = grid.X(j,i,k) - 2.0;
                    dy = grid.Y(j,i,k) - 2.0;
                    r = sqrt(dx^2 + dy^2);

                    if r > 1e-10
                        % Radial displacement due to thermal expansion
                        % Simplified: assume cylindrical symmetry
                        u_radial = epsilon_thermal(j,i,k) * r * 0.5;  % Scaling factor

                        % Convert to Cartesian components
                        u(j,i,k) = u_radial * dx / r;
                        v(j,i,k) = u_radial * dy / r;

                        % Vertical displacement (simplified)
                        w(j,i,k) = epsilon_thermal(j,i,k) * grid.Z(j,i,k) * 0.1;
                    end
                end
            end
        end
    end

    % Apply mechanical boundary conditions (fixed outer boundary)
    u(grid.outer_boundary) = 0;
    v(grid.outer_boundary) = 0;
    w(grid.outer_boundary) = 0;
end

function visualize_results(grid, results, material, bc, params)
% Comprehensive visualization of thermoelastic results

    fprintf('  Generating comprehensive visualization...\n');

    figure('Position', [100, 100, 1600, 1200], 'Name', 'Thermoelastic Analysis: Circular Hole');

    % Final time step results
    T_final = results.T(:,:,:,end) - 273.15;  % Convert to Celsius
    u_final = results.u(:,:,:,end) * 1000;    % Convert to mm
    v_final = results.v(:,:,:,end) * 1000;    % Convert to mm
    w_final = results.w(:,:,:,end) * 1000;    % Convert to mm
    time_hours = results.time / 3600;

    % 1. Temperature distribution at mid-depth
    subplot(3,4,1);
    mid_z = round(params.nz/2);
    T_slice = squeeze(T_final(:,:,mid_z));
    T_slice(grid.hole_mask(:,:,mid_z)) = NaN;  % Mark hole

    contourf(grid.x, grid.y, T_slice, 20, 'LineStyle', 'none');
    colorbar; title('Temperature at Mid-Depth (°C)');
    xlabel('x [m]'); ylabel('y [m]');

    % Mark hole boundary
    hold on;
    theta = linspace(0, 2*pi, 100);
    hole_x = 2.0 + 0.08 * cos(theta);
    hole_y = 2.0 + 0.08 * sin(theta);
    plot(hole_x, hole_y, 'k-', 'LineWidth', 2);

    % 2. Temperature evolution
    subplot(3,4,2);
    plot(time_hours, results.T_avg, 'b-', 'LineWidth', 2);
    xlabel('Time [hours]'); ylabel('Average Temperature [°C]');
    title('Temperature Evolution');
    set(gca, 'XGrid', 'on', 'YGrid', 'on');

    % 3. Displacement magnitude at mid-depth
    subplot(3,4,3);
    disp_magnitude = sqrt(u_final.^2 + v_final.^2 + w_final.^2);
    disp_slice = squeeze(disp_magnitude(:,:,mid_z));
    disp_slice(grid.hole_mask(:,:,mid_z)) = NaN;

    contourf(grid.x, grid.y, disp_slice, 20, 'LineStyle', 'none');
    colorbar; title('Displacement Magnitude at Mid-Depth (mm)');
    xlabel('x [m]'); ylabel('y [m]');

    % 4. Maximum displacement evolution
    subplot(3,4,4);
    plot(time_hours, results.max_displacement, 'r-', 'LineWidth', 2);
    xlabel('Time [hours]'); ylabel('Max Displacement [mm]');
    title('Maximum Displacement Evolution');
    set(gca, 'XGrid', 'on', 'YGrid', 'on');

    % 5. Radial temperature profile
    subplot(3,4,5);
    mid_z = round(params.nz/2);
    center_j = round(params.ny/2);
    radial_distances = [];
    radial_temperatures = [];

    for i = 1:params.nx
        if grid.medium_mask(center_j, i, mid_z)
            r = abs(grid.x(i) - 2.0);
            if r > 0.08  % Outside hole
                radial_distances = [radial_distances, r];
                radial_temperatures = [radial_temperatures, T_final(center_j, i, mid_z)];
            end
        end
    end

    if ~isempty(radial_distances)
        [radial_distances, idx] = sort(radial_distances);
        radial_temperatures = radial_temperatures(idx);
        plot(radial_distances, radial_temperatures, 'g-', 'LineWidth', 2);
        xlabel('Radial Distance from Hole [m]'); ylabel('Temperature [°C]');
        title('Radial Temperature Profile');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
    end

    % 6. Displacement vectors (quiver plot)
    subplot(3,4,6);
    skip = 3;  % Skip points for clarity
    [X_sub, Y_sub] = meshgrid(grid.x(1:skip:end), grid.y(1:skip:end));
    u_sub = u_final(1:skip:end, 1:skip:end, mid_z);
    v_sub = v_final(1:skip:end, 1:skip:end, mid_z);

    quiver(X_sub, Y_sub, u_sub, v_sub, 2, 'b');
    xlabel('x [m]'); ylabel('y [m]');
    title('Displacement Vectors (mm)');
    axis equal; axis tight;

    % Mark hole
    hold on;
    plot(hole_x, hole_y, 'k-', 'LineWidth', 2);

    % 7. Temperature along depth (at hole boundary)
    subplot(3,4,7);
    hole_boundary_temps = [];
    depths = [];

    for k = 1:params.nz
        boundary_points = find(grid.hole_boundary(:,:,k));
        if ~isempty(boundary_points)
            [j_idx, i_idx] = ind2sub([params.ny, params.nx], boundary_points);
            temp_at_boundary = T_final(sub2ind(size(T_final), j_idx, i_idx, k*ones(size(j_idx))));
            hole_boundary_temps = [hole_boundary_temps; mean(temp_at_boundary)];
            depths = [depths; grid.z(k)];
        end
    end

    if ~isempty(depths)
        plot(hole_boundary_temps, depths, 'r-', 'LineWidth', 2);
        xlabel('Temperature [°C]'); ylabel('Depth [m]');
        title('Temperature Along Hole Boundary');
        set(gca, 'XGrid', 'on', 'YGrid', 'on');
        set(gca, 'YDir', 'reverse');  % Depth increases downward
    end

    % 8. Stress visualization (simplified)
    subplot(3,4,8);
    % Approximate thermal stress
    thermal_stress = material.E * material.alpha_T * (T_final - (bc.T_outer - 273.15)) / (1 - material.nu);
    stress_slice = squeeze(thermal_stress(:,:,mid_z));
    stress_slice(grid.hole_mask(:,:,mid_z)) = NaN;

    contourf(grid.x, grid.y, stress_slice/1e6, 20, 'LineStyle', 'none');  % Convert to MPa
    colorbar; title('Thermal Stress at Mid-Depth (MPa)');
    xlabel('x [m]'); ylabel('y [m]');

    % 9. 3D temperature isosurface
    subplot(3,4,9);
    T_3d = results.T(:,:,:,end) - 273.15;
    T_3d(grid.hole_mask) = NaN;

    % Create isosurface at intermediate temperature
    iso_temp = (max(T_3d(:)) + min(T_3d(:))) / 2;
    if ~isnan(iso_temp)
        try
            isosurface(grid.X, grid.Y, grid.Z, T_3d, iso_temp);
            xlabel('x [m]'); ylabel('y [m]'); zlabel('z [m]');
            title(sprintf('Temperature Isosurface (%.1f°C)', iso_temp));
            view(45, 30);
        catch
            text(0.5, 0.5, 'Isosurface generation failed', 'HorizontalAlignment', 'center');
            title('3D Temperature Visualization');
        end
    end

    % 10. Performance summary
    subplot(3,4,10);
    axis off;

    % Calculate performance metrics
    final_T_avg = results.T_avg(end);
    final_max_disp = results.max_displacement(end);
    max_thermal_stress = max(thermal_stress(grid.medium_mask)) / 1e6;  % MPa

    % Display summary
    text(0.1, 0.9, 'THERMOELASTIC PERFORMANCE', 'FontSize', 12, 'FontWeight', 'bold');
    text(0.1, 0.8, sprintf('Grid: %d × %d × %d', params.nx, params.ny, params.nz), 'FontSize', 10);
    text(0.1, 0.7, sprintf('Simulation Time: %.1f hours', time_hours(end)), 'FontSize', 10);
    text(0.1, 0.6, sprintf('Final Avg Temperature: %.1f°C', final_T_avg), 'FontSize', 10);
    text(0.1, 0.5, sprintf('Max Displacement: %.2f mm', final_max_disp), 'FontSize', 10);
    text(0.1, 0.4, sprintf('Max Thermal Stress: %.1f MPa', max_thermal_stress), 'FontSize', 10);
    text(0.1, 0.3, sprintf('Heat Flux: %.0f W/m²', bc.q_hole), 'FontSize', 10);
    text(0.1, 0.2, sprintf('Material: E = %.0f GPa', material.E/1e9), 'FontSize', 10);
    text(0.1, 0.1, sprintf('Thermal Expansion: %.0e /K', material.alpha_T), 'FontSize', 10);

    % 11. Hole region visualization
    subplot(3,4,11);
    hole_slice = grid.hole_mask(:,:,mid_z);
    medium_slice = grid.medium_mask(:,:,mid_z);

    imagesc(grid.x, grid.y, double(medium_slice));
    colormap(gca, [0.8 0.8 0.8; 0.2 0.2 0.8]);  % Gray for medium, blue for hole
    xlabel('x [m]'); ylabel('y [m]');
    title('Domain Layout (Mid-Depth)');
    axis equal; axis tight;

    % 12. Time evolution comparison
    subplot(3,4,12);
    yyaxis left;
    plot(time_hours, results.T_avg, 'b-', 'LineWidth', 2);
    ylabel('Temperature [°C]', 'Color', 'b');

    yyaxis right;
    plot(time_hours, results.max_displacement, 'r-', 'LineWidth', 2);
    ylabel('Displacement [mm]', 'Color', 'r');

    xlabel('Time [hours]');
    title('Coupled Response Evolution');
    set(gca, 'XGrid', 'on');

    sgtitle('Thermoelastic Analysis: Medium with Circular Hole', 'FontSize', 14, 'FontWeight', 'bold');

    % Save visualization
    saveas(gcf, 'thermoelastic_circular_hole_results.png');
    fprintf('  Visualization completed and saved!\n');
end

function generate_vtk_output(grid, results, material, bc, params)
% Generate VTK files for ParaView visualization

    fprintf('  Creating VTK output directory...\n');
    vtk_dir = 'vtk_output';
    if ~exist(vtk_dir, 'dir')
        mkdir(vtk_dir);
    end

    % Time steps to output (every 10th step to reduce file size)
    output_steps = 1:10:params.nt;
    if output_steps(end) ~= params.nt
        output_steps = [output_steps, params.nt];  % Always include final step
    end

    fprintf('  Generating VTK files for %d time steps...\n', length(output_steps));

    % Create structured grid coordinates
    [X, Y, Z] = meshgrid(grid.x, grid.y, grid.z);

    for step_idx = 1:length(output_steps)
        n = output_steps(step_idx);
        time = results.time(n);

        % Extract fields at current time step
        T = results.T(:,:,:,n);
        u = results.u(:,:,:,n);
        v = results.v(:,:,:,n);
        w = results.w(:,:,:,n);

        % Create VTK filename
        vtk_filename = sprintf('%s/thermoelastic_step_%04d.vtk', vtk_dir, n);

        % Write VTK file
        write_vtk_structured_grid(vtk_filename, X, Y, Z, T, u, v, w, grid, time, material, bc);

        if mod(step_idx, 5) == 0 || step_idx == length(output_steps)
            fprintf('    Written %d/%d VTK files\n', step_idx, length(output_steps));
        end
    end

    % Create PVD file for time series
    create_pvd_file(vtk_dir, output_steps, results.time);

    fprintf('  VTK output completed: %d files in %s/\n', length(output_steps), vtk_dir);
    fprintf('  Open %s/thermoelastic_time_series.pvd in ParaView\n', vtk_dir);
end

function write_vtk_structured_grid(filename, X, Y, Z, T, u, v, w, grid, time, material, bc)
% Write VTK structured grid file

    [ny, nx, nz] = size(X);

    % Open file for writing
    fid = fopen(filename, 'w');
    if fid == -1
        error('Could not open file %s for writing', filename);
    end

    try
        % VTK header
        fprintf(fid, '# vtk DataFile Version 3.0\n');
        fprintf(fid, 'Thermoelastic Analysis: Circular Hole (t=%.1f hours)\n', time/3600);
        fprintf(fid, 'ASCII\n');
        fprintf(fid, 'DATASET STRUCTURED_GRID\n');
        fprintf(fid, 'DIMENSIONS %d %d %d\n', nx, ny, nz);
        fprintf(fid, 'POINTS %d float\n', nx*ny*nz);

        % Write grid points
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    fprintf(fid, '%.6f %.6f %.6f\n', X(j,i,k), Y(j,i,k), Z(j,i,k));
                end
            end
        end

        % Point data
        fprintf(fid, 'POINT_DATA %d\n', nx*ny*nz);

        % Temperature field
        fprintf(fid, 'SCALARS Temperature float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    if grid.medium_mask(j,i,k)
                        temp_celsius = T(j,i,k) - 273.15;
                    else
                        temp_celsius = NaN;  % Hole region
                    end
                    fprintf(fid, '%.6f\n', temp_celsius);
                end
            end
        end

        % Displacement vector field
        fprintf(fid, 'VECTORS Displacement float\n');
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    if grid.medium_mask(j,i,k)
                        fprintf(fid, '%.6f %.6f %.6f\n', u(j,i,k), v(j,i,k), w(j,i,k));
                    else
                        fprintf(fid, '0.0 0.0 0.0\n');  % Zero displacement in hole
                    end
                end
            end
        end

        % Displacement magnitude
        fprintf(fid, 'SCALARS Displacement_Magnitude float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    if grid.medium_mask(j,i,k)
                        disp_mag = sqrt(u(j,i,k)^2 + v(j,i,k)^2 + w(j,i,k)^2) * 1000;  % mm
                    else
                        disp_mag = 0.0;
                    end
                    fprintf(fid, '%.6f\n', disp_mag);
                end
            end
        end

        % Thermal stress (approximate)
        fprintf(fid, 'SCALARS Thermal_Stress float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    if grid.medium_mask(j,i,k)
                        dT = T(j,i,k) - material.T_ref;
                        thermal_stress = material.E * material.alpha_T * dT / (1 - material.nu) / 1e6;  % MPa
                    else
                        thermal_stress = 0.0;
                    end
                    fprintf(fid, '%.6f\n', thermal_stress);
                end
            end
        end

        % Material region indicator
        fprintf(fid, 'SCALARS Material_Region float 1\n');
        fprintf(fid, 'LOOKUP_TABLE default\n');
        for k = 1:nz
            for j = 1:ny
                for i = 1:nx
                    if grid.medium_mask(j,i,k)
                        region = 1.0;  % Medium
                    else
                        region = 0.0;  % Hole
                    end
                    fprintf(fid, '%.1f\n', region);
                end
            end
        end

    catch ME
        fclose(fid);
        rethrow(ME);
    end

    fclose(fid);
end

function create_pvd_file(vtk_dir, output_steps, time_array)
% Create PVD file for time series visualization in ParaView

    pvd_filename = sprintf('%s/thermoelastic_time_series.pvd', vtk_dir);

    fid = fopen(pvd_filename, 'w');
    if fid == -1
        error('Could not create PVD file %s', pvd_filename);
    end

    try
        % PVD header
        fprintf(fid, '<?xml version="1.0"?>\n');
        fprintf(fid, '<VTKFile type="Collection" version="0.1" byte_order="LittleEndian">\n');
        fprintf(fid, '  <Collection>\n');

        % Add each time step
        for i = 1:length(output_steps)
            n = output_steps(i);
            time_hours = time_array(n) / 3600;
            vtk_file = sprintf('thermoelastic_step_%04d.vtk', n);

            fprintf(fid, '    <DataSet timestep="%.6f" group="" part="0" file="%s"/>\n', ...
                time_hours, vtk_file);
        end

        % PVD footer
        fprintf(fid, '  </Collection>\n');
        fprintf(fid, '</VTKFile>\n');

    catch ME
        fclose(fid);
        rethrow(ME);
    end

    fclose(fid);

    fprintf('  Created PVD time series file: %s\n', pvd_filename);
end
