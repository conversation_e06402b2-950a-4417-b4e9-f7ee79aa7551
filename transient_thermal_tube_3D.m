function transient_thermal_tube_3D()
% 3D Transient Thermal Analysis of Fluid Flow in a Tube
% Considers both convection and conduction heat transfer
% Author: Generated for Geothermal Pile Analysis
% Date: 2025-07-03

clear; clc; close all;

%% Physical Parameters
% Tube geometry
L = 2.0;           % Length of tube [m]
R = 0.05;          % Radius of tube [m]
wall_thickness = 0.005; % Wall thickness [m]

% Fluid properties (water)
rho_f = 1000;      % Density [kg/m³]
cp_f = 4180;       % Specific heat [J/kg·K]
k_f = 0.6;         % Thermal conductivity [W/m·K]
mu = 0.001;        % Dynamic viscosity [Pa·s]
v_inlet = 0.1;     % Inlet velocity [m/s]

% Tube wall properties (steel)
rho_w = 7850;      % Density [kg/m³]
cp_w = 460;        % Specific heat [J/kg·K]
k_w = 50;          % Thermal conductivity [W/m·K]

% Boundary conditions
T_inlet = 353;     % Inlet temperature [K] (80°C)
T_wall_outer = 293; % Outer wall temperature [K] (20°C)
T_initial = 293;   % Initial temperature [K]

%% Numerical Parameters
nr = 20;           % Number of radial nodes
nz = 50;           % Number of axial nodes
nt = 1000;         % Number of time steps
dt = 0.1;          % Time step [s]
total_time = nt * dt;

% Grid generation
dr = R / (nr - 1);
dz = L / (nz - 1);
r = linspace(0, R, nr);
z = linspace(0, L, nz);
[R_grid, Z_grid] = meshgrid(r, z);

%% Initialize temperature field
T = T_initial * ones(nz, nr, nt);
T(:, :, 1) = T_initial; % Initial condition

% Velocity field (fully developed parabolic profile)
u = zeros(nz, nr);
for i = 1:nr
    u(:, i) = 2 * v_inlet * (1 - (r(i)/R)^2); % Parabolic velocity profile
end

%% Material properties arrays
alpha_f = k_f / (rho_f * cp_f); % Thermal diffusivity of fluid

fprintf('Starting 3D transient thermal analysis...\n');
fprintf('Grid: %d x %d, Time steps: %d\n', nz, nr, nt);
fprintf('Total simulation time: %.1f seconds\n', total_time);

%% Time stepping loop
for t = 2:nt
    T_old = T(:, :, t-1);
    T_new = T_old;
    
    % Interior nodes (fluid region)
    for i = 2:nz-1  % Axial direction
        for j = 2:nr-1  % Radial direction
            
            % Conduction terms
            d2T_dr2 = (T_old(i, j+1) - 2*T_old(i, j) + T_old(i, j-1)) / dr^2;
            dT_dr = (T_old(i, j+1) - T_old(i, j-1)) / (2*dr);
            d2T_dz2 = (T_old(i+1, j) - 2*T_old(i, j) + T_old(i-1, j)) / dz^2;
            
            % Convection term
            dT_dz = (T_old(i+1, j) - T_old(i-1, j)) / (2*dz);
            
            % Heat equation with convection and conduction
            dT_dt = alpha_f * (d2T_dr2 + (1/r(j)) * dT_dr + d2T_dz2) - u(i, j) * dT_dz;
            
            T_new(i, j) = T_old(i, j) + dt * dT_dt;
        end
    end
    
    % Boundary conditions
    % Inlet boundary (z = 0)
    T_new(1, :) = T_inlet;
    
    % Outlet boundary (z = L) - zero gradient
    T_new(nz, :) = T_new(nz-1, :);
    
    % Centerline boundary (r = 0) - symmetry
    T_new(:, 1) = T_new(:, 2);
    
    % Wall boundary (r = R) - convective heat transfer
    h = calculate_heat_transfer_coefficient(v_inlet, R, k_f, mu, rho_f, cp_f);
    for i = 1:nz
        % Newton's law of cooling at the wall
        T_new(i, nr) = (h * T_wall_outer + (k_f/dr) * T_new(i, nr-1)) / (h + k_f/dr);
    end
    
    T(:, :, t) = T_new;
    
    % Progress indicator
    if mod(t, 100) == 0
        fprintf('Time step %d/%d completed (%.1f%%)\n', t, nt, 100*t/nt);
    end
end

fprintf('Analysis completed!\n');

%% Post-processing and visualization
visualize_results(T, R_grid, Z_grid, r, z, dt, nt);

end

function h = calculate_heat_transfer_coefficient(v, D, k, mu, rho, cp)
% Calculate convective heat transfer coefficient using Nusselt number correlations
    Re = rho * v * D / mu;  % Reynolds number
    Pr = mu * cp / k;       % Prandtl number
    
    % Dittus-Boelter correlation for turbulent flow
    if Re > 2300
        Nu = 0.023 * Re^0.8 * Pr^0.4;
    else
        % Laminar flow
        Nu = 3.66; % Constant Nusselt number for fully developed laminar flow
    end
    
    h = Nu * k / D;
end

function visualize_results(T, R_grid, Z_grid, r, z, dt, nt)
% Visualization function for results
    
    figure('Position', [100, 100, 1200, 800]);
    
    % Temperature contours at final time
    subplot(2, 3, 1);
    contourf(Z_grid, R_grid, T(:, :, end), 20);
    colorbar;
    title('Temperature Distribution at Final Time');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');
    
    % Temperature evolution at centerline
    subplot(2, 3, 2);
    time_vec = (0:nt-1) * dt;
    plot(time_vec, squeeze(T(end/2, 1, :)), 'b-', 'LineWidth', 2);
    grid on;
    title('Centerline Temperature Evolution');
    xlabel('Time [s]');
    ylabel('Temperature [K]');
    
    % Radial temperature profile at different times
    subplot(2, 3, 3);
    time_indices = [1, nt/4, nt/2, 3*nt/4, nt];
    colors = ['b', 'g', 'r', 'c', 'm'];
    hold on;
    for i = 1:length(time_indices)
        idx = round(time_indices(i));
        plot(r, T(end/2, :, idx), [colors(i), '-'], 'LineWidth', 2);
    end
    grid on;
    title('Radial Temperature Profiles');
    xlabel('Radial Position [m]');
    ylabel('Temperature [K]');
    legend('t=0', 't=T/4', 't=T/2', 't=3T/4', 't=T', 'Location', 'best');
    
    % 3D surface plot
    subplot(2, 3, [4, 5, 6]);
    surf(Z_grid, R_grid, T(:, :, end));
    colorbar;
    title('3D Temperature Distribution');
    xlabel('Axial Position [m]');
    ylabel('Radial Position [m]');
    zlabel('Temperature [K]');
    view(45, 30);
    
    % Save results
    save('thermal_analysis_results.mat', 'T', 'R_grid', 'Z_grid', 'r', 'z', 'dt');
    
    fprintf('Results saved to thermal_analysis_results.mat\n');
    fprintf('Visualization completed!\n');
end
